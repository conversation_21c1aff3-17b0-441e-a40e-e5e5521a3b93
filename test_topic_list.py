#!/usr/bin/env python3
"""
Test script for TopicListPanel functionality
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_topic_list_panel():
    """Test TopicListPanel functionality"""
    print("Testing TopicListPanel...")

    try:
        from utils.config import Config
        from utils.theme import ThemeManager
        from gui.topic_list import TopicListPanel

        # Create test window
        root = tk.Tk()
        root.title("Topic List Test")
        root.geometry("800x600")

        # Initialize components
        config = Config()
        theme = ThemeManager(root)

        # Create main frame
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Callback function for topic selection
        def on_topic_selected(topic_data):
            print(f"Topic selected: {topic_data.get('title', 'Unknown')}")
            print(f"Description: {topic_data.get('description', 'No description')[:100]}...")

        # Create TopicListPanel
        topic_list = TopicListPanel(main_frame, config, theme, on_topic_selected)

        # Test basic functionality
        assert hasattr(topic_list, 'add_topic_dialog'), "TopicListPanel missing add_topic_dialog"
        assert hasattr(topic_list, 'get_topic_count'), "TopicListPanel missing get_topic_count"
        assert hasattr(topic_list, 'load_topic_library'), "TopicListPanel missing load_topic_library"
        assert hasattr(topic_list, 'topics_treeview'), "TopicListPanel missing topics_treeview"

        print("✓ TopicListPanel initialized successfully")
        print(f"✓ Topic library loaded with {topic_list.get_topic_count()} topics")

        # Add instructions
        instructions = ttk.Label(
            main_frame,
            text="Instructions:\n" +
                 "• Click '➕ Add Topic' to add a new topic to the library\n" +
                 "• Double-click a topic to select it\n" +
                 "• Right-click for context menu options\n" +
                 "• Use the search box to filter topics\n" +
                 "• Close this window when done testing",
            justify=tk.LEFT,
            background="lightyellow",
            padding=10
        )
        instructions.pack(fill=tk.X, pady=(10, 0))

        print("✓ Test window created - interact with the GUI to test functionality")

        # Run the GUI
        root.mainloop()

        return True

    except Exception as e:
        print(f"✗ TopicListPanel test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    print("TopicListPanel Test")
    print("=" * 30)

    if test_topic_list_panel():
        print("\n✓ TopicListPanel test completed successfully")
        return 0
    else:
        print("\n✗ TopicListPanel test failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())