"""
Enhanced widgets with responsive behavior for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict
from utils.logging_setup import LoggerMixin


class ResponsiveText(tk.Text, LoggerMixin):
    """Text widget with responsive features and multi-column support"""
    
    def __init__(self, parent, layout_manager=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.layout_manager = layout_manager
        self.columns = 1
        self.original_content = ""
        self.auto_resize = True
        self.min_width_chars = 40
        self.max_columns = 3
        
        if layout_manager:
            layout_manager.register_layout_callback(
                f"text_{id(self)}", self.update_responsive_layout
            )
        
        self.bind('<Configure>', self.on_resize)
        
    def set_content(self, content: str, auto_format: bool = True):
        """Set content with automatic formatting"""
        self.original_content = content
        if auto_format:
            self.format_content()
        else:
            self.delete('1.0', tk.END)
            self.insert('1.0', content)
    
    def update_responsive_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update layout based on screen category"""
        if category == 'ultra_wide':
            self.columns = min(self.max_columns, 3)
        elif category == 'large':
            self.columns = min(self.max_columns, 2)
        else:
            self.columns = 1
            
        self.format_content()
    
    def on_resize(self, event=None):
        """Handle widget resize"""
        if event and event.widget == self and self.auto_resize:
            self.after_idle(self.update_columns_for_width)
    
    def update_columns_for_width(self):
        """Update column count based on widget width"""
        try:
            width = self.winfo_width()
            font = self.cget('font')
            
            if font and width > 1:
                char_width = self.tk.call('font', 'measure', font, '0')
                if char_width > 0:
                    chars_available = width // char_width
                    optimal_columns = max(1, min(
                        self.max_columns,
                        chars_available // self.min_width_chars
                    ))
                    
                    if optimal_columns != self.columns:
                        self.columns = optimal_columns
                        self.format_content()
                        
        except (tk.TclError, ZeroDivisionError):
            pass
    
    def format_content(self):
        """Format content for current column configuration"""
        if not self.original_content:
            return
            
        if self.columns <= 1:
            self.delete('1.0', tk.END)
            self.insert('1.0', self.original_content)
            return
        
        # Multi-column formatting
        formatted = self._format_multicolumn(self.original_content, self.columns)
        self.delete('1.0', tk.END)
        self.insert('1.0', formatted)
    
    def _format_multicolumn(self, text: str, columns: int) -> str:
        """Format text into multiple columns"""
        if columns <= 1:
            return text
            
        # Split into paragraphs
        paragraphs = text.split('\n\n')
        
        # Calculate column width
        try:
            widget_width = self.winfo_width()
            font = self.cget('font')
            char_width = self.tk.call('font', 'measure', font, '0')
            
            if char_width > 0:
                column_width = max(20, (widget_width // columns) // char_width - 3)
            else:
                column_width = 40
        except (tk.TclError, ZeroDivisionError):
            column_width = 40
        
        # Distribute paragraphs across columns
        column_content = [[] for _ in range(columns)]
        current_column = 0
        
        for paragraph in paragraphs:
            wrapped = self._wrap_text(paragraph, column_width)
            column_content[current_column].append(wrapped)
            current_column = (current_column + 1) % columns
        
        # Format columns side by side
        formatted_lines = []
        max_lines = max(len('\n'.join(col).split('\n')) for col in column_content)
        
        for line_idx in range(max_lines):
            line_parts = []
            for col_idx in range(columns):
                col_text = '\n'.join(column_content[col_idx])
                col_lines = col_text.split('\n')
                
                if line_idx < len(col_lines):
                    line_text = col_lines[line_idx][:column_width]
                    line_parts.append(line_text.ljust(column_width))
                else:
                    line_parts.append(' ' * column_width)
            
            formatted_lines.append(' │ '.join(line_parts))
        
        return '\n'.join(formatted_lines)
    
    def _wrap_text(self, text: str, width: int) -> str:
        """Wrap text to specified width"""
        words = text.split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            if current_length + len(word) + 1 <= width:
                current_line.append(word)
                current_length += len(word) + 1
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]
                current_length = len(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return '\n'.join(lines)


class AdaptiveNotebook(ttk.Notebook, LoggerMixin):
    """Notebook widget that adapts tab layout based on screen size"""
    
    def __init__(self, parent, layout_manager=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.layout_manager = layout_manager
        self.tab_overflow_menu = None
        self.max_visible_tabs = 8
        
        if layout_manager:
            layout_manager.register_layout_callback(
                f"notebook_{id(self)}", self.update_tab_layout
            )
    
    def update_tab_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update tab layout based on screen category"""
        if category == 'small':
            self.max_visible_tabs = 4
        elif category == 'medium':
            self.max_visible_tabs = 6
        else:
            self.max_visible_tabs = 8
            
        self.manage_tab_overflow()
    
    def manage_tab_overflow(self):
        """Handle tab overflow by creating a dropdown menu"""
        tab_count = self.index('end')
        
        if tab_count > self.max_visible_tabs:
            # Hide overflow tabs and create menu
            self.create_overflow_menu()
        else:
            # Show all tabs and remove menu
            self.remove_overflow_menu()
    
    def create_overflow_menu(self):
        """Create overflow menu for hidden tabs"""
        # Implementation would create a menu button for overflow tabs
        pass
    
    def remove_overflow_menu(self):
        """Remove overflow menu when not needed"""
        if self.tab_overflow_menu:
            self.tab_overflow_menu.destroy()
            self.tab_overflow_menu = None


class CollapsibleSection(ttk.Frame, LoggerMixin):
    """A collapsible section that can expand/collapse to save space"""
    
    def __init__(self, parent, title: str, collapsed: bool = False, **kwargs):
        super().__init__(parent, **kwargs)
        self.title = title
        self.collapsed = collapsed
        self.content_frame: ttk.Frame = ttk.Frame(self)  # Initialize immediately
        self.toggle_button = None  # Will be set in setup_ui
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the collapsible section UI"""
        # Header frame with toggle button
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X)
        
        # Toggle button
        self.toggle_button = ttk.Button(
            header_frame,
            text=self.get_toggle_text(),
            command=self.toggle_collapsed,
            width=len(self.title) + 4
        )
        self.toggle_button.pack(side=tk.LEFT)
        
        # Content frame
        self.content_frame = ttk.Frame(self)
        if not self.collapsed:
            self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
    
    def get_toggle_text(self) -> str:
        """Get text for toggle button"""
        icon = "▼" if not self.collapsed else "▶"
        return f"{icon} {self.title}"
    
    def toggle_collapsed(self):
        """Toggle the collapsed state"""
        self.collapsed = not self.collapsed
        if self.toggle_button:
            self.toggle_button.configure(text=self.get_toggle_text())
        
        if self.collapsed:
            self.content_frame.pack_forget()
        else:
            self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
    
    def get_content_frame(self) -> ttk.Frame:
        """Get the content frame for adding widgets"""
        return self.content_frame


class ResponsiveStatusBar(ttk.Frame, LoggerMixin):
    """Status bar that adapts its layout based on screen size"""
    
    def __init__(self, parent, layout_manager=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.layout_manager = layout_manager
        self.status_var = tk.StringVar(value="Ready")
        self.progress_var = tk.StringVar()
        self.connection_var = tk.StringVar(value="●")  # Connection indicator
        
        self.status_label = None
        self.progress_label = None
        self.connection_label = None
        
        self.setup_ui()
        
        if layout_manager:
            layout_manager.register_layout_callback(
                f"statusbar_{id(self)}", self.update_layout
            )
    
    def setup_ui(self):
        """Set up status bar UI"""
        # Left side - Main status
        self.status_label = ttk.Label(self, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT)
        
        # Right side - Progress and connection
        right_frame = ttk.Frame(self)
        right_frame.pack(side=tk.RIGHT)
        
        self.connection_label = ttk.Label(right_frame, textvariable=self.connection_var)
        self.connection_label.pack(side=tk.RIGHT, padx=(5, 0))
        
        self.progress_label = ttk.Label(right_frame, textvariable=self.progress_var)
        self.progress_label.pack(side=tk.RIGHT, padx=(5, 0))
    
    def update_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update layout based on screen size"""
        if category == 'small':
            # Minimal status for small screens
            self.configure_labels(status_width=20, progress_width=10)
        elif category == 'medium':
            # Moderate status for medium screens
            self.configure_labels(status_width=40, progress_width=15)
        else:
            # Full status for large screens
            self.configure_labels(status_width=60, progress_width=25)
    
    def configure_labels(self, status_width: int, progress_width: int):
        """Configure label widths"""
        # This would normally configure the actual width, but Tkinter labels
        # don't have a width parameter by default. This is a placeholder.
        pass
    
    def set_status(self, status: str):
        """Set the main status text"""
        self.status_var.set(status)
    
    def set_progress(self, progress: str):
        """Set the progress text"""
        self.progress_var.set(progress)
    
    def set_connection_status(self, connected: bool):
        """Set connection status indicator"""
        self.connection_var.set("●" if connected else "○")


class SearchableCombobox(ttk.Combobox):
    """Combobox with search functionality"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.all_values = []
        self.bind('<KeyRelease>', self.on_key_release)
        self.bind('<FocusIn>', self.on_focus_in)
    
    def set_values(self, values: list):
        """Set all available values"""
        self.all_values = values
        self['values'] = values
    
    def on_key_release(self, event):
        """Handle key release for search functionality"""
        if event.keysym in ['Up', 'Down', 'Left', 'Right', 'Tab']:
            return
            
        current_text = self.get().lower()
        
        if not current_text:
            self['values'] = self.all_values
        else:
            # Filter values based on current text
            filtered = [item for item in self.all_values 
                       if current_text in item.lower()]
            self['values'] = filtered
            
        # Show dropdown if there are filtered results
        if filtered:
            self.event_generate('<Button-1>')
    
    def on_focus_in(self, event):
        """Handle focus in to show all values"""
        self['values'] = self.all_values


class ResponsiveTreeview(ttk.Treeview):
    """Treeview that adapts column widths based on screen size"""
    
    def __init__(self, parent, layout_manager=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.layout_manager = layout_manager
        self.base_column_widths = {}
        
        if layout_manager:
            layout_manager.register_layout_callback(
                f"tree_{id(self)}", self.update_column_layout
            )
        
        self.bind('<Configure>', self.on_resize)
    
    def set_base_column_widths(self, widths: Dict[str, int]):
        """Set base column widths for responsive scaling"""
        self.base_column_widths = widths
    
    def update_column_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update column layout based on screen category"""
        scale_factors = {
            'small': 0.8,
            'medium': 1.0,
            'large': 1.2,
            'ultra_wide': 1.4
        }
        
        scale = scale_factors.get(category, 1.0)
        
        for column, base_width in self.base_column_widths.items():
            try:
                new_width = int(base_width * scale)
                self.column(column, width=new_width)
            except tk.TclError:
                pass
    
    def on_resize(self, event=None):
        """Handle resize events"""
        if event and event.widget == self:
            self.after_idle(self.adjust_columns_to_width)
    
    def adjust_columns_to_width(self):
        """Adjust column widths to fit available space"""
        try:
            total_width = self.winfo_width()
            columns = self['columns']
            
            if columns and total_width > 1:
                # Distribute width among columns
                column_width = max(50, total_width // len(columns))
                
                for column in columns:
                    self.column(column, width=column_width)
                    
        except (tk.TclError, ZeroDivisionError):
            pass
