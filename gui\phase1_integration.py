"""
Phase 1 Core Layout Improvements Integration
Integrates all Phase 1 components into the main application
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, List
from utils.logging_setup import LoggerMixin

# Import Phase 1 components
from gui.advanced_responsive_layout import ResponsiveLayoutManager
from gui.enhanced_text_widget import EnhancedTextWidget
from gui.advanced_content_management import AdvancedContentManager
try:
    from gui.high_performance_scrolling import OptimizedScrolling
    PERFORMANCE_AVAILABLE = True
except ImportError:
    PERFORMANCE_AVAILABLE = False


class Phase1IntegrationManager(LoggerMixin):
    """Manages integration of all Phase 1 improvements."""
    
    def __init__(self, main_window: tk.Tk):
        super().__init__()
        self.main_window = main_window
        
        # Initialize Phase 1 components
        self.responsive_layout = ResponsiveLayoutManager(main_window)
        self.content_manager = AdvancedContentManager()
        
        # Optional scrolling manager
        self.scrolling_manager = None
        if PERFORMANCE_AVAILABLE:
            try:
                self.scrolling_manager = OptimizedScrolling(main_window)
            except Exception as e:
                self.logger.warning(f"Could not initialize scrolling manager: {e}")
        
        # Integration state
        self.integrated_panels = {}
        self.enhanced_widgets = {}
        self.performance_enabled = False
        
        self.logger.info("Phase 1 Integration Manager initialized")
        
    def integrate_all_improvements(self) -> bool:
        """Integrate all Phase 1 improvements into the application."""
        try:
            self.logger.info("Starting Phase 1 integration...")
            
            # 1. Enable high-performance scrolling
            self.enable_performance_improvements()
            
            # 2. Setup responsive layout system
            self.setup_responsive_layout()
            
            # 3. Integrate advanced content management
            self.integrate_content_management()
            
            # 4. Enhance existing widgets
            self.enhance_existing_widgets()
            
            # 5. Apply initial layout optimization
            self.optimize_initial_layout()
            
            self.logger.info("Phase 1 integration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Phase 1 integration failed: {e}")
            return False
            
    def enable_performance_improvements(self):
        """Enable high-performance scrolling for all scrollable widgets."""
        try:
            # Apply to main window
            self.scrolling_manager.apply_to_window(self.main_window)
            
            # Find and enhance all existing scrollable widgets
            scrollable_widgets = self.find_scrollable_widgets(self.main_window)
            
            for widget in scrollable_widgets:
                self.scrolling_manager.optimize_widget(widget)
                
            self.performance_enabled = True
            self.logger.info(f"Performance improvements applied to {len(scrollable_widgets)} widgets")
            
        except Exception as e:
            self.logger.error(f"Failed to enable performance improvements: {e}")
            
    def find_scrollable_widgets(self, parent: tk.Widget) -> List[tk.Widget]:
        """Find all scrollable widgets in the widget hierarchy."""
        scrollable_widgets = []
        
        def traverse_widgets(widget):
            # Check if widget has scrollbars or is scrollable
            if hasattr(widget, 'yview') or hasattr(widget, 'xview'):
                scrollable_widgets.append(widget)
                
            # Check for scrollbar widgets
            if isinstance(widget, (tk.Scrollbar, ttk.Scrollbar)):
                scrollable_widgets.append(widget)
                
            # Traverse children
            try:
                for child in widget.winfo_children():
                    traverse_widgets(child)
            except Exception:
                pass  # Skip widgets that can't be traversed
                
        traverse_widgets(parent)
        return scrollable_widgets
        
    def setup_responsive_layout(self):
        """Setup responsive layout system for the main window."""
        try:
            # Initialize responsive layout
            self.responsive_layout.initialize()
            
            # Apply to main window structure
            self.apply_responsive_to_main_structure()
            
            # Setup breakpoint handling
            self.setup_breakpoint_handling()
            
            self.logger.info("Responsive layout system setup completed")
            
        except Exception as e:
            self.logger.error(f"Failed to setup responsive layout: {e}")
            
    def apply_responsive_to_main_structure(self):
        """Apply responsive layout to main window structure."""
        try:
            # Find main panels/frames in the application
            main_panels = self.find_main_panels()
            
            for panel_id, panel_widget in main_panels.items():
                # Register panel with responsive layout
                self.responsive_layout.register_panel(panel_id, panel_widget)
                
                # Apply adaptive weights
                self.responsive_layout.apply_adaptive_weights(panel_id)
                
                self.integrated_panels[panel_id] = panel_widget
                
            self.logger.info(f"Responsive layout applied to {len(main_panels)} panels")
            
        except Exception as e:
            self.logger.error(f"Failed to apply responsive layout to main structure: {e}")
            
    def find_main_panels(self) -> Dict[str, tk.Widget]:
        """Find main panels in the application."""
        panels = {}
        
        def find_panels(widget, path=""):
            # Look for frames that might be main panels
            if isinstance(widget, (tk.Frame, ttk.Frame, tk.LabelFrame, ttk.LabelFrame)):
                # Use widget class name and position as ID
                widget_name = widget.__class__.__name__
                if hasattr(widget, 'master') and hasattr(widget.master, 'winfo_children'):
                    position = len([w for w in widget.master.winfo_children() 
                                  if w == widget or w.__class__ == widget.__class__])
                    panel_id = f"{widget_name}_{position}"
                else:
                    panel_id = f"{widget_name}_{len(panels)}"
                    
                panels[panel_id] = widget
                
            # Continue traversing
            try:
                for child in widget.winfo_children():
                    find_panels(child, f"{path}/{child.__class__.__name__}")
            except Exception:
                pass
                
        find_panels(self.main_window)
        return panels
        
    def setup_breakpoint_handling(self):
        """Setup responsive breakpoint handling."""
        def on_window_resize(event):
            if event.widget == self.main_window:
                try:
                    # Update responsive layout based on new size
                    window_width = self.main_window.winfo_width()
                    window_height = self.main_window.winfo_height()
                    
                    # Apply responsive adjustments
                    self.responsive_layout.handle_window_resize(window_width, window_height)
                    
                    # Trigger content reflow
                    self.trigger_content_reflow()
                    
                except Exception as e:
                    self.logger.error(f"Error handling window resize: {e}")
                    
        self.main_window.bind('<Configure>', on_window_resize)
        
    def integrate_content_management(self):
        """Integrate advanced content management system."""
        try:
            # Register all panels with content manager
            for panel_id, panel_widget in self.integrated_panels.items():
                self.content_manager.register_display_panel(panel_id, panel_widget)
                
            # Setup content optimization
            self.setup_content_optimization()
            
            self.logger.info("Content management system integrated")
            
        except Exception as e:
            self.logger.error(f"Failed to integrate content management: {e}")
            
    def setup_content_optimization(self):
        """Setup automatic content optimization."""
        # This will be called when content is added or window is resized
        def optimize_all_panels():
            for panel_id in self.integrated_panels.keys():
                try:
                    self.content_manager.optimize_layout(panel_id, 'balanced')
                except Exception as e:
                    self.logger.error(f"Failed to optimize panel {panel_id}: {e}")
                    
        # Store reference for later use
        self.optimize_content = optimize_all_panels
        
    def enhance_existing_widgets(self):
        """Enhance existing widgets with Phase 1 improvements."""
        try:
            # Find text widgets that can be enhanced
            text_widgets = self.find_text_widgets()
            
            for widget_id, widget in text_widgets.items():
                self.enhance_text_widget(widget_id, widget)
                
            self.logger.info(f"Enhanced {len(text_widgets)} text widgets")
            
        except Exception as e:
            self.logger.error(f"Failed to enhance existing widgets: {e}")
            
    def find_text_widgets(self) -> Dict[str, tk.Widget]:
        """Find text widgets that can be enhanced."""
        text_widgets = {}
        
        def find_text(widget, path=""):
            if isinstance(widget, (tk.Text, tk.Label)):
                widget_id = f"{widget.__class__.__name__}_{len(text_widgets)}"
                text_widgets[widget_id] = widget
                
            try:
                for child in widget.winfo_children():
                    find_text(child, f"{path}/{child.__class__.__name__}")
            except Exception:
                pass
                
        find_text(self.main_window)
        return text_widgets
        
    def enhance_text_widget(self, widget_id: str, widget: tk.Widget):
        """Enhance a specific text widget."""
        try:
            # Apply enhanced functionality where possible
            if isinstance(widget, tk.Text):
                # Apply performance optimizations
                self.scrolling_manager.optimize_widget(widget)
                
                # Store reference
                self.enhanced_widgets[widget_id] = widget
                
        except Exception as e:
            self.logger.error(f"Failed to enhance widget {widget_id}: {e}")
            
    def optimize_initial_layout(self):
        """Apply initial layout optimization."""
        try:
            # Get current window size and apply optimal layout
            self.main_window.update_idletasks()
            
            # Trigger responsive layout
            window_width = self.main_window.winfo_width()
            window_height = self.main_window.winfo_height()
            
            if window_width > 1 and window_height > 1:  # Valid dimensions
                self.responsive_layout.handle_window_resize(window_width, window_height)
                
            # Optimize content layout
            if hasattr(self, 'optimize_content'):
                self.optimize_content()
                
            self.logger.info("Initial layout optimization completed")
            
        except Exception as e:
            self.logger.error(f"Failed to optimize initial layout: {e}")
            
    def trigger_content_reflow(self):
        """Trigger content reflow for responsive adjustment."""
        try:
            if hasattr(self, 'optimize_content'):
                self.optimize_content()
        except Exception as e:
            self.logger.error(f"Error during content reflow: {e}")
            
    def add_content_with_management(self, panel_id: str, content: Any, 
                                  priority: str = 'normal', content_type: str = 'text',
                                  metadata: Optional[Dict] = None) -> bool:
        """Add content using advanced content management."""
        try:
            return self.content_manager.add_content(
                panel_id, content, priority, content_type, metadata
            )
        except Exception as e:
            self.logger.error(f"Failed to add content to {panel_id}: {e}")
            return False
            
    def create_enhanced_text_widget(self, parent: tk.Widget, **kwargs) -> EnhancedTextWidget:
        """Create an enhanced text widget with all improvements."""
        try:
            enhanced_widget = EnhancedTextWidget(parent, **kwargs)
            
            # Apply performance optimizations
            self.scrolling_manager.optimize_widget(enhanced_widget)
            
            return enhanced_widget
            
        except Exception as e:
            self.logger.error(f"Failed to create enhanced text widget: {e}")
            # Fallback to regular text widget
            return tk.Text(parent, **kwargs)
            
    def get_integration_status(self) -> Dict[str, Any]:
        """Get status of Phase 1 integration."""
        return {
            'performance_enabled': self.performance_enabled,
            'responsive_panels': len(self.integrated_panels),
            'enhanced_widgets': len(self.enhanced_widgets),
            'content_manager_active': self.content_manager is not None,
            'total_managed_content': len(self.content_manager.content_items) if self.content_manager else 0
        }
        
    def apply_theme_integration(self, theme_config: Dict[str, Any]):
        """Apply theme integration with Phase 1 improvements."""
        try:
            # Apply theme to responsive components
            if hasattr(self.responsive_layout, 'apply_theme'):
                self.responsive_layout.apply_theme(theme_config)
                
            # Apply theme to enhanced widgets
            for widget in self.enhanced_widgets.values():
                if hasattr(widget, 'apply_theme'):
                    widget.apply_theme(theme_config)
                    
            self.logger.info("Theme integration applied")
            
        except Exception as e:
            self.logger.error(f"Failed to apply theme integration: {e}")


def integrate_phase1_improvements(main_window: tk.Tk) -> Phase1IntegrationManager:
    """
    Main function to integrate all Phase 1 improvements into an existing application.
    
    Args:
        main_window: The main tkinter window
        
    Returns:
        Phase1IntegrationManager instance for ongoing management
    """
    integration_manager = Phase1IntegrationManager(main_window)
    
    if integration_manager.integrate_all_improvements():
        return integration_manager
    else:
        raise Exception("Failed to integrate Phase 1 improvements")


# Example usage integration function for existing applications
def apply_to_existing_app(app_instance):
    """Apply Phase 1 improvements to existing application instance."""
    try:
        # Get main window from app instance
        if hasattr(app_instance, 'window'):
            main_window = app_instance.window
        elif hasattr(app_instance, 'root'):
            main_window = app_instance.root
        elif hasattr(app_instance, 'main_window'):
            main_window = app_instance.main_window
        else:
            raise ValueError("Could not find main window in application instance")
            
        # Apply improvements
        integration_manager = integrate_phase1_improvements(main_window)
        
        # Store reference in app instance
        app_instance.phase1_manager = integration_manager
        
        return integration_manager
        
    except Exception as e:
        print(f"Failed to apply Phase 1 improvements to existing app: {e}")
        return None
