"""
Advanced Content Management System for AI Analysis Program
Handles dynamic content fitting, overflow management, priority-based display, and smart content optimization
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, List, Optional
from enum import Enum
from utils.logging_setup import LoggerMixin


class ContentPriority(Enum):
    """Content priority levels for display management."""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    OPTIONAL = 5


class ContentType(Enum):
    """Types of content for specialized handling."""
    TEXT = "text"
    ANALYSIS_RESULT = "analysis_result"
    SUMMARY = "summary"
    DETAILS = "details"
    CONNECTIONS = "connections"
    MIND_MAP = "mind_map"
    RAW_DATA = "raw_data"
    ERROR = "error"
    STATUS = "status"


class ContentItem:
    """Represents a single content item with metadata."""
    
    def __init__(self, content_id: str, content: Any, content_type: ContentType, 
                 priority: ContentPriority = ContentPriority.NORMAL, 
                 metadata: Optional[Dict] = None):
        self.content_id = content_id
        self.content = content
        self.content_type = content_type
        self.priority = priority
        self.metadata = metadata or {}
        self.display_widget: Optional[Any] = None  # Can be any tkinter widget
        self.is_visible = False
        self.display_order = 0
        
    def get_display_size_estimate(self) -> int:
        """Estimate display size in pixels."""
        if isinstance(self.content, str):
            lines = self.content.count('\n') + 1
            return lines * 20  # Approximate line height
        return 100  # Default estimate
        
    def get_content_summary(self, max_length: int = 100) -> str:
        """Get truncated summary of content."""
        if isinstance(self.content, str):
            if len(self.content) <= max_length:
                return self.content
            return self.content[:max_length] + "..."
        return str(self.content)[:max_length]


class SmartOverflowHandler:
    """Enhanced overflow handling with multiple strategies."""
    
    def __init__(self):
        self.overflow_strategies = {
            ContentType.TEXT: self.handle_text_overflow,
            ContentType.ANALYSIS_RESULT: self.handle_analysis_overflow,
            ContentType.SUMMARY: self.handle_summary_overflow,
            ContentType.DETAILS: self.handle_details_overflow
        }
        
    def handle_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle overflow for specific content type."""
        content_type = content_item.content_type
        handler = self.overflow_strategies.get(content_type, self.handle_default_overflow)
        return handler(content_item, available_space)
        
    def handle_text_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle text content overflow with smart truncation."""
        content = content_item.content
        if not isinstance(content, str):
            return {"action": "none"}
            
        estimated_lines = available_space // 20
        content_lines = content.split('\n')
        
        if len(content_lines) <= estimated_lines:
            return {"action": "none"}
            
        visible_lines = max(3, estimated_lines - 2)
        truncated_content = '\n'.join(content_lines[:visible_lines])
        
        return {
            "action": "truncate",
            "visible_content": truncated_content,
            "hidden_content": '\n'.join(content_lines[visible_lines:]),
            "show_expand": True
        }
        
    def handle_analysis_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle analysis result overflow by creating tabbed interface."""
        return {
            "action": "create_tab",
            "tab_title": "Analysis Details",
            "summary": content_item.get_content_summary(200)
        }
        
    def handle_summary_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle summary overflow with expandable section."""
        return {
            "action": "expand_section",
            "section_title": "Summary",
            "max_height": available_space * 0.7
        }
        
    def handle_details_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle details overflow with scrollable area."""
        return {
            "action": "create_scrollable",
            "max_height": available_space * 0.8,
            "enable_search": True
        }
        
    def handle_default_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Default overflow handling."""
        return {
            "action": "create_scrollable",
            "max_height": available_space * 0.6
        }


class AdvancedLayoutOptimizer(LoggerMixin):
    """Advanced layout optimization with multiple strategies."""
    
    def __init__(self):
        super().__init__()
        self.optimization_strategies = {
            'compact': self.apply_compact_layout,
            'balanced': self.apply_balanced_layout,
            'detailed': self.apply_detailed_layout
        }
        
    def optimize_layout(self, content_items: List[ContentItem], 
                       available_space: Dict[str, int], 
                       strategy: str = 'balanced') -> Dict[str, Any]:
        """Optimize content layout based on available space and strategy."""
        
        optimizer = self.optimization_strategies.get(strategy, self.apply_balanced_layout)
        return optimizer(content_items, available_space)
        
    def apply_compact_layout(self, content_items: List[ContentItem], 
                           available_space: Dict[str, int]) -> Dict[str, Any]:
        """Apply compact layout optimization."""
        layout = {
            'primary_content': [],
            'secondary_content': [],
            'overflow_content': [],
            'layout_type': 'compact'
        }
        
        # Sort by priority
        sorted_items = sorted(content_items, key=lambda x: x.priority.value)
        
        used_space = 0
        total_space = available_space.get('height', 600)
        
        for item in sorted_items:
            item_size = item.get_display_size_estimate()
            
            if used_space + item_size <= total_space * 0.8:  # Use 80% of space
                layout['primary_content'].append(item)
                used_space += item_size
            elif used_space + item_size <= total_space:
                layout['secondary_content'].append(item)
                used_space += item_size
            else:
                layout['overflow_content'].append(item)
                
        return layout
        
    def apply_balanced_layout(self, content_items: List[ContentItem], 
                            available_space: Dict[str, int]) -> Dict[str, Any]:
        """Apply balanced layout optimization."""
        layout = {
            'visible_content': [],
            'collapsible_content': [],
            'overflow_tabs': {},
            'layout_type': 'balanced'
        }
        
        # Sort by priority and size
        sorted_items = sorted(content_items, 
                            key=lambda x: (x.priority.value, x.get_display_size_estimate()))
        
        used_space = 0
        total_space = available_space.get('height', 600)
        space_threshold = total_space * 0.7  # Use 70% for immediate visibility
        
        for item in sorted_items:
            item_size = item.get_display_size_estimate()
            
            if item.priority in [ContentPriority.CRITICAL, ContentPriority.HIGH]:
                layout['visible_content'].append(item)
                used_space += item_size
            elif used_space + item_size <= space_threshold:
                layout['visible_content'].append(item)
                used_space += item_size
            elif item.content_type in [ContentType.DETAILS, ContentType.RAW_DATA]:
                # Group these in overflow tabs
                tab_name = item.content_type.value
                if tab_name not in layout['overflow_tabs']:
                    layout['overflow_tabs'][tab_name] = []
                layout['overflow_tabs'][tab_name].append(item)
            else:
                layout['collapsible_content'].append(item)
                
        return layout
        
    def apply_detailed_layout(self, content_items: List[ContentItem], 
                            available_space: Dict[str, int]) -> Dict[str, Any]:
        """Apply detailed layout optimization."""
        layout = {
            'primary_content': [],
            'tabbed_content': {},
            'expandable_sections': [],
            'layout_type': 'detailed'
        }
        
        # Group content by type
        content_by_type = {}
        for item in content_items:
            content_type = item.content_type
            if content_type not in content_by_type:
                content_by_type[content_type] = []
            content_by_type[content_type].append(item)
            
        # Organize into tabs and sections
        for content_type, items in content_by_type.items():
            if len(items) == 1 and items[0].priority in [ContentPriority.CRITICAL, ContentPriority.HIGH]:
                layout['primary_content'].extend(items)
            elif content_type in [ContentType.ANALYSIS_RESULT, ContentType.DETAILS]:
                layout['tabbed_content'][content_type.value] = items
            else:
                layout['expandable_sections'].extend(items)
                
        return layout


class AdvancedContentManager(LoggerMixin):
    """Advanced content management system with priority-based display and overflow handling."""
    
    def __init__(self):
        super().__init__()
        self.content_items = {}  # content_id -> ContentItem
        self.display_panels = {}  # panel_id -> widget
        self.overflow_handler = SmartOverflowHandler()
        self.layout_optimizer = AdvancedLayoutOptimizer()
        
        # Content organization
        self.content_by_priority = {priority: [] for priority in ContentPriority}
        self.content_by_type = {content_type: [] for content_type in ContentType}
        
        # Display state
        self.visible_content = set()
        self.layout_callbacks = []
        
        # Legacy compatibility
        self.content_panels = {}  # For backward compatibility
        self.overflow_handlers = {}  # For backward compatibility
        
    def register_display_panel(self, panel_id: str, widget: tk.Widget):
        """Register a display panel for content management."""
        self.display_panels[panel_id] = widget
        
        # Legacy compatibility
        self.content_panels[panel_id] = {
            'widget': widget,
            'content': [],
            'visible_content': [],
            'overflow_content': []
        }
        
        self.logger.info(f"Registered display panel: {panel_id}")
        
    def register_panel(self, panel_id: str, panel_widget, overflow_handler=None):
        """Legacy method for backward compatibility."""
        self.register_display_panel(panel_id, panel_widget)
        if overflow_handler:
            self.overflow_handlers[panel_id] = overflow_handler
        
    def add_content(self, panel_id: str, content: Any, priority: str = 'normal', 
                   content_type: str = 'text', metadata=None) -> bool:
        """Legacy method for backward compatibility."""
        
        # Convert legacy priority to enum
        priority_map = {
            'critical': ContentPriority.CRITICAL,
            'high': ContentPriority.HIGH,
            'normal': ContentPriority.NORMAL,
            'low': ContentPriority.LOW,
            'background': ContentPriority.OPTIONAL
        }
        
        # Convert legacy content type to enum
        type_map = {
            'text': ContentType.TEXT,
            'analysis': ContentType.ANALYSIS_RESULT,
            'summary': ContentType.SUMMARY,
            'details': ContentType.DETAILS,
            'error': ContentType.ERROR,
            'status': ContentType.STATUS
        }
        
        content_priority = priority_map.get(priority, ContentPriority.NORMAL)
        content_type_enum = type_map.get(content_type, ContentType.TEXT)
        
        # Generate unique content ID
        content_id = f"{panel_id}_{len(self.content_items)}"
        
        # Create and add content item
        content_item = self.add_content_item(
            content_id, content, content_type_enum, content_priority, panel_id, metadata
        )
        
        return content_item is not None
        
    def add_content_item(self, content_id: str, content: Any, content_type: ContentType, 
                        priority: ContentPriority = ContentPriority.NORMAL, 
                        panel_id: Optional[str] = None, metadata: Optional[Dict] = None) -> Optional[ContentItem]:
        """Add content to the management system."""
        
        content_item = ContentItem(content_id, content, content_type, priority, metadata)
        
        # Store content item
        self.content_items[content_id] = content_item
        self.content_by_priority[priority].append(content_item)
        self.content_by_type[content_type].append(content_item)
        
        # Legacy compatibility - add to content_panels
        if panel_id and panel_id in self.content_panels:
            legacy_item = {
                'content': content,
                'priority': priority.name.lower(),
                'type': content_type.value,
                'metadata': metadata or {},
                'id': content_id
            }
            self.content_panels[panel_id]['content'].append(legacy_item)
        
        # Auto-place content if panel specified
        if panel_id and panel_id in self.display_panels:
            self.place_content(content_item, panel_id)
            
        self.logger.info(f"Added content: {content_id} ({content_type.value}, {priority.name})")
        return content_item
        
    def place_content(self, content_item: ContentItem, panel_id: str, 
                     force_display: bool = False) -> bool:
        """Place content in specified panel."""
        
        if panel_id not in self.display_panels:
            self.logger.warning(f"Panel {panel_id} not found")
            return False
            
        panel_widget = self.display_panels[panel_id]
        
        # Check available space
        available_space = self.get_available_space(panel_widget)
        
        # Check for overflow
        if not force_display:
            overflow_result = self.overflow_handler.handle_overflow(content_item, available_space['height'])
            
            if overflow_result['action'] != 'none':
                return self.handle_content_overflow(content_item, panel_id, overflow_result)
                
        # Create display widget
        display_widget = self.create_display_widget(content_item, panel_widget)
        
        if display_widget:
            content_item.display_widget = display_widget
            content_item.is_visible = True
            self.visible_content.add(content_item.content_id)
            
            # Pack or grid the widget
            self.position_widget(display_widget, panel_widget, content_item)
            
            self.logger.info(f"Placed content {content_item.content_id} in panel {panel_id}")
            return True
            
        return False
        
    def create_display_widget(self, content_item: ContentItem, parent: tk.Widget) -> Optional[tk.Widget]:
        """Create appropriate display widget for content."""
        
        content_type = content_item.content_type
        content = content_item.content
        
        if content_type == ContentType.TEXT:
            return self.create_text_display(content, parent, content_item)
        elif content_type == ContentType.ANALYSIS_RESULT:
            return self.create_analysis_display(content, parent, content_item)
        elif content_type == ContentType.SUMMARY:
            return self.create_summary_display(content, parent, content_item)
        elif content_type == ContentType.ERROR:
            return self.create_error_display(content, parent, content_item)
        elif content_type == ContentType.STATUS:
            return self.create_status_display(content, parent, content_item)
        else:
            return self.create_default_display(content, parent, content_item)
            
    def create_text_display(self, content: str, parent: tk.Widget, content_item: ContentItem) -> tk.Widget:
        """Create text display widget."""
        frame = tk.Frame(parent, relief='sunken', bd=1)
        
        # Add title if available
        title = content_item.metadata.get('title', '')
        if title:
            title_label = tk.Label(frame, text=title, font=('Arial', 10, 'bold'))
            title_label.pack(anchor='w', padx=5, pady=(5, 0))
            
        # Text widget
        text_widget = tk.Text(frame, wrap='word', height=10, font=('Arial', 9))
        text_widget.insert('1.0', content)
        text_widget.configure(state='disabled')
        
        # Scrollbar
        scrollbar = tk.Scrollbar(frame, orient='vertical', command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        return frame
        
    def create_analysis_display(self, content: str, parent: tk.Widget, content_item: ContentItem) -> tk.Widget:
        """Create analysis result display widget."""
        frame = tk.LabelFrame(parent, text="Analysis Result", font=('Arial', 10, 'bold'))
        
        # Summary section
        summary = content_item.metadata.get('summary', content[:200] + "..." if len(content) > 200 else content)
        summary_label = tk.Label(frame, text=summary, wraplength=400, justify='left')
        summary_label.pack(anchor='w', padx=10, pady=5)
        
        # Details button
        details_button = tk.Button(
            frame, 
            text="View Details", 
            command=lambda: self.show_content_details(content_item)
        )
        details_button.pack(anchor='w', padx=10, pady=5)
        
        return frame
        
    def create_summary_display(self, content: str, parent: tk.Widget, content_item: ContentItem) -> tk.Widget:
        """Create summary display widget."""
        frame = tk.LabelFrame(parent, text="Summary", font=('Arial', 10, 'bold'))
        
        text_label = tk.Label(frame, text=content, wraplength=400, justify='left', font=('Arial', 9))
        text_label.pack(anchor='w', padx=10, pady=10)
        
        return frame
        
    def create_error_display(self, content: str, parent: tk.Widget, content_item: ContentItem) -> tk.Widget:
        """Create error display widget."""
        frame = tk.Frame(parent, bg='#ffeeee', relief='raised', bd=2)
        
        error_label = tk.Label(
            frame, 
            text=f"⚠️ Error: {content}", 
            fg='red', 
            bg='#ffeeee',
            wraplength=400, 
            justify='left',
            font=('Arial', 9)
        )
        error_label.pack(anchor='w', padx=10, pady=10)
        
        return frame
        
    def create_status_display(self, content: str, parent: tk.Widget, content_item: ContentItem) -> tk.Widget:
        """Create status display widget."""
        frame = tk.Frame(parent, bg='#eeffee', relief='flat', bd=1)
        
        status_label = tk.Label(
            frame, 
            text=f"ℹ️ {content}", 
            fg='green', 
            bg='#eeffee',
            font=('Arial', 9)
        )
        status_label.pack(anchor='w', padx=5, pady=5)
        
        return frame
        
    def create_default_display(self, content: Any, parent: tk.Widget, content_item: ContentItem) -> tk.Widget:
        """Create default display widget."""
        frame = tk.Frame(parent, relief='groove', bd=1)
        
        content_str = str(content)
        label = tk.Label(frame, text=content_str, wraplength=400, justify='left')
        label.pack(anchor='w', padx=10, pady=10)
        
        return frame
        
    def position_widget(self, widget: tk.Widget, parent: tk.Widget, content_item: ContentItem):
        """Position widget within parent based on priority and type."""
        priority = content_item.priority
        
        # Higher priority items go to the top
        if priority in [ContentPriority.CRITICAL, ContentPriority.HIGH]:
            widget.pack(side='top', fill='x', padx=5, pady=(5, 2))
        else:
            widget.pack(side='top', fill='x', padx=5, pady=2)
            
    def get_available_space(self, widget: tk.Widget) -> Dict[str, int]:
        """Calculate available space in widget."""
        try:
            widget.update_idletasks()
            return {
                'width': widget.winfo_width(),
                'height': widget.winfo_height()
            }
        except Exception:
            return {'width': 400, 'height': 300}  # Default fallback
            
    def handle_content_overflow(self, content_item: ContentItem, panel_id: str, 
                               overflow_result: Dict[str, Any]) -> bool:
        """Handle content overflow based on overflow strategy."""
        action = overflow_result['action']
        
        if action == 'truncate':
            return self.create_truncated_display(content_item, panel_id, overflow_result)
        elif action == 'create_tab':
            return self.create_tabbed_display(content_item, panel_id, overflow_result)
        elif action == 'expand_section':
            return self.create_expandable_display(content_item, panel_id, overflow_result)
        elif action == 'create_scrollable':
            return self.create_scrollable_display(content_item, panel_id, overflow_result)
            
        return False
        
    def create_truncated_display(self, content_item: ContentItem, panel_id: str, 
                               overflow_result: Dict[str, Any]) -> bool:
        """Create truncated content display with expand option."""
        panel_widget = self.display_panels[panel_id]
        frame = tk.Frame(panel_widget)
        
        # Show visible content
        visible_content = overflow_result['visible_content']
        text_label = tk.Label(frame, text=visible_content, wraplength=400, justify='left')
        text_label.pack(anchor='w', padx=5, pady=5)
        
        # Expand button
        if overflow_result.get('show_expand', False):
            expand_button = tk.Button(
                frame, 
                text="Show More...", 
                command=lambda: self.expand_content(content_item, frame)
            )
            expand_button.pack(anchor='w', padx=5, pady=2)
            
        content_item.display_widget = frame
        content_item.is_visible = True
        self.position_widget(frame, panel_widget, content_item)
        
        return True
        
    def create_tabbed_display(self, content_item: ContentItem, panel_id: str, 
                            overflow_result: Dict[str, Any]) -> bool:
        """Create tabbed display for overflow content."""
        # This would integrate with a notebook widget
        # For now, create a summary with a details button
        return self.place_content(content_item, panel_id, force_display=True)
        
    def create_expandable_display(self, content_item: ContentItem, panel_id: str, 
                                overflow_result: Dict[str, Any]) -> bool:
        """Create expandable section display."""
        try:
            from gui.enhanced_text_widget import ExpandableSection
            
            panel_widget = self.display_panels[panel_id]
            section_title = overflow_result.get('section_title', content_item.content_type.value.title())
            
            section = ExpandableSection(panel_widget, section_title, str(content_item.content))
            section.pack(fill='x', padx=5, pady=2)
            
            content_item.display_widget = section.section_frame
            content_item.is_visible = True
            
            return True
        except ImportError:
            # Fallback if ExpandableSection not available
            return self.create_scrollable_display(content_item, panel_id, overflow_result)
        
    def create_scrollable_display(self, content_item: ContentItem, panel_id: str, 
                                overflow_result: Dict[str, Any]) -> bool:
        """Create scrollable display for overflow content."""
        panel_widget = self.display_panels[panel_id]
        max_height = overflow_result.get('max_height', 200)
        
        frame = tk.Frame(panel_widget)
        
        # Create scrollable text widget
        text_widget = tk.Text(frame, wrap='word', height=int(max_height/20))
        scrollbar = tk.Scrollbar(frame, orient='vertical', command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.insert('1.0', str(content_item.content))
        text_widget.configure(state='disabled')
        
        text_widget.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        content_item.display_widget = frame
        content_item.is_visible = True
        self.position_widget(frame, panel_widget, content_item)
        
        return True
        
    def expand_content(self, content_item: ContentItem, current_frame: Optional[tk.Widget] = None):
        """Expand truncated content to show full content."""
        # Create new window or expand current display
        content_window = tk.Toplevel()
        content_window.title(f"Content: {content_item.content_id}")
        content_window.geometry("600x400")
        
        text_widget = tk.Text(content_window, wrap='word')
        scrollbar = tk.Scrollbar(content_window, orient='vertical', command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.insert('1.0', str(content_item.content))
        text_widget.configure(state='disabled')
        
        text_widget.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
    def show_content_details(self, content_item: ContentItem):
        """Show detailed view of content."""
        self.expand_content(content_item)
        
    def optimize_layout(self, panel_id: str, strategy: str = 'balanced'):
        """Optimize layout for specific panel."""
        if panel_id not in self.display_panels:
            return
            
        panel_widget = self.display_panels[panel_id]
        available_space = self.get_available_space(panel_widget)
        
        # Get content items for this panel
        panel_content = [item for item in self.content_items.values() 
                        if item.display_widget and item.display_widget.master == panel_widget]
        
        # Apply layout optimization
        layout_result = self.layout_optimizer.optimize_layout(panel_content, available_space, strategy)
        
        # Apply the optimized layout
        self.apply_optimized_layout(panel_widget, layout_result)
        
    def apply_optimized_layout(self, panel_widget: tk.Widget, layout_result: Dict[str, Any]):
        """Apply optimized layout to panel."""
        # Clear current layout
        for child in panel_widget.winfo_children():
            child.pack_forget()
            
        layout_type = layout_result['layout_type']
        
        if layout_type == 'compact':
            self.apply_compact_layout_to_panel(panel_widget, layout_result)
        elif layout_type == 'detailed':
            self.apply_detailed_layout_to_panel(panel_widget, layout_result)
        elif layout_type == 'balanced':
            self.apply_balanced_layout_to_panel(panel_widget, layout_result)
            
    def apply_compact_layout_to_panel(self, panel_widget: tk.Widget, layout_result: Dict[str, Any]):
        """Apply compact layout to panel."""
        # Show primary content first
        for item in layout_result['primary_content']:
            if item.display_widget:
                item.display_widget.pack(side='top', fill='x', padx=2, pady=1)
                
        # Show secondary content
        for item in layout_result['secondary_content']:
            if item.display_widget:
                item.display_widget.pack(side='top', fill='x', padx=2, pady=1)
                
        # Add overflow indicator if needed
        if layout_result['overflow_content']:
            overflow_button = tk.Button(
                panel_widget,
                text=f"+ {len(layout_result['overflow_content'])} more items",
                command=lambda: self.show_overflow_content(layout_result['overflow_content'])
            )
            overflow_button.pack(side='bottom', pady=5)
            
    def apply_detailed_layout_to_panel(self, panel_widget: tk.Widget, layout_result: Dict[str, Any]):
        """Apply detailed layout to panel."""
        # Create notebook for tabs if needed
        if layout_result['tabbed_content']:
            notebook = ttk.Notebook(panel_widget)
            notebook.pack(fill='both', expand=True, padx=5, pady=5)
            
            for tab_name, items in layout_result['tabbed_content'].items():
                tab_frame = ttk.Frame(notebook)
                notebook.add(tab_frame, text=tab_name.title())
                
                for item in items:
                    if item.display_widget:
                        # Reparent widget to tab frame
                        item.display_widget.pack_forget()
                        item.display_widget.master = tab_frame
                        item.display_widget.pack(side='top', fill='x', padx=5, pady=2)
                        
        # Show primary content
        for item in layout_result['primary_content']:
            if item.display_widget:
                item.display_widget.pack(side='top', fill='x', padx=5, pady=2)
                
    def apply_balanced_layout_to_panel(self, panel_widget: tk.Widget, layout_result: Dict[str, Any]):
        """Apply balanced layout to panel."""
        # Show visible content
        for item in layout_result['visible_content']:
            if item.display_widget:
                item.display_widget.pack(side='top', fill='x', padx=5, pady=2)
                
        # Add collapsible sections
        for item in layout_result['collapsible_content']:
            try:
                from gui.enhanced_text_widget import ExpandableSection
                section = ExpandableSection(
                    panel_widget, 
                    item.content_type.value.title(), 
                    str(item.content),
                    'collapsed'
                )
                section.pack(fill='x', padx=5, pady=2)
            except ImportError:
                # Fallback: create a simple labeled frame
                frame = tk.LabelFrame(panel_widget, text=item.content_type.value.title())
                label = tk.Label(frame, text=str(item.content)[:100] + "...", wraplength=300)
                label.pack(padx=5, pady=5)
                frame.pack(fill='x', padx=5, pady=2)
            
    def show_overflow_content(self, overflow_items: List[ContentItem]):
        """Show overflow content in separate window."""
        overflow_window = tk.Toplevel()
        overflow_window.title("Additional Content")
        overflow_window.geometry("500x400")
        
        # Create scrollable frame for overflow content
        canvas = tk.Canvas(overflow_window)
        scrollbar = tk.Scrollbar(overflow_window, orient='vertical', command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Add overflow items
        for item in overflow_items:
            item_frame = self.create_display_widget(item, scrollable_frame)
            if item_frame:
                item_frame.pack(side='top', fill='x', padx=5, pady=2)
                
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def clear_panel(self, panel_id: str):
        """Clear all content from specified panel."""
        if panel_id in self.display_panels:
            panel_widget = self.display_panels[panel_id]
            for child in panel_widget.winfo_children():
                child.destroy()
                
    def remove_content(self, content_id: str):
        """Remove content from management system."""
        if content_id in self.content_items:
            content_item = self.content_items[content_id]
            
            # Remove from display
            if content_item.display_widget:
                content_item.display_widget.destroy()
                
            # Remove from tracking
            del self.content_items[content_id]
            self.visible_content.discard(content_id)
            
            # Remove from priority and type lists
            self.content_by_priority[content_item.priority].remove(content_item)
            self.content_by_type[content_item.content_type].remove(content_item)
            
            self.logger.info(f"Removed content: {content_id}")
            
    def get_content_summary(self) -> Dict[str, Any]:
        """Get summary of managed content."""
        return {
            'total_items': len(self.content_items),
            'visible_items': len(self.visible_content),
            'by_priority': {priority.name: len(items) for priority, items in self.content_by_priority.items()},
            'by_type': {content_type.value: len(items) for content_type, items in self.content_by_type.items()},
            'display_panels': list(self.display_panels.keys())
        }


# Legacy aliases for backward compatibility
ContentManager = AdvancedContentManager
LayoutOptimizer = AdvancedLayoutOptimizer
