"""
Enhanced Main Window with Responsive Layout for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import show_error, show_info, confirm_action
from utils.theme import <PERSON><PERSON><PERSON>ger
from typing import Dict, Any
from analysis.ollama_client import OllamaClient
from analysis.enhanced_ollama_client import EnhancedOllamaClient
from analysis.advanced_analyzers import analysis_registry
from gui.topic_input import TopicInputPanel
from gui.topic_list import TopicListPanel
from gui.analysis_panel import AnalysisPanel
from gui.results_viewer import ResultsViewer
from gui.settings_dialog import SettingsDialog
from gui.progress_dialog import AnalysisProgressManager
from data.project_manager import ProjectManager

# Import new responsive components
from gui.responsive_layout import ResponsiveLayoutManager, AdaptivePanedWindow
from gui.enhanced_scrolling import EnhancedScrollable<PERSON>rame
from gui.content_management import ContentManager


class EnhancedMainWindow(LoggerMixin):
    """Enhanced main application window with responsive layout"""
    
    def __init__(self, root: tk.Tk, config: Config):
        self.root = root
        self.config = config
        self.ollama_client = OllamaClient(config)
        self.enhanced_client = EnhancedOllamaClient(config)
        self.project_manager = ProjectManager(config)
        self.progress_manager = AnalysisProgressManager(root)

        # Initialize responsive layout manager
        self.layout_manager = ResponsiveLayoutManager(root)
        self.content_manager = ContentManager()

        # Initialize theme manager
        self.theme = ThemeManager(root)

        # Initialize components
        self.topic_input = None
        self.topic_list = None
        self.analysis_panel = None
        self.results_viewer = None
        
        # Responsive components
        self.main_paned = None
        self.right_paned = None
        self.left_scrollable = None
        self.analysis_scrollable = None
        self.results_scrollable = None

        # State variables
        self.current_analysis = None
        self.analysis_running = False
        self.current_project_id = None

        self.setup_ui()
        self.setup_menu()
        self.check_ollama_connection()
        
        # Apply initial layout
        self.layout_manager.apply_layout(force_update=True)
    
    def setup_ui(self):
        """Set up the enhanced responsive user interface"""
        # Create main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

        # Add toolbar
        self.setup_toolbar(main_frame)

        # Create adaptive paned window for resizable panels
        self.main_paned = AdaptivePanedWindow(
            main_frame, 
            self.layout_manager,
            orient=tk.HORIZONTAL
        )
        self.main_paned.pack(fill=tk.BOTH, expand=True, pady=(3, 0))

        # Left panel - Topic List and Topic Input with enhanced scrolling
        self.left_scrollable = EnhancedScrollableFrame(self.main_paned)
        left_main_frame = ttk.Frame(self.left_scrollable.get_frame())
        left_main_frame.pack(fill=tk.BOTH, expand=True)

        # Create vertical paned window for topic list and topic input
        left_paned = AdaptivePanedWindow(
            left_main_frame,
            self.layout_manager,
            orient=tk.VERTICAL
        )
        left_paned.pack(fill=tk.BOTH, expand=True)

        # Topic List panel (top)
        topic_list_frame = ttk.LabelFrame(
            left_paned,
            text="📚 Topic Library",
            padding=3
        )
        self.topic_list = TopicListPanel(topic_list_frame, self.config, self.theme, self.on_topic_selected)
        left_paned.add_panel(topic_list_frame, 'topic_list')

        # Topic Input panel (bottom)
        topic_input_frame = ttk.LabelFrame(
            left_paned,
            text="📝 Topic Input",
            padding=3
        )
        self.topic_input = TopicInputPanel(topic_input_frame, self.config, self.theme)
        left_paned.add_panel(topic_input_frame, 'topic_input')

        self.main_paned.add_panel(self.left_scrollable, 'left_panel')

        # Right panel container with adaptive behavior
        right_container = ttk.Frame(self.main_paned)
        self.main_paned.add_panel(right_container, 'right_container', weight=2)

        # Right panel - Analysis Control (top) and Results (bottom)
        self.right_paned = AdaptivePanedWindow(
            right_container,
            self.layout_manager,
            orient=tk.VERTICAL
        )
        self.right_paned.pack(fill=tk.BOTH, expand=True)

        # Analysis Control panel with enhanced scrolling
        self.analysis_scrollable = EnhancedScrollableFrame(self.right_paned)
        analysis_frame = ttk.LabelFrame(
            self.analysis_scrollable.get_frame(),
            text="⚙️ Analysis Control",
            padding=5
        )
        analysis_frame.pack(fill=tk.BOTH, expand=True)
        self.analysis_panel = AnalysisPanel(analysis_frame, self.config, self.on_start_analysis, self.theme)
        self.right_paned.add_panel(self.analysis_scrollable, 'analysis_panel')

        # Results panel with enhanced scrolling and content management
        self.results_scrollable = EnhancedScrollableFrame(self.right_paned)
        results_frame = ttk.LabelFrame(
            self.results_scrollable.get_frame(),
            text="📊 Results",
            padding=3
        )
        results_frame.pack(fill=tk.BOTH, expand=True)
        self.results_viewer = EnhancedResultsViewer(results_frame, self.config, self.theme, self.content_manager)
        self.right_paned.add_panel(self.results_scrollable, 'results_viewer', weight=3)

        # Register content panels with content manager
        self.content_manager.register_panel('results', self.results_viewer)
        self.content_manager.register_panel('analysis', self.analysis_panel)

        # Status bar with responsive layout
        self.setup_status_bar()

    def on_topic_selected(self, topic_data: Dict[str, Any]):
        """Handle topic selection from the topic library"""
        try:
            # Load the selected topic into the topic input panel
            self.topic_input.set_topic_data(topic_data)

            # Update status
            self.update_status(f"Loaded topic: {topic_data.get('title', 'Unknown')}")

            # Show success message
            show_info("Topic Loaded", f"Topic '{topic_data.get('title', 'Unknown')}' loaded successfully.")

        except Exception as e:
            self.logger.error(f"Failed to load selected topic: {e}")
            show_error("Load Error", f"Failed to load selected topic: {str(e)}")

    def setup_toolbar(self, parent):
        """Set up the responsive application toolbar"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 3))

        # Left side - Primary actions with compact layout
        left_toolbar = ttk.Frame(toolbar_frame)
        left_toolbar.pack(side=tk.LEFT)

        # Quick action buttons with adaptive widths
        self.toolbar_buttons = {
            'new': ttk.Button(left_toolbar, text="🆕", command=self.new_analysis),
            'open': ttk.Button(left_toolbar, text="📂", command=self.open_analysis),
            'save': ttk.Button(left_toolbar, text="💾", command=self.save_analysis),
            'start': ttk.Button(left_toolbar, text="▶️", command=self.start_analysis),
            'stop': ttk.Button(left_toolbar, text="⏹️", command=self.stop_analysis)
        }

        for i, (name, button) in enumerate(self.toolbar_buttons.items()):
            button.pack(side=tk.LEFT, padx=(0 if i == 0 else 2, 0))

        # Separator
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )

        # Right side - Settings and help
        right_toolbar = ttk.Frame(toolbar_frame)
        right_toolbar.pack(side=tk.RIGHT)

        ttk.Button(right_toolbar, text="⚙️", command=self.show_settings).pack(side=tk.RIGHT, padx=(2, 0))
        ttk.Button(right_toolbar, text="❓", command=self.show_user_guide).pack(side=tk.RIGHT, padx=(2, 0))

        # Register toolbar for responsive updates
        self.layout_manager.register_layout_callback('toolbar', self.update_toolbar_layout)
        
        # Apply initial layout to set correct button sizes
        self.root.after_idle(lambda: self.layout_manager.apply_layout(force_update=True))

    def update_toolbar_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update toolbar layout based on screen size"""
        if category in ['small']:
            # Keep icon-only buttons for small screens
            button_configs = {
                'new': {"text": "🆕", "width": 3},
                'open': {"text": "📂", "width": 3},
                'save': {"text": "💾", "width": 3},
                'start': {"text": "▶️", "width": 3},
                'stop': {"text": "⏹️", "width": 3}
            }
        elif category in ['medium']:
            # Add minimal text for medium screens
            button_configs = {
                'new': {"text": "🆕 New", "width": 8},
                'open': {"text": "📂 Open", "width": 9},
                'save': {"text": "💾 Save", "width": 9},
                'start': {"text": "▶️ Start", "width": 10},
                'stop': {"text": "⏹️ Stop", "width": 9}
            }
        else:
            # Full text for large screens
            button_configs = {
                'new': {"text": "🆕 New Analysis", "width": 16},
                'open': {"text": "📂 Open Project", "width": 16},
                'save': {"text": "💾 Save Project", "width": 16},
                'start': {"text": "▶️ Start Analysis", "width": 18},
                'stop': {"text": "⏹️ Stop Analysis", "width": 17}
            }

        # Update button texts and widths
        for name, button in self.toolbar_buttons.items():
            config = button_configs.get(name, {})
            if config:
                try:
                    button.configure(text=config["text"], width=config["width"])
                except tk.TclError:
                    pass

    def setup_status_bar(self):
        """Set up responsive status bar"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=3, pady=(0, 3))

        # Left side - Status information
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT)

        # Right side - Progress and controls
        self.progress_frame = ttk.Frame(self.status_frame)
        self.progress_frame.pack(side=tk.RIGHT)

        self.progress_var = tk.StringVar()
        self.progress_label = ttk.Label(self.progress_frame, textvariable=self.progress_var, width=20)
        self.progress_label.pack(side=tk.RIGHT, padx=(5, 0))

        # Register status bar for responsive updates
        self.layout_manager.register_layout_callback('status', self.update_status_layout)

    def update_status_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update status bar layout based on screen size"""
        if category == 'small':
            # Compact status for small screens
            self.progress_label.configure(width=15)
        else:
            # Full status for larger screens
            self.progress_label.configure(width=25)

    # Implement required methods from original MainWindow
    def setup_menu(self):
        """Set up the application menu"""
        # Implementation from original main_window.py
        pass

    def check_ollama_connection(self):
        """Check Ollama connection status"""
        # Implementation from original main_window.py
        pass

    def new_analysis(self):
        """Create new analysis"""
        # Implementation from original main_window.py
        pass

    def open_analysis(self):
        """Open existing analysis"""
        # Implementation from original main_window.py
        pass

    def save_analysis(self):
        """Save current analysis"""
        # Implementation from original main_window.py
        pass

    def start_analysis(self):
        """Start analysis process"""
        # Implementation from original main_window.py
        pass

    def stop_analysis(self):
        """Stop analysis process"""
        # Implementation from original main_window.py
        pass

    def on_start_analysis(self, analysis_config):
        """Handle analysis start"""
        # Implementation from original main_window.py
        pass

    def show_settings(self):
        """Show settings dialog"""
        # Implementation from original main_window.py
        pass

    def show_user_guide(self):
        """Show user guide"""
        # Implementation from original main_window.py
        pass

    def confirm_close(self) -> bool:
        """Confirm application close"""
        # Implementation from original main_window.py
        return True

    def cleanup(self):
        """Cleanup resources"""
        # Implementation from original main_window.py
        pass


class EnhancedResultsViewer(ResultsViewer):
    """Enhanced results viewer with content management"""
    
    def __init__(self, parent: tk.Widget, config: Config, theme=None, content_manager=None):
        self.content_manager = content_manager
        super().__init__(parent, config, theme)
        
    def display_results(self, results: Dict[str, Any]):
        """Display results with content management"""
        if self.content_manager:
            # Add results to content manager for overflow handling
            self.content_manager.add_content(
                'results', 
                results, 
                priority='high',
                content_type='analysis_results'
            )
        
        # Call parent implementation
        super().display_results(results)


# Maintain backward compatibility
class MainWindow(EnhancedMainWindow):
    """Backward compatible main window class"""
    pass
