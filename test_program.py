#!/usr/bin/env python3
"""
Test script for AI Analysis Program
Tests basic functionality without requiring Ollama
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from utils.config import Config
        print("✓ Config imported successfully")
        
        from utils.logging_setup import setup_logging
        print("✓ Logging setup imported successfully")
        
        from utils.helpers import sanitize_filename, generate_hash
        print("✓ Helpers imported successfully")
        
        from analysis.ollama_client import OllamaClient
        print("✓ Ollama client imported successfully")
        
        from analysis.mindmap_generator import MindMapGenerator
        print("✓ Mind map generator imported successfully")

        from analysis.advanced_analyzers import analysis_registry
        print("✓ Advanced analyzers imported successfully")

        from gui.settings_dialog import SettingsDialog
        print("✓ Settings dialog imported successfully")

        from gui.progress_dialog import ProgressDialog, AnalysisProgressManager
        print("✓ Progress dialog imported successfully")

        from data.project_manager import ProjectManager
        print("✓ Project manager imported successfully")
        
        from gui.main_window import MainWindow
        print("✓ Main window imported successfully")
        
        from gui.topic_input import TopicInputPanel
        print("✓ Topic input panel imported successfully")
        
        from gui.analysis_panel import AnalysisPanel
        print("✓ Analysis panel imported successfully")
        
        from gui.results_viewer import ResultsViewer
        print("✓ Results viewer imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_config():
    """Test configuration system"""
    print("\nTesting configuration...")
    
    try:
        from utils.config import Config
        
        config = Config()
        
        # Test default values
        assert config.get("ollama.base_url") == "http://localhost:11434"
        assert config.get("analysis.max_recursive_depth") == 5
        assert config.get("export.default_format") == "markdown"
        
        # Test setting values
        config.set("test.value", "test_data")
        assert config.get("test.value") == "test_data"
        
        print("✓ Configuration system working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_mindmap_generator():
    """Test mind map generator"""
    print("\nTesting mind map generator...")
    
    try:
        from utils.config import Config
        from analysis.mindmap_generator import MindMapGenerator
        
        config = Config()
        generator = MindMapGenerator(config)
        
        # Test data conversion
        sample_results = {
            "type": "iterative",
            "topic": "Test Topic",
            "main_analysis": "This is a test analysis of the main topic.",
            "sub_analyses": [
                {
                    "topic": "Sub-topic 1",
                    "analysis": "Analysis of sub-topic 1"
                },
                {
                    "topic": "Sub-topic 2", 
                    "analysis": "Analysis of sub-topic 2"
                }
            ],
            "connections": "These topics are connected through common themes.",
            "metadata": {
                "model": "test_model",
                "timestamp": "2024-01-01T12:00:00"
            }
        }
        
        # Convert to mind map data
        mindmap_data = generator.convert_analysis_to_mindmap(sample_results)
        
        # Verify structure
        assert "nodes" in mindmap_data
        assert "links" in mindmap_data
        assert "metadata" in mindmap_data
        assert len(mindmap_data["nodes"]) >= 3  # Root + 2 sub-topics + connections
        
        print("✓ Mind map generator working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Mind map generator test failed: {e}")
        return False

def test_file_structure():
    """Test that required directories and files exist"""
    print("\nTesting file structure...")
    
    required_dirs = [
        "utils",
        "gui", 
        "analysis",
        "data"
    ]
    
    required_files = [
        "main.py",
        "requirements.txt",
        "utils/__init__.py",
        "utils/config.py",
        "utils/logging_setup.py",
        "utils/helpers.py",
        "gui/__init__.py",
        "gui/main_window.py",
        "gui/topic_input.py",
        "gui/analysis_panel.py",
        "gui/results_viewer.py",
        "analysis/__init__.py",
        "analysis/ollama_client.py",
        "analysis/mindmap_generator.py",
        "data/__init__.py"
    ]
    
    all_good = True
    
    # Check directories
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✓ Directory {dir_name} exists")
        else:
            print(f"✗ Directory {dir_name} missing")
            all_good = False
    
    # Check files
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✓ File {file_name} exists")
        else:
            print(f"✗ File {file_name} missing")
            all_good = False
    
    return all_good

def create_sample_analysis():
    """Create a sample analysis for testing"""
    return {
        "type": "iterative",
        "topic": "Artificial Intelligence in Healthcare",
        "main_analysis": """
        Artificial Intelligence in Healthcare represents a transformative force that is reshaping 
        medical practice, diagnosis, and patient care. This technology encompasses machine learning 
        algorithms, natural language processing, computer vision, and robotics to enhance medical 
        decision-making, improve patient outcomes, and streamline healthcare operations.
        
        Key areas of impact include diagnostic imaging, drug discovery, personalized medicine, 
        and administrative efficiency. AI systems can analyze medical images with remarkable 
        accuracy, sometimes surpassing human radiologists in detecting certain conditions.
        """,
        "sub_analyses": [
            {
                "topic": "Diagnostic Imaging",
                "analysis": """
                AI in diagnostic imaging has shown remarkable progress, particularly in radiology 
                and pathology. Deep learning models can detect cancers, fractures, and other 
                abnormalities in X-rays, CT scans, and MRIs with high accuracy. Companies like 
                Google's DeepMind have developed systems that can diagnose over 50 eye diseases 
                from retinal scans.
                """
            },
            {
                "topic": "Drug Discovery",
                "analysis": """
                AI is accelerating drug discovery by predicting molecular behavior, identifying 
                potential drug targets, and optimizing clinical trials. Machine learning models 
                can analyze vast databases of chemical compounds and biological data to identify 
                promising drug candidates, potentially reducing development time from decades to years.
                """
            },
            {
                "topic": "Personalized Medicine",
                "analysis": """
                AI enables personalized treatment plans by analyzing patient genetics, medical 
                history, and lifestyle factors. This approach allows for tailored therapies that 
                are more effective and have fewer side effects. Precision oncology is a leading 
                example where AI helps match cancer patients with the most suitable treatments.
                """
            },
            {
                "topic": "Administrative Efficiency",
                "analysis": """
                AI streamlines healthcare administration through automated scheduling, billing, 
                and documentation. Natural language processing can extract information from 
                clinical notes, while chatbots handle routine patient inquiries. This reduces 
                administrative burden on healthcare providers and improves operational efficiency.
                """
            }
        ],
        "connections": """
        The sub-topics in AI healthcare are deeply interconnected. Diagnostic imaging provides 
        data that feeds into personalized medicine algorithms. Drug discovery benefits from 
        diagnostic insights and personalized patient profiles. Administrative efficiency 
        supports all other areas by ensuring smooth operations and data management.
        
        Common themes include data integration, pattern recognition, and the need for regulatory 
        compliance. All areas face similar challenges around data privacy, algorithm bias, 
        and the need for clinical validation.
        """,
        "metadata": {
            "model": "test_model",
            "timestamp": "2024-01-01T12:00:00",
            "analysis_duration": "5 minutes"
        }
    }

def test_mindmap_generation():
    """Test complete mind map generation"""
    print("\nTesting complete mind map generation...")
    
    try:
        from utils.config import Config
        from analysis.mindmap_generator import MindMapGenerator
        
        config = Config()
        generator = MindMapGenerator(config)
        
        # Create sample analysis
        sample_analysis = create_sample_analysis()
        
        # Generate mind map
        output_path = generator.generate_interactive_mindmap(
            sample_analysis, 
            "test_mindmap.html"
        )
        
        # Verify file was created
        if Path(output_path).exists():
            print(f"✓ Mind map generated successfully: {output_path}")
            
            # Check file size (should be substantial)
            file_size = Path(output_path).stat().st_size
            if file_size > 10000:  # At least 10KB
                print(f"✓ Mind map file has reasonable size: {file_size} bytes")
                return True
            else:
                print(f"✗ Mind map file too small: {file_size} bytes")
                return False
        else:
            print("✗ Mind map file was not created")
            return False
            
    except Exception as e:
        print(f"✗ Mind map generation test failed: {e}")
        return False

def test_advanced_analyzers():
    """Test advanced analysis types"""
    print("\nTesting advanced analyzers...")

    try:
        from analysis.advanced_analyzers import analysis_registry

        # Test registry
        available_types = analysis_registry.list_available()
        assert len(available_types) >= 3  # Should have at least comparative, swot, temporal

        print(f"✓ Found {len(available_types)} analysis types")

        # Test comparative analysis
        comparative_data = {
            "topics": ["Python", "JavaScript", "Java"],
            "context": "Programming languages comparison"
        }

        # Mock ollama client for testing
        class MockOllamaClient:
            def generate(self, prompt):
                if "comparison" in prompt.lower():
                    return '{"Python": {"quality": 8, "cost": 9}, "JavaScript": {"quality": 7, "cost": 8}, "Java": {"quality": 8, "cost": 7}}'
                elif "swot" in prompt.lower():
                    return """
                    STRENGTHS:
                    - Strong community support
                    - Extensive libraries

                    WEAKNESSES:
                    - Performance limitations
                    - Learning curve

                    OPPORTUNITIES:
                    - Growing market demand
                    - New frameworks

                    THREATS:
                    - Competition from other languages
                    - Technology changes
                    """
                elif "timeline" in prompt.lower():
                    return """
                    2010: Initial development
                    2015: Major version release
                    2020: Widespread adoption
                    """
                else:
                    return "Test analysis result"

            def analyze_topic(self, topic, context="", analysis_type="general"):
                return f"Analysis of {topic}: This is a test analysis."

        mock_client = MockOllamaClient()

        # Test comparative analysis
        comp_result = analysis_registry.analyze("comparative", comparative_data, mock_client)
        assert comp_result.get("type") == "comparative"
        assert "topics" in comp_result
        print("✓ Comparative analysis working")

        # Test SWOT analysis
        swot_data = {
            "topic": "AI Technology",
            "context": "Business implementation"
        }
        swot_result = analysis_registry.analyze("swot", swot_data, mock_client)
        assert swot_result.get("type") == "swot"
        assert "swot_matrix" in swot_result
        print("✓ SWOT analysis working")

        # Test temporal analysis
        temporal_data = {
            "topic": "Cloud Computing",
            "time_frame": "10 years"
        }
        temporal_result = analysis_registry.analyze("temporal", temporal_data, mock_client)
        assert temporal_result.get("type") == "temporal"
        assert "timeline" in temporal_result
        print("✓ Temporal analysis working")

        print("✓ Advanced analyzers working correctly")
        return True

    except Exception as e:
        print(f"✗ Advanced analyzers test failed: {e}")
        return False

def test_improvements():
    """Test the three major improvements"""
    print("\nTesting program improvements...")

    try:
        from utils.config import Config
        from data.project_manager import ProjectManager

        config = Config()

        # Test 1: Project Manager
        project_manager = ProjectManager(config)

        # Create a test project
        project_id = project_manager.create_project(
            "Test Project",
            "A test project for validation",
            {"title": "Test Topic", "sub_topics": ["Sub 1", "Sub 2"]}
        )

        assert project_id, "Project creation failed"

        # Load the project
        project_data = project_manager.load_project(project_id)
        assert project_data is not None, "Project loading failed"
        assert project_data["name"] == "Test Project"

        # List projects
        projects = project_manager.list_projects()
        assert len(projects) >= 1, "Project listing failed"

        # Add analysis to history
        history_id = project_manager.add_analysis_to_history(
            project_id, "test", "Test Topic", 5.0, "test_model", True, 1000
        )
        assert history_id, "Analysis history addition failed"

        # Get statistics
        stats = project_manager.get_project_statistics()
        assert stats["total_projects"] >= 1, "Statistics retrieval failed"

        print("✓ Project management system working")

        # Test 2: Settings Dialog (basic structure test)
        # We can't fully test GUI components without a display, but we can test imports
        from gui.settings_dialog import SettingsDialog
        print("✓ Settings dialog structure available")

        # Test 3: Progress Dialog (basic structure test)
        from gui.progress_dialog import ProgressDialog, AnalysisProgressManager
        print("✓ Progress dialog structure available")

        # Cleanup test project
        project_manager.delete_project(project_id)

        print("✓ All improvements working correctly")
        return True

    except Exception as e:
        print(f"✗ Improvements test failed: {e}")
        return False

def test_theme_system():
    """Test the theme system functionality"""
    print("\nTesting theme system...")

    try:
        import tkinter as tk
        from utils.theme import ThemeManager, ColorScheme, Typography, Spacing

        # Test color scheme
        colors = ColorScheme()
        assert hasattr(colors, 'primary'), "ColorScheme missing primary color"
        assert hasattr(colors, 'accent'), "ColorScheme missing accent color"
        assert hasattr(colors, 'bg_main'), "ColorScheme missing background color"
        print("✓ ColorScheme structure valid")

        # Test typography
        typography = Typography()
        assert hasattr(typography, 'font_main'), "Typography missing main font"
        assert hasattr(typography, 'size_normal'), "Typography missing normal size"
        print("✓ Typography structure valid")

        # Test spacing
        spacing = Spacing()
        assert hasattr(spacing, 'pad_md'), "Spacing missing medium padding"
        assert hasattr(spacing, 'margin_sm'), "Spacing missing small margin"
        print("✓ Spacing structure valid")

        # Test theme manager (without display)
        root = tk.Tk()
        root.withdraw()  # Hide window

        theme = ThemeManager(root)
        assert theme.colors is not None, "ThemeManager colors not initialized"
        assert theme.typography is not None, "ThemeManager typography not initialized"
        assert theme.spacing is not None, "ThemeManager spacing not initialized"

        # Test helper methods
        font = theme.get_font("normal", "bold", "main")
        assert isinstance(font, tuple), "get_font should return tuple"
        assert len(font) == 3, "Font tuple should have 3 elements"

        color = theme.get_color("primary")
        assert isinstance(color, str), "get_color should return string"
        assert color.startswith("#"), "Color should be hex format"

        spacing_val = theme.get_spacing("pad_md")
        assert isinstance(spacing_val, int), "get_spacing should return integer"

        root.destroy()
        print("✓ ThemeManager working correctly")
        return True

    except Exception as e:
        print(f"✗ Theme system test failed: {e}")
        return False

def test_custom_widgets():
    """Test custom widget functionality"""
    print("\nTesting custom widgets...")

    try:
        import tkinter as tk
        from gui.widgets import CollapsibleFrame, ToolTip, StatusIndicator
        from utils.theme import ThemeManager

        root = tk.Tk()
        root.withdraw()  # Hide window

        theme = ThemeManager(root)

        # Test CollapsibleFrame
        collapsible = CollapsibleFrame(root, title="Test Frame", theme=theme)
        assert hasattr(collapsible, 'content_frame'), "CollapsibleFrame missing content_frame"
        assert hasattr(collapsible, 'toggle'), "CollapsibleFrame missing toggle method"
        assert hasattr(collapsible, 'collapsed'), "CollapsibleFrame missing collapsed state"

        # Test toggle functionality
        initial_state = collapsible.collapsed
        collapsible.toggle()
        assert collapsible.collapsed != initial_state, "Toggle should change state"

        print("✓ CollapsibleFrame working")

        # Test StatusIndicator
        status = StatusIndicator(root, theme=theme)
        assert hasattr(status, 'set_status'), "StatusIndicator missing set_status method"

        status.set_status("Test Status", "success")
        assert status.status_var.get() == "Test Status", "Status text not set correctly"

        print("✓ StatusIndicator working")

        root.destroy()
        print("✓ Custom widgets working correctly")
        return True

    except Exception as e:
        print(f"✗ Custom widgets test failed: {e}")
        return False

def test_gui_components():
    """Test GUI component initialization"""
    print("\nTesting GUI components...")

    try:
        import tkinter as tk
        from utils.config import Config
        from utils.theme import ThemeManager
        from gui.topic_input import TopicInputPanel
        from gui.analysis_panel import AnalysisPanel
        from gui.results_viewer import ResultsViewer

        root = tk.Tk()
        root.withdraw()  # Hide window

        config = Config()
        theme = ThemeManager(root)

        # Test TopicInputPanel
        topic_frame = tk.Frame(root)
        topic_panel = TopicInputPanel(topic_frame, config, theme)
        assert hasattr(topic_panel, 'get_topic_data'), "TopicInputPanel missing get_topic_data"
        assert hasattr(topic_panel, 'set_topic_data'), "TopicInputPanel missing set_topic_data"
        assert hasattr(topic_panel, 'subtopics_data'), "TopicInputPanel missing subtopics_data"
        print("✓ TopicInputPanel initialized")

        # Test AnalysisPanel
        analysis_frame = tk.Frame(root)
        def dummy_callback(config):
            pass
        analysis_panel = AnalysisPanel(analysis_frame, config, dummy_callback, theme)
        assert hasattr(analysis_panel, 'get_analysis_config'), "AnalysisPanel missing get_analysis_config"
        assert hasattr(analysis_panel, 'start_analysis'), "AnalysisPanel missing start_analysis"
        print("✓ AnalysisPanel initialized")

        # Test ResultsViewer
        results_frame = tk.Frame(root)
        results_viewer = ResultsViewer(results_frame, config, theme)
        assert hasattr(results_viewer, 'display_results'), "ResultsViewer missing display_results"
        assert hasattr(results_viewer, 'clear'), "ResultsViewer missing clear"
        print("✓ ResultsViewer initialized")

        root.destroy()
        print("✓ GUI components working correctly")
        return True

    except Exception as e:
        print(f"✗ GUI components test failed: {e}")
        return False

def test_data_flow():
    """Test data flow between components"""
    print("\nTesting data flow...")

    try:
        import tkinter as tk
        from utils.config import Config
        from utils.theme import ThemeManager
        from gui.topic_input import TopicInputPanel
        from gui.analysis_panel import AnalysisPanel

        root = tk.Tk()
        root.withdraw()  # Hide window

        config = Config()
        theme = ThemeManager(root)

        # Create components
        topic_frame = tk.Frame(root)
        topic_panel = TopicInputPanel(topic_frame, config, theme)

        analysis_frame = tk.Frame(root)
        def dummy_callback(config):
            pass
        analysis_panel = AnalysisPanel(analysis_frame, config, dummy_callback, theme)

        # Test topic data flow
        test_data = {
            'title': 'Test Topic',
            'description': 'Test Description',
            'sub_topics': ['Sub 1', 'Sub 2'],
            'sub_topics_detailed': [
                {'title': 'Sub 1', 'description': 'Description 1'},
                {'title': 'Sub 2', 'description': 'Description 2'}
            ]
        }

        # Set topic data
        topic_panel.set_topic_data(test_data)

        # Get topic data back
        retrieved_data = topic_panel.get_topic_data()
        assert retrieved_data is not None, "Topic data retrieval failed"
        assert retrieved_data['title'] == test_data['title'], "Title not preserved"
        assert len(retrieved_data['sub_topics']) == 2, "Sub-topics not preserved"
        print("✓ Topic data flow working")

        # Test analysis configuration
        analysis_config = analysis_panel.get_analysis_config()
        assert 'type' in analysis_config, "Analysis config missing type"
        assert 'temperature' in analysis_config, "Analysis config missing temperature"
        assert 'max_depth' in analysis_config, "Analysis config missing max_depth"
        print("✓ Analysis configuration working")

        root.destroy()
        print("✓ Data flow working correctly")
        return True

    except Exception as e:
        print(f"✗ Data flow test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("AI Analysis Program - Test Suite")
    print("=" * 40)
    
    tests = [
        test_file_structure,
        test_imports,
        test_config,
        test_mindmap_generator,
        test_mindmap_generation,
        test_advanced_analyzers,
        test_improvements,
        test_theme_system,
        test_custom_widgets,
        test_gui_components,
        test_data_flow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The program is ready to use.")
        print("\nTo run the program:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Make sure Ollama is running (for AI functionality)")
        print("3. Run: python main.py")
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
