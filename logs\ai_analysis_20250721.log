2025-07-21 10:33:10 - AIAnalysis - INFO - Logging system initialized
2025-07-21 10:33:10 - AIAnalysis - INFO - Log level: INFO
2025-07-21 10:33:10 - AIAnalysis - INFO - Console logging: True
2025-07-21 10:33:10 - AIAnalysis - INFO - File logging: True
2025-07-21 10:33:10 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Conversation_Research_Tool\logs
2025-07-21 10:33:11 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 10:33:11 - AIAnalysis - INFO - Starting application main loop
2025-07-21 10:33:31 - AIAnalysis - INFO - Application closing
2025-07-21 10:33:31 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 12:25:29 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:25:29 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:25:29 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:25:29 - AIAnalysis - INFO - File logging: True
2025-07-21 12:25:29 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Conversation_Research_Tool\logs
2025-07-21 12:25:30 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 12:25:30 - AIAnalysis - INFO - Starting application main loop
2025-07-21 12:26:29 - AIAnalysis.EnhancedOllamaClient - INFO - Starting foundation analysis for: Money
2025-07-21 12:26:31 - AIAnalysis.EnhancedOllamaClient - ERROR - Generation failed: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-21 12:26:31 - AIAnalysis.EnhancedOllamaClient - INFO - Starting sub-topic analysis for 3 topics
2025-07-21 12:26:31 - AIAnalysis.EnhancedOllamaClient - INFO - Analyzing sub-topic 1/3: Society
2025-07-21 12:26:31 - AIAnalysis.EnhancedOllamaClient - ERROR - Error in progressive analysis: 'analysis_framework'
2025-07-21 12:26:51 - AIAnalysis.EnhancedOllamaClient - INFO - Starting foundation analysis for: Money
2025-07-21 12:26:51 - AIAnalysis.EnhancedOllamaClient - ERROR - Generation failed: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-21 12:26:51 - AIAnalysis.EnhancedOllamaClient - INFO - Starting sub-topic analysis for 3 topics
2025-07-21 12:26:51 - AIAnalysis.EnhancedOllamaClient - INFO - Analyzing sub-topic 1/3: Society
2025-07-21 12:26:51 - AIAnalysis.EnhancedOllamaClient - ERROR - Error in progressive analysis: 'analysis_framework'
2025-07-21 12:26:58 - AIAnalysis.EnhancedOllamaClient - INFO - Starting foundation analysis for: Money
2025-07-21 12:26:58 - AIAnalysis.EnhancedOllamaClient - ERROR - Generation failed: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-21 12:26:58 - AIAnalysis.EnhancedOllamaClient - INFO - Starting sub-topic analysis for 3 topics
2025-07-21 12:26:58 - AIAnalysis.EnhancedOllamaClient - INFO - Analyzing sub-topic 1/3: Society
2025-07-21 12:26:58 - AIAnalysis.EnhancedOllamaClient - ERROR - Error in progressive analysis: 'analysis_framework'
2025-07-21 12:29:18 - AIAnalysis.EnhancedOllamaClient - INFO - Starting foundation analysis for: Money
2025-07-21 12:29:18 - AIAnalysis.EnhancedOllamaClient - ERROR - Generation failed: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-21 12:29:18 - AIAnalysis.EnhancedOllamaClient - INFO - Starting sub-topic analysis for 3 topics
2025-07-21 12:29:18 - AIAnalysis.EnhancedOllamaClient - INFO - Analyzing sub-topic 1/3: Society
2025-07-21 12:29:18 - AIAnalysis.EnhancedOllamaClient - ERROR - Error in progressive analysis: 'analysis_framework'
2025-07-21 12:29:21 - AIAnalysis.EnhancedOllamaClient - INFO - Starting foundation analysis for: Money
2025-07-21 12:29:21 - AIAnalysis.EnhancedOllamaClient - ERROR - Generation failed: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-21 12:29:21 - AIAnalysis.EnhancedOllamaClient - INFO - Starting sub-topic analysis for 3 topics
2025-07-21 12:29:21 - AIAnalysis.EnhancedOllamaClient - INFO - Analyzing sub-topic 1/3: Society
2025-07-21 12:29:21 - AIAnalysis.EnhancedOllamaClient - ERROR - Error in progressive analysis: 'analysis_framework'
2025-07-21 12:29:34 - AIAnalysis - INFO - Application closing
2025-07-21 12:29:35 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 12:36:26 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:36:26 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:36:26 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:36:26 - AIAnalysis - INFO - File logging: True
2025-07-21 12:36:26 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 12:36:27 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 12:36:27 - AIAnalysis - INFO - Starting application main loop
2025-07-21 12:41:11 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:41:11 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:41:11 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:41:11 - AIAnalysis - INFO - File logging: True
2025-07-21 12:41:11 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 12:41:12 - AIAnalysis - ERROR - Failed to initialize application: unknown option "-minsize"
2025-07-21 12:41:52 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:41:52 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:41:52 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:41:52 - AIAnalysis - INFO - File logging: True
2025-07-21 12:41:52 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 12:41:53 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 12:41:53 - AIAnalysis - INFO - Starting application main loop
2025-07-21 12:42:32 - AIAnalysis - INFO - Application closing
2025-07-21 12:42:32 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 12:42:49 - AIAnalysis - INFO - Application closing
2025-07-21 12:42:49 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 12:42:53 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:42:53 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:42:53 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:42:53 - AIAnalysis - INFO - File logging: True
2025-07-21 12:42:53 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 12:42:54 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 12:42:54 - AIAnalysis - INFO - Starting application main loop
2025-07-21 12:44:11 - AIAnalysis - INFO - Application closing
2025-07-21 12:44:11 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 12:44:29 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:44:29 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:44:29 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:44:29 - AIAnalysis - INFO - File logging: True
2025-07-21 12:44:29 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 12:44:29 - AIAnalysis - ERROR - Failed to initialize application: unknown option "-stretch"
2025-07-21 12:45:31 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:45:31 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:45:31 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:45:31 - AIAnalysis - INFO - File logging: True
2025-07-21 12:45:31 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 12:45:31 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 12:45:31 - AIAnalysis - INFO - Starting application main loop
2025-07-21 12:46:49 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 12:46:54 - AIAnalysis - INFO - Logging system initialized
2025-07-21 12:46:54 - AIAnalysis - INFO - Log level: INFO
2025-07-21 12:46:54 - AIAnalysis - INFO - Console logging: True
2025-07-21 12:46:54 - AIAnalysis - INFO - File logging: True
2025-07-21 12:46:54 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 12:46:54 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 12:46:54 - AIAnalysis - INFO - Starting application main loop
2025-07-21 12:47:10 - AIAnalysis - INFO - Application closing
2025-07-21 12:47:10 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 13:13:59 - AIAnalysis - INFO - Logging system initialized
2025-07-21 13:13:59 - AIAnalysis - INFO - Log level: INFO
2025-07-21 13:13:59 - AIAnalysis - INFO - Console logging: True
2025-07-21 13:13:59 - AIAnalysis - INFO - File logging: True
2025-07-21 13:13:59 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 13:14:00 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 13:14:00 - AIAnalysis - INFO - Starting application main loop
2025-07-21 13:14:22 - AIAnalysis - INFO - Application closing
2025-07-21 13:14:23 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 13:14:53 - AIAnalysis - INFO - Logging system initialized
2025-07-21 13:14:53 - AIAnalysis - INFO - Log level: INFO
2025-07-21 13:14:53 - AIAnalysis - INFO - Console logging: True
2025-07-21 13:14:53 - AIAnalysis - INFO - File logging: True
2025-07-21 13:14:53 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 13:14:54 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 13:14:54 - AIAnalysis - INFO - Starting application main loop
2025-07-21 13:15:19 - AIAnalysis - INFO - Application closing
2025-07-21 13:15:19 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 13:24:13 - AIAnalysis - INFO - Logging system initialized
2025-07-21 13:24:13 - AIAnalysis - INFO - Log level: INFO
2025-07-21 13:24:13 - AIAnalysis - INFO - Console logging: True
2025-07-21 13:24:13 - AIAnalysis - INFO - File logging: True
2025-07-21 13:24:13 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 13:24:14 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 13:24:14 - AIAnalysis - INFO - Starting application main loop
2025-07-21 13:26:16 - AIAnalysis - INFO - Application closing
2025-07-21 13:26:16 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 13:33:45 - AIAnalysis - INFO - Logging system initialized
2025-07-21 13:33:45 - AIAnalysis - INFO - Log level: INFO
2025-07-21 13:33:45 - AIAnalysis - INFO - Console logging: True
2025-07-21 13:33:45 - AIAnalysis - INFO - File logging: True
2025-07-21 13:33:45 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 13:33:46 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 13:33:46 - AIAnalysis - INFO - Starting application main loop
2025-07-21 13:35:56 - AIAnalysis - INFO - Application closing
2025-07-21 13:35:56 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 13:37:21 - AIAnalysis - INFO - Logging system initialized
2025-07-21 13:37:21 - AIAnalysis - INFO - Log level: INFO
2025-07-21 13:37:21 - AIAnalysis - INFO - Console logging: True
2025-07-21 13:37:21 - AIAnalysis - INFO - File logging: True
2025-07-21 13:37:21 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 13:37:21 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 13:37:21 - AIAnalysis - INFO - Starting application main loop
2025-07-21 13:37:39 - AIAnalysis - INFO - Application closing
2025-07-21 13:37:39 - AIAnalysis - INFO - Application cleanup completed
2025-07-21 15:26:07 - AIAnalysis - INFO - Logging system initialized
2025-07-21 15:26:07 - AIAnalysis - INFO - Log level: INFO
2025-07-21 15:26:07 - AIAnalysis - INFO - Console logging: True
2025-07-21 15:26:07 - AIAnalysis - INFO - File logging: True
2025-07-21 15:26:07 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-21 15:26:08 - AIAnalysis - INFO - No scrollable frames found, but performance system is ready
2025-07-21 15:26:08 - AIAnalysis - INFO - Application initialized successfully
2025-07-21 15:26:08 - AIAnalysis - INFO - Starting application main loop
2025-07-21 15:26:32 - AIAnalysis - INFO - Application closing
2025-07-21 15:26:32 - AIAnalysis - INFO - Application cleanup completed
