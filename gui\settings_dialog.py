"""
Settings dialog for AI Analysis Program
Provides GUI for configuration management
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Optional
import json

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import show_error, show_info

class SettingsDialog(LoggerMixin):
    """Settings configuration dialog"""
    
    def __init__(self, parent: tk.Widget, config: Config):
        self.parent = parent
        self.config = config
        self.result = None
        self.temp_config = {}
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Settings")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.center_dialog()
        
        self.setup_ui()
        
        # Load current configuration
        self.load_current_config()
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def center_dialog(self):
        """Center the dialog on screen"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
    
    def setup_ui(self):
        """Set up the settings dialog UI"""
        # Main container
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for different setting categories
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Ollama settings tab
        self.setup_ollama_tab()
        
        # Analysis settings tab
        self.setup_analysis_tab()
        
        # Export settings tab
        self.setup_export_tab()
        
        # GUI settings tab
        self.setup_gui_tab()
        
        # Performance settings tab
        self.setup_performance_tab()
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # Buttons
        ttk.Button(
            button_frame,
            text="Reset to Defaults",
            command=self.reset_to_defaults
        ).pack(side=tk.LEFT)
        
        ttk.Button(
            button_frame,
            text="Import Settings",
            command=self.import_settings
        ).pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(
            button_frame,
            text="Export Settings",
            command=self.export_settings
        ).pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel_clicked
        ).pack(side=tk.RIGHT)
        
        ttk.Button(
            button_frame,
            text="Apply",
            command=self.apply_clicked
        ).pack(side=tk.RIGHT, padx=(0, 5))
        
        ttk.Button(
            button_frame,
            text="OK",
            command=self.ok_clicked
        ).pack(side=tk.RIGHT, padx=(0, 5))
    
    def setup_ollama_tab(self):
        """Set up Ollama settings tab"""
        ollama_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(ollama_frame, text="Ollama")
        
        # Base URL
        ttk.Label(ollama_frame, text="Base URL:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.ollama_url_var = tk.StringVar()
        ttk.Entry(ollama_frame, textvariable=self.ollama_url_var, width=40).grid(row=0, column=1, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # Default model
        ttk.Label(ollama_frame, text="Default Model:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.ollama_model_var = tk.StringVar()
        ttk.Entry(ollama_frame, textvariable=self.ollama_model_var, width=40).grid(row=1, column=1, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # Timeout
        ttk.Label(ollama_frame, text="Timeout (seconds):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.ollama_timeout_var = tk.IntVar()
        ttk.Spinbox(ollama_frame, from_=5, to=300, textvariable=self.ollama_timeout_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Temperature
        ttk.Label(ollama_frame, text="Temperature:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.ollama_temp_var = tk.DoubleVar()
        ttk.Scale(ollama_frame, from_=0.0, to=2.0, variable=self.ollama_temp_var, orient=tk.HORIZONTAL, length=200).grid(row=3, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Max tokens
        ttk.Label(ollama_frame, text="Max Tokens:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.ollama_tokens_var = tk.IntVar()
        ttk.Spinbox(ollama_frame, from_=100, to=8192, textvariable=self.ollama_tokens_var, width=10).grid(row=4, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ollama_frame.columnconfigure(1, weight=1)
    
    def setup_analysis_tab(self):
        """Set up Analysis settings tab"""
        analysis_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(analysis_frame, text="Analysis")
        
        # Max recursive depth
        ttk.Label(analysis_frame, text="Max Recursive Depth:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.analysis_depth_var = tk.IntVar()
        ttk.Spinbox(analysis_frame, from_=1, to=10, textvariable=self.analysis_depth_var, width=10).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Connection threshold
        ttk.Label(analysis_frame, text="Connection Threshold:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.analysis_threshold_var = tk.DoubleVar()
        ttk.Scale(analysis_frame, from_=0.0, to=1.0, variable=self.analysis_threshold_var, orient=tk.HORIZONTAL, length=200).grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Batch size
        ttk.Label(analysis_frame, text="Batch Size:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.analysis_batch_var = tk.IntVar()
        ttk.Spinbox(analysis_frame, from_=1, to=50, textvariable=self.analysis_batch_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Enable caching
        self.analysis_caching_var = tk.BooleanVar()
        ttk.Checkbutton(analysis_frame, text="Enable result caching", variable=self.analysis_caching_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        analysis_frame.columnconfigure(1, weight=1)
    
    def setup_export_tab(self):
        """Set up Export settings tab"""
        export_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(export_frame, text="Export")
        
        # Default format
        ttk.Label(export_frame, text="Default Format:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.export_format_var = tk.StringVar()
        format_combo = ttk.Combobox(export_frame, textvariable=self.export_format_var, values=["markdown", "json", "txt"], state="readonly")
        format_combo.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Output directory
        ttk.Label(export_frame, text="Output Directory:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.export_dir_var = tk.StringVar()
        dir_frame = ttk.Frame(export_frame)
        dir_frame.grid(row=1, column=1, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        ttk.Entry(dir_frame, textvariable=self.export_dir_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(dir_frame, text="Browse", command=self.browse_export_dir).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Include metadata
        self.export_metadata_var = tk.BooleanVar()
        ttk.Checkbutton(export_frame, text="Include metadata in exports", variable=self.export_metadata_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Timestamp format
        ttk.Label(export_frame, text="Timestamp Format:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.export_timestamp_var = tk.StringVar()
        ttk.Entry(export_frame, textvariable=self.export_timestamp_var, width=30).grid(row=3, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        export_frame.columnconfigure(1, weight=1)
    
    def setup_gui_tab(self):
        """Set up GUI settings tab"""
        gui_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(gui_frame, text="Interface")
        
        # Theme
        ttk.Label(gui_frame, text="Theme:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.gui_theme_var = tk.StringVar()
        theme_combo = ttk.Combobox(gui_frame, textvariable=self.gui_theme_var, values=["default", "dark", "light"], state="readonly")
        theme_combo.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Font size
        ttk.Label(gui_frame, text="Font Size:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.gui_font_var = tk.IntVar()
        ttk.Spinbox(gui_frame, from_=8, to=16, textvariable=self.gui_font_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Auto save
        self.gui_autosave_var = tk.BooleanVar()
        ttk.Checkbutton(gui_frame, text="Auto-save work", variable=self.gui_autosave_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Save interval
        ttk.Label(gui_frame, text="Save Interval (seconds):").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.gui_save_interval_var = tk.IntVar()
        ttk.Spinbox(gui_frame, from_=60, to=3600, textvariable=self.gui_save_interval_var, width=10).grid(row=3, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        gui_frame.columnconfigure(1, weight=1)
    
    def setup_performance_tab(self):
        """Set up Performance settings tab"""
        perf_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(perf_frame, text="Performance")
        
        # Max concurrent analyses
        ttk.Label(perf_frame, text="Max Concurrent Analyses:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.perf_concurrent_var = tk.IntVar()
        ttk.Spinbox(perf_frame, from_=1, to=16, textvariable=self.perf_concurrent_var, width=10).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Cache TTL
        ttk.Label(perf_frame, text="Cache TTL (hours):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.perf_cache_ttl_var = tk.IntVar()
        ttk.Spinbox(perf_frame, from_=1, to=168, textvariable=self.perf_cache_ttl_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Max memory
        ttk.Label(perf_frame, text="Max Memory (MB):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.perf_memory_var = tk.IntVar()
        ttk.Spinbox(perf_frame, from_=128, to=4096, textvariable=self.perf_memory_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Enable streaming
        self.perf_streaming_var = tk.BooleanVar()
        ttk.Checkbutton(perf_frame, text="Enable streaming responses", variable=self.perf_streaming_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # UI update interval
        ttk.Label(perf_frame, text="UI Update Interval (ms):").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.perf_ui_interval_var = tk.IntVar()
        ttk.Spinbox(perf_frame, from_=50, to=1000, textvariable=self.perf_ui_interval_var, width=10).grid(row=4, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        perf_frame.columnconfigure(1, weight=1)
    
    def load_current_config(self):
        """Load current configuration into UI"""
        # Ollama settings
        self.ollama_url_var.set(self.config.get("ollama.base_url", "http://localhost:11434"))
        self.ollama_model_var.set(self.config.get("ollama.default_model", "llama2"))
        self.ollama_timeout_var.set(self.config.get("ollama.timeout", 30))
        self.ollama_temp_var.set(self.config.get("ollama.temperature", 0.7))
        self.ollama_tokens_var.set(self.config.get("ollama.max_tokens", 2048))
        
        # Analysis settings
        self.analysis_depth_var.set(self.config.get("analysis.max_recursive_depth", 5))
        self.analysis_threshold_var.set(self.config.get("analysis.connection_threshold", 0.7))
        self.analysis_batch_var.set(self.config.get("analysis.batch_size", 10))
        self.analysis_caching_var.set(self.config.get("analysis.enable_caching", True))
        
        # Export settings
        self.export_format_var.set(self.config.get("export.default_format", "markdown"))
        self.export_dir_var.set(self.config.get("export.output_directory", "exports"))
        self.export_metadata_var.set(self.config.get("export.include_metadata", True))
        self.export_timestamp_var.set(self.config.get("export.timestamp_format", "%Y%m%d_%H%M%S"))
        
        # GUI settings
        self.gui_theme_var.set(self.config.get("gui.theme", "default"))
        self.gui_font_var.set(self.config.get("gui.font_size", 10))
        self.gui_autosave_var.set(self.config.get("gui.auto_save", True))
        self.gui_save_interval_var.set(self.config.get("gui.save_interval", 300))
        
        # Performance settings
        self.perf_concurrent_var.set(self.config.get("performance.max_concurrent_analyses", 4))
        self.perf_cache_ttl_var.set(self.config.get("performance.cache_ttl_hours", 24))
        self.perf_memory_var.set(self.config.get("performance.max_memory_mb", 512))
        self.perf_streaming_var.set(self.config.get("performance.enable_streaming", True))
        self.perf_ui_interval_var.set(self.config.get("performance.ui_update_interval_ms", 100))
    
    def collect_settings(self) -> Dict[str, Any]:
        """Collect settings from UI"""
        return {
            "ollama": {
                "base_url": self.ollama_url_var.get(),
                "default_model": self.ollama_model_var.get(),
                "timeout": self.ollama_timeout_var.get(),
                "temperature": self.ollama_temp_var.get(),
                "max_tokens": self.ollama_tokens_var.get()
            },
            "analysis": {
                "max_recursive_depth": self.analysis_depth_var.get(),
                "connection_threshold": self.analysis_threshold_var.get(),
                "batch_size": self.analysis_batch_var.get(),
                "enable_caching": self.analysis_caching_var.get()
            },
            "export": {
                "default_format": self.export_format_var.get(),
                "output_directory": self.export_dir_var.get(),
                "include_metadata": self.export_metadata_var.get(),
                "timestamp_format": self.export_timestamp_var.get()
            },
            "gui": {
                "theme": self.gui_theme_var.get(),
                "font_size": self.gui_font_var.get(),
                "auto_save": self.gui_autosave_var.get(),
                "save_interval": self.gui_save_interval_var.get()
            },
            "performance": {
                "max_concurrent_analyses": self.perf_concurrent_var.get(),
                "cache_ttl_hours": self.perf_cache_ttl_var.get(),
                "max_memory_mb": self.perf_memory_var.get(),
                "enable_streaming": self.perf_streaming_var.get(),
                "ui_update_interval_ms": self.perf_ui_interval_var.get()
            }
        }
    
    def browse_export_dir(self):
        """Browse for export directory"""
        directory = filedialog.askdirectory(title="Select Export Directory")
        if directory:
            self.export_dir_var.set(directory)
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Reset all settings to default values?"):
            # Reset to default config
            default_config = Config.DEFAULT_CONFIG
            
            # Update UI with defaults
            self.ollama_url_var.set(default_config["ollama"]["base_url"])
            self.ollama_model_var.set(default_config["ollama"]["default_model"])
            self.ollama_timeout_var.set(default_config["ollama"]["timeout"])
            self.ollama_temp_var.set(default_config["ollama"]["temperature"])
            self.ollama_tokens_var.set(default_config["ollama"]["max_tokens"])
            
            self.analysis_depth_var.set(default_config["analysis"]["max_recursive_depth"])
            self.analysis_threshold_var.set(default_config["analysis"]["connection_threshold"])
            self.analysis_batch_var.set(default_config["analysis"]["batch_size"])
            self.analysis_caching_var.set(default_config["analysis"]["enable_caching"])
            
            self.export_format_var.set(default_config["export"]["default_format"])
            self.export_dir_var.set(default_config["export"]["output_directory"])
            self.export_metadata_var.set(default_config["export"]["include_metadata"])
            self.export_timestamp_var.set(default_config["export"]["timestamp_format"])
            
            self.gui_theme_var.set(default_config["gui"]["theme"])
            self.gui_font_var.set(default_config["gui"]["font_size"])
            self.gui_autosave_var.set(default_config["gui"]["auto_save"])
            self.gui_save_interval_var.set(default_config["gui"]["save_interval"])
    
    def import_settings(self):
        """Import settings from file"""
        filename = filedialog.askopenfilename(
            title="Import Settings",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)
                
                # Update config and reload UI
                self.config.config_data.update(imported_config)
                self.load_current_config()
                
                show_info("Import Successful", f"Settings imported from {filename}")
                
            except Exception as e:
                show_error("Import Error", f"Failed to import settings: {str(e)}")
    
    def export_settings(self):
        """Export current settings to file"""
        filename = filedialog.asksaveasfilename(
            title="Export Settings",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                settings = self.collect_settings()
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2)
                
                show_info("Export Successful", f"Settings exported to {filename}")
                
            except Exception as e:
                show_error("Export Error", f"Failed to export settings: {str(e)}")
    
    def ok_clicked(self):
        """Handle OK button click"""
        self.apply_settings()
        self.result = "ok"
        self.dialog.destroy()
    
    def apply_clicked(self):
        """Handle Apply button click"""
        self.apply_settings()
    
    def apply_settings(self):
        """Apply settings to configuration"""
        try:
            settings = self.collect_settings()
            
            # Update configuration
            for section, section_settings in settings.items():
                for key, value in section_settings.items():
                    self.config.set(f"{section}.{key}", value)
            
            # Save configuration
            self.config.save_config()
            
            # Ensure directories exist
            self.config.ensure_directories()
            
            show_info("Settings Applied", "Settings have been applied successfully.")
            
        except Exception as e:
            show_error("Apply Error", f"Failed to apply settings: {str(e)}")
    
    def cancel_clicked(self):
        """Handle Cancel button click"""
        self.result = "cancel"
        self.dialog.destroy()
