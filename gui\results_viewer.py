"""
Results viewer for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, List
import json
from datetime import datetime

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import show_error, show_info, format_timestamp
from analysis.mindmap_generator import MindMapGenerator

class ResultsViewer(LoggerMixin):
    """Panel for displaying analysis results"""
    
    def __init__(self, parent: tk.Widget, config: Config, theme=None):
        self.parent = parent
        self.config = config
        self.theme = theme
        self.current_results = None
        self.mindmap_generator = MindMapGenerator(config)
        self.mindmap_path = None
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the results viewer interface"""
        # Toolbar with more compact layout
        toolbar_frame = ttk.Frame(self.parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 3))
        
        # Left side buttons
        left_buttons = ttk.Frame(toolbar_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(
            left_buttons,
            text="📄 Markdown",
            command=self.export_markdown
        ).pack(side=tk.LEFT, padx=(0, 3))
        
        ttk.Button(
            left_buttons,
            text="📊 JSON",
            command=self.export_json
        ).pack(side=tk.LEFT, padx=(0, 3))
        
        ttk.Button(
            left_buttons,
            text="🗑️ Clear",
            command=self.clear_results
        ).pack(side=tk.LEFT, padx=(0, 3))
        
        # Right side buttons
        right_buttons = ttk.Frame(toolbar_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(
            right_buttons,
            text="🔗 Connections",
            command=self.view_connections
        ).pack(side=tk.RIGHT)
        
        # Results display area with enhanced layout
        display_frame = ttk.Frame(self.parent)
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for tabbed results with better styling
        self.notebook = ttk.Notebook(display_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Summary tab
        self.summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.summary_frame, text="📝 Summary")
        self.setup_summary_tab()
        
        # Detailed results tab
        self.details_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.details_frame, text="📋 Details")
        self.setup_details_tab()
        
        # Connections tab
        self.connections_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.connections_frame, text="🔗 Connections")
        self.setup_connections_tab()
        
        # Mind map tab
        self.mindmap_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.mindmap_frame, text="🧠 Mind Map")
        self.setup_mindmap_tab()

        # Raw data tab
        self.raw_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.raw_frame, text="⚙️ Raw Data")
        self.setup_raw_tab()
    
    def setup_summary_tab(self):
        """Set up the summary tab"""
        # Summary text widget with enhanced scrolling
        text_frame = ttk.Frame(self.summary_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)
        
        # Create scrollbars
        scrollbar_y = ttk.Scrollbar(text_frame)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        
        scrollbar_x = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Text widget with better font and sizing
        self.summary_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            yscrollcommand=scrollbar_y.set,
            xscrollcommand=scrollbar_x.set,
            font=("Consolas", 10),  # Better font for readability
            padx=10,
            pady=10,
            bg="#f8f9fa",  # Light background
            relief=tk.FLAT,
            bd=1
        )
        self.summary_text.pack(fill=tk.BOTH, expand=True)


        # Configure scrollbars
        scrollbar_y.configure(command=self.summary_text.yview)
        scrollbar_x.configure(command=self.summary_text.xview)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.summary_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            yscrollcommand=scrollbar_y.set,
            xscrollcommand=scrollbar_x.set,
            font=('Arial', 10),
            state=tk.DISABLED
        )
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar_y.config(command=self.summary_text.yview)
        scrollbar_x.config(command=self.summary_text.xview)
    
    def setup_details_tab(self):
        """Set up the detailed results tab"""
        # Create treeview for hierarchical display
        tree_frame = ttk.Frame(self.details_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Treeview with scrollbars
        tree_scroll_y = ttk.Scrollbar(tree_frame)
        tree_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        
        tree_scroll_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL)
        tree_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.details_tree = ttk.Treeview(
            tree_frame,
            yscrollcommand=tree_scroll_y.set,
            xscrollcommand=tree_scroll_x.set,
            columns=('Type', 'Content'),
            show='tree headings'
        )
        self.details_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        tree_scroll_y.config(command=self.details_tree.yview)
        tree_scroll_x.config(command=self.details_tree.xview)
        
        # Configure columns
        self.details_tree.heading('#0', text='Topic/Section')
        self.details_tree.heading('Type', text='Type')
        self.details_tree.heading('Content', text='Content Preview')
        
        self.details_tree.column('#0', width=200)
        self.details_tree.column('Type', width=100)
        self.details_tree.column('Content', width=300)
        
        # Bind double-click to view full content
        self.details_tree.bind('<Double-1>', self.view_full_content)
    
    def setup_connections_tab(self):
        """Set up the connections tab"""
        connections_frame = ttk.Frame(self.connections_frame)
        connections_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Connections text widget
        conn_scroll_y = ttk.Scrollbar(connections_frame)
        conn_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.connections_text = tk.Text(
            connections_frame,
            wrap=tk.WORD,
            yscrollcommand=conn_scroll_y.set,
            font=('Arial', 10),
            state=tk.DISABLED
        )
        self.connections_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        conn_scroll_y.config(command=self.connections_text.yview)
    
    def setup_raw_tab(self):
        """Set up the raw data tab"""
        raw_frame = ttk.Frame(self.raw_frame)
        raw_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Raw JSON display
        raw_scroll_y = ttk.Scrollbar(raw_frame)
        raw_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        
        raw_scroll_x = ttk.Scrollbar(raw_frame, orient=tk.HORIZONTAL)
        raw_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.raw_text = tk.Text(
            raw_frame,
            wrap=tk.NONE,
            yscrollcommand=raw_scroll_y.set,
            xscrollcommand=raw_scroll_x.set,
            font=('Courier', 9),
            state=tk.DISABLED
        )
        self.raw_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        raw_scroll_y.config(command=self.raw_text.yview)
        raw_scroll_x.config(command=self.raw_text.xview)

    def setup_mindmap_tab(self):
        """Set up the mind map tab"""
        mindmap_container = ttk.Frame(self.mindmap_frame)
        mindmap_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Toolbar for mind map
        mindmap_toolbar = ttk.Frame(mindmap_container)
        mindmap_toolbar.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(
            mindmap_toolbar,
            text="Generate Mind Map",
            command=self.generate_mindmap
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            mindmap_toolbar,
            text="Open in Browser",
            command=self.open_mindmap_in_browser
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            mindmap_toolbar,
            text="Export Mind Map",
            command=self.export_mindmap
        ).pack(side=tk.LEFT)

        # Status label
        self.mindmap_status_var = tk.StringVar(value="No mind map generated")
        self.mindmap_status_label = ttk.Label(
            mindmap_toolbar,
            textvariable=self.mindmap_status_var,
            foreground="gray"
        )
        self.mindmap_status_label.pack(side=tk.RIGHT)

        # Mind map display area
        self.mindmap_display_frame = ttk.LabelFrame(
            mindmap_container,
            text="Mind Map Preview",
            padding=10
        )
        self.mindmap_display_frame.pack(fill=tk.BOTH, expand=True)

        # Placeholder text
        self.mindmap_placeholder = ttk.Label(
            self.mindmap_display_frame,
            text="Click 'Generate Mind Map' to create an interactive visualization\nof your analysis results.",
            justify=tk.CENTER,
            foreground="gray"
        )
        self.mindmap_placeholder.pack(expand=True)
    
    def display_results(self, results: Dict[str, Any]):
        """Display analysis results"""
        self.current_results = results
        
        # Update all tabs
        self.update_summary_tab(results)
        self.update_details_tab(results)
        self.update_connections_tab(results)
        self.update_mindmap_tab(results)
        self.update_raw_tab(results)
        
        # Switch to summary tab
        self.notebook.select(0)
    
    def update_summary_tab(self, results: Dict[str, Any]):
        """Update the summary tab"""
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.delete(1.0, tk.END)
        
        # Generate summary
        summary = self.generate_summary(results)
        self.summary_text.insert(tk.END, summary)
        
        self.summary_text.config(state=tk.DISABLED)
    
    def update_details_tab(self, results: Dict[str, Any]):
        """Update the details tab"""
        # Clear existing items
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # Populate tree based on analysis type
        analysis_type = results.get('type', 'unknown')
        
        if analysis_type == 'iterative':
            self.populate_iterative_tree(results)
        elif analysis_type == 'recursive':
            self.populate_recursive_tree(results)
        elif analysis_type in ['comparative', 'swot', 'temporal']:
            self.populate_advanced_tree(results)
    
    def update_connections_tab(self, results: Dict[str, Any]):
        """Update the connections tab"""
        self.connections_text.config(state=tk.NORMAL)
        self.connections_text.delete(1.0, tk.END)
        
        connections = results.get('connections', '')
        if connections:
            self.connections_text.insert(tk.END, connections)
        else:
            self.connections_text.insert(tk.END, "No connections analysis available.")
        
        self.connections_text.config(state=tk.DISABLED)

    def update_mindmap_tab(self, results: Dict[str, Any]):
        """Update the mind map tab"""
        # Reset mind map state
        self.mindmap_path = None
        self.mindmap_status_var.set("Click 'Generate Mind Map' to create visualization")

        # Show placeholder
        self.mindmap_placeholder.pack(expand=True)

    def generate_mindmap(self):
        """Generate interactive mind map"""
        if not self.current_results:
            show_error("No Results", "No analysis results available for mind map generation.")
            return

        try:
            self.mindmap_status_var.set("Generating mind map...")
            self.parent.update_idletasks()

            # Generate mind map HTML file
            self.mindmap_path = self.mindmap_generator.generate_interactive_mindmap(self.current_results)

            self.mindmap_status_var.set(f"Mind map generated: {self.mindmap_path}")

            # Hide placeholder and show success message
            self.mindmap_placeholder.pack_forget()

            success_frame = ttk.Frame(self.mindmap_display_frame)
            success_frame.pack(expand=True)

            ttk.Label(
                success_frame,
                text="✓ Interactive Mind Map Generated Successfully!",
                foreground="green",
                font=('Arial', 12, 'bold')
            ).pack(pady=20)

            ttk.Label(
                success_frame,
                text="Click 'Open in Browser' to view the interactive mind map.",
                foreground="gray"
            ).pack()

            show_info("Mind Map Generated", f"Interactive mind map has been generated.\nFile: {self.mindmap_path}")

        except Exception as e:
            self.logger.error(f"Failed to generate mind map: {e}")
            self.mindmap_status_var.set("Failed to generate mind map")
            show_error("Generation Error", f"Failed to generate mind map:\n{str(e)}")

    def open_mindmap_in_browser(self):
        """Open mind map in default browser"""
        if not self.mindmap_path:
            show_error("No Mind Map", "Please generate a mind map first.")
            return

        try:
            import webbrowser
            import os

            # Convert to absolute path and open in browser
            abs_path = os.path.abspath(self.mindmap_path)
            file_url = f"file:///{abs_path.replace(os.sep, '/')}"
            webbrowser.open(file_url)

            self.logger.info(f"Opened mind map in browser: {file_url}")

        except Exception as e:
            self.logger.error(f"Failed to open mind map in browser: {e}")
            show_error("Browser Error", f"Failed to open mind map in browser:\n{str(e)}")

    def export_mindmap(self):
        """Export mind map file"""
        if not self.mindmap_path:
            show_error("No Mind Map", "Please generate a mind map first.")
            return

        from tkinter import filedialog
        import shutil

        # Ask user where to save
        export_path = filedialog.asksaveasfilename(
            title="Export Mind Map",
            defaultextension=".html",
            filetypes=[("HTML files", "*.html"), ("All files", "*.*")]
        )

        if export_path:
            try:
                shutil.copy2(self.mindmap_path, export_path)
                show_info("Export Successful", f"Mind map exported to:\n{export_path}")
            except Exception as e:
                show_error("Export Error", f"Failed to export mind map:\n{str(e)}")
    
    def update_raw_tab(self, results: Dict[str, Any]):
        """Update the raw data tab"""
        self.raw_text.config(state=tk.NORMAL)
        self.raw_text.delete(1.0, tk.END)
        
        # Pretty print JSON
        try:
            formatted_json = json.dumps(results, indent=2, ensure_ascii=False)
            self.raw_text.insert(tk.END, formatted_json)
        except Exception as e:
            self.raw_text.insert(tk.END, f"Error formatting results: {str(e)}")
        
        self.raw_text.config(state=tk.DISABLED)
    
    def generate_summary(self, results: Dict[str, Any]) -> str:
        """Generate a summary of the analysis results"""
        summary_lines = []
        
        # Header
        analysis_type = results.get('type', 'Unknown').title()
        topic = results.get('topic', 'Unknown Topic')
        
        summary_lines.append(f"# {analysis_type} Analysis Results")
        summary_lines.append(f"**Topic:** {topic}")
        summary_lines.append("")
        
        # Metadata
        metadata = results.get('metadata', {})
        if metadata:
            summary_lines.append("## Analysis Information")
            if 'timestamp' in metadata:
                timestamp = metadata['timestamp']
                summary_lines.append(f"**Completed:** {timestamp}")
            if 'model' in metadata:
                summary_lines.append(f"**AI Model:** {metadata['model']}")
            if 'max_depth' in metadata:
                summary_lines.append(f"**Max Depth:** {metadata['max_depth']}")
            summary_lines.append("")
        
        # Main analysis
        if 'main_analysis' in results:
            summary_lines.append("## Main Topic Analysis")
            summary_lines.append(results['main_analysis'])
            summary_lines.append("")
        
        # Sub-analyses count
        sub_analyses = results.get('sub_analyses', [])
        if sub_analyses:
            summary_lines.append(f"## Sub-topic Analyses ({len(sub_analyses)} topics)")
            for i, sub_analysis in enumerate(sub_analyses[:3], 1):  # Show first 3
                topic_name = sub_analysis.get('topic', f'Sub-topic {i}')
                analysis_preview = sub_analysis.get('analysis', '')[:200] + "..."
                summary_lines.append(f"**{i}. {topic_name}**")
                summary_lines.append(analysis_preview)
                summary_lines.append("")
            
            if len(sub_analyses) > 3:
                summary_lines.append(f"... and {len(sub_analyses) - 3} more sub-topics")
                summary_lines.append("")
        
        # Connections summary
        connections = results.get('connections', '')
        if connections:
            summary_lines.append("## Key Connections")
            connections_preview = connections[:300] + "..." if len(connections) > 300 else connections
            summary_lines.append(connections_preview)
            summary_lines.append("")

        # Advanced analysis summaries
        analysis_type = results.get('type', '')
        if analysis_type == 'comparative':
            self._add_comparative_summary(results, summary_lines)
        elif analysis_type == 'swot':
            self._add_swot_summary(results, summary_lines)
        elif analysis_type == 'temporal':
            self._add_temporal_summary(results, summary_lines)

        return "\n".join(summary_lines)

    def _add_comparative_summary(self, results: Dict[str, Any], summary_lines: List[str]):
        """Add comparative analysis summary"""
        topics = results.get('topics', [])
        comparison_matrix = results.get('comparison_matrix', {})

        summary_lines.append(f"## Comparative Analysis ({len(topics)} topics)")

        if comparison_matrix and 'scores' in comparison_matrix:
            # Find best performing topic
            scores = comparison_matrix['scores']
            if scores:
                topic_averages = {}
                for topic, topic_scores in scores.items():
                    if isinstance(topic_scores, dict):
                        avg_score = sum(topic_scores.values()) / len(topic_scores)
                        topic_averages[topic] = avg_score

                if topic_averages:
                    best_topic = max(topic_averages, key=topic_averages.get)
                    summary_lines.append(f"**Best Overall Performance:** {best_topic} (avg: {topic_averages[best_topic]:.1f})")

        recommendations = results.get('recommendations', '')
        if recommendations:
            rec_preview = recommendations[:200] + "..." if len(recommendations) > 200 else recommendations
            summary_lines.append(f"**Key Recommendations:** {rec_preview}")

        summary_lines.append("")

    def _add_swot_summary(self, results: Dict[str, Any], summary_lines: List[str]):
        """Add SWOT analysis summary"""
        swot_matrix = results.get('swot_matrix', {})

        summary_lines.append("## SWOT Analysis Summary")

        for category in ['strengths', 'weaknesses', 'opportunities', 'threats']:
            items = swot_matrix.get(category, [])
            if items:
                summary_lines.append(f"**{category.title()}:** {len(items)} items identified")
                # Show top 2 items
                for item in items[:2]:
                    summary_lines.append(f"  • {item}")
                if len(items) > 2:
                    summary_lines.append(f"  • ... and {len(items) - 2} more")

        recommendations = results.get('strategic_recommendations', [])
        if recommendations:
            summary_lines.append(f"**Strategic Recommendations:** {len(recommendations)} strategies identified")
            if recommendations:
                summary_lines.append(f"  • {recommendations[0]}")

        summary_lines.append("")

    def _add_temporal_summary(self, results: Dict[str, Any], summary_lines: List[str]):
        """Add temporal analysis summary"""
        timeline = results.get('timeline', [])
        trends = results.get('trends', {})

        summary_lines.append("## Temporal Analysis Summary")

        if timeline:
            summary_lines.append(f"**Timeline:** {len(timeline)} key events identified")
            # Show first and last events
            if len(timeline) >= 2:
                first_event = timeline[0]
                last_event = timeline[-1]
                summary_lines.append(f"  • From: {first_event.get('period', 'Unknown')} - {first_event.get('description', '')[:50]}...")
                summary_lines.append(f"  • To: {last_event.get('period', 'Unknown')} - {last_event.get('description', '')[:50]}...")

        if trends:
            summary_lines.append("**Key Trends:**")
            for trend_type, trend_items in trends.items():
                if trend_items:
                    summary_lines.append(f"  • {trend_type.title()}: {len(trend_items)} trends")

        predictions = results.get('predictions', '')
        if predictions:
            pred_preview = predictions[:150] + "..." if len(predictions) > 150 else predictions
            summary_lines.append(f"**Future Outlook:** {pred_preview}")

        summary_lines.append("")
    
    def populate_iterative_tree(self, results: Dict[str, Any]):
        """Populate tree for iterative analysis results"""
        # Main analysis
        if 'main_analysis' in results:
            main_item = self.details_tree.insert(
                '', 'end',
                text=results.get('topic', 'Main Topic'),
                values=('Main Analysis', self.truncate_text(results['main_analysis']))
            )
        
        # Sub-analyses
        sub_analyses = results.get('sub_analyses', [])
        if sub_analyses:
            sub_parent = self.details_tree.insert('', 'end', text='Sub-topic Analyses', values=('Container', ''))
            
            for sub_analysis in sub_analyses:
                topic = sub_analysis.get('topic', 'Unknown')
                analysis = sub_analysis.get('analysis', '')
                
                self.details_tree.insert(
                    sub_parent, 'end',
                    text=topic,
                    values=('Sub-analysis', self.truncate_text(analysis))
                )
    
    def populate_recursive_tree(self, results: Dict[str, Any]):
        """Populate tree for recursive analysis results"""
        analysis_tree = results.get('analysis_tree', {})
        if analysis_tree:
            self.add_recursive_node('', analysis_tree)
    
    def add_recursive_node(self, parent: str, node: Dict[str, Any]):
        """Add a recursive analysis node to the tree"""
        topic = node.get('topic', 'Unknown')
        analysis = node.get('analysis', '')
        depth = node.get('depth', 0)
        
        # Create tree item
        item = self.details_tree.insert(
            parent, 'end',
            text=f"{topic} (depth {depth})",
            values=('Recursive Analysis', self.truncate_text(analysis) if analysis else 'No analysis')
        )
        
        # Add subtopics
        subtopics = node.get('subtopics', [])
        for subtopic in subtopics:
            self.add_recursive_node(item, subtopic)

    def populate_advanced_tree(self, results: Dict[str, Any]):
        """Populate tree for advanced analysis results"""
        analysis_type = results.get('type', 'unknown')

        if analysis_type == 'comparative':
            self.populate_comparative_tree(results)
        elif analysis_type == 'swot':
            self.populate_swot_tree(results)
        elif analysis_type == 'temporal':
            self.populate_temporal_tree(results)

    def populate_comparative_tree(self, results: Dict[str, Any]):
        """Populate tree for comparative analysis"""
        topics = results.get('topics', [])
        comparison_matrix = results.get('comparison_matrix', {})
        similarities = results.get('similarities', {})

        # Add comparison matrix
        if comparison_matrix:
            matrix_item = self.details_tree.insert(
                '', 'end',
                text='Comparison Matrix',
                values=('Comparison', 'Scores and criteria analysis')
            )

            scores = comparison_matrix.get('scores', {})
            for topic, topic_scores in scores.items():
                topic_item = self.details_tree.insert(
                    matrix_item, 'end',
                    text=topic,
                    values=('Topic Scores', str(topic_scores))
                )

        # Add similarities
        if similarities:
            sim_item = self.details_tree.insert(
                '', 'end',
                text='Topic Similarities',
                values=('Similarities', f'{len(similarities)} comparisons')
            )

            for (topic1, topic2), similarity in similarities.items():
                self.details_tree.insert(
                    sim_item, 'end',
                    text=f'{topic1} ↔ {topic2}',
                    values=('Similarity', f'{similarity:.2f}')
                )

        # Add recommendations
        recommendations = results.get('recommendations', '')
        if recommendations:
            self.details_tree.insert(
                '', 'end',
                text='Recommendations',
                values=('Recommendations', self.truncate_text(recommendations))
            )

    def populate_swot_tree(self, results: Dict[str, Any]):
        """Populate tree for SWOT analysis"""
        swot_matrix = results.get('swot_matrix', {})
        prioritized_swot = results.get('prioritized_swot', {})

        # Add SWOT categories
        for category, items in swot_matrix.items():
            category_item = self.details_tree.insert(
                '', 'end',
                text=category.title(),
                values=('SWOT Category', f'{len(items)} items')
            )

            # Add prioritized items if available
            if category in prioritized_swot:
                prioritized_items = prioritized_swot[category]
                for item_data in prioritized_items:
                    item_text = item_data.get('item', '')
                    priority_score = item_data.get('priority_score', 0)

                    self.details_tree.insert(
                        category_item, 'end',
                        text=self.truncate_text(item_text, 50),
                        values=('SWOT Item', f'Priority: {priority_score:.1f}')
                    )
            else:
                # Add regular items
                for item in items:
                    self.details_tree.insert(
                        category_item, 'end',
                        text=self.truncate_text(item, 50),
                        values=('SWOT Item', 'Standard item')
                    )

        # Add strategic recommendations
        recommendations = results.get('strategic_recommendations', [])
        if recommendations:
            rec_item = self.details_tree.insert(
                '', 'end',
                text='Strategic Recommendations',
                values=('Strategies', f'{len(recommendations)} recommendations')
            )

            for i, rec in enumerate(recommendations, 1):
                self.details_tree.insert(
                    rec_item, 'end',
                    text=f'Strategy {i}',
                    values=('Recommendation', self.truncate_text(rec))
                )

    def populate_temporal_tree(self, results: Dict[str, Any]):
        """Populate tree for temporal analysis"""
        timeline = results.get('timeline', [])
        trends = results.get('trends', {})

        # Add timeline
        if timeline:
            timeline_item = self.details_tree.insert(
                '', 'end',
                text='Timeline',
                values=('Timeline', f'{len(timeline)} events')
            )

            for event in timeline:
                period = event.get('period', 'Unknown')
                description = event.get('description', '')

                self.details_tree.insert(
                    timeline_item, 'end',
                    text=period,
                    values=('Event', self.truncate_text(description))
                )

        # Add trends
        if trends:
            trends_item = self.details_tree.insert(
                '', 'end',
                text='Trends Analysis',
                values=('Trends', 'Identified patterns')
            )

            for trend_type, trend_items in trends.items():
                if trend_items:
                    trend_category = self.details_tree.insert(
                        trends_item, 'end',
                        text=trend_type.title(),
                        values=('Trend Category', f'{len(trend_items)} trends')
                    )

                    for trend in trend_items:
                        self.details_tree.insert(
                            trend_category, 'end',
                            text=self.truncate_text(trend, 50),
                            values=('Trend', 'Identified trend')
                        )

        # Add predictions
        predictions = results.get('predictions', '')
        if predictions:
            self.details_tree.insert(
                '', 'end',
                text='Future Predictions',
                values=('Predictions', self.truncate_text(predictions))
            )
    
    def truncate_text(self, text: str, max_length: int = 100) -> str:
        """Truncate text for display"""
        if len(text) <= max_length:
            return text
        return text[:max_length] + "..."
    
    def view_full_content(self, event):
        """View full content of selected item"""
        selection = self.details_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        values = self.details_tree.item(item, 'values')
        
        if len(values) >= 2:
            content_type = values[0]
            # Find full content from results
            # This would need to be implemented based on the tree structure
            show_info("Full Content", f"Full content viewer for {content_type} will be implemented")
    
    def export_markdown(self):
        """Export results to markdown format"""
        if not self.current_results:
            show_error("No Results", "No analysis results to export.")
            return
        
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="Export to Markdown",
            defaultextension=".md",
            filetypes=[("Markdown files", "*.md"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                markdown_content = self.generate_markdown(self.current_results)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                show_info("Export Successful", f"Results exported to {filename}")
            except Exception as e:
                show_error("Export Error", f"Failed to export: {str(e)}")
    
    def export_json(self):
        """Export results to JSON format"""
        if not self.current_results:
            show_error("No Results", "No analysis results to export.")
            return
        
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="Export to JSON",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.current_results, f, indent=2, ensure_ascii=False)
                show_info("Export Successful", f"Results exported to {filename}")
            except Exception as e:
                show_error("Export Error", f"Failed to export: {str(e)}")
    
    def generate_markdown(self, results: Dict[str, Any]) -> str:
        """Generate markdown content from results"""
        # This would generate a comprehensive markdown report
        # For now, return the summary
        return self.generate_summary(results)
    
    def clear_results(self):
        """Clear all results"""
        if messagebox.askyesno("Clear Results", "Clear all analysis results?"):
            self.current_results = None
            
            # Clear all tabs
            self.summary_text.config(state=tk.NORMAL)
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.config(state=tk.DISABLED)
            
            for item in self.details_tree.get_children():
                self.details_tree.delete(item)
            
            self.connections_text.config(state=tk.NORMAL)
            self.connections_text.delete(1.0, tk.END)
            self.connections_text.config(state=tk.DISABLED)

            # Clear mind map
            self.mindmap_path = None
            self.mindmap_status_var.set("No mind map generated")

            # Clear mind map display and show placeholder
            for widget in self.mindmap_display_frame.winfo_children():
                widget.destroy()
            self.mindmap_placeholder = ttk.Label(
                self.mindmap_display_frame,
                text="Click 'Generate Mind Map' to create an interactive visualization\nof your analysis results.",
                justify=tk.CENTER,
                foreground="gray"
            )
            self.mindmap_placeholder.pack(expand=True)

            self.raw_text.config(state=tk.NORMAL)
            self.raw_text.delete(1.0, tk.END)
            self.raw_text.config(state=tk.DISABLED)
    
    def view_connections(self):
        """Switch to connections tab"""
        self.notebook.select(2)  # Connections tab
    
    def has_results(self) -> bool:
        """Check if there are results to display"""
        return self.current_results is not None
    
    def export_results(self, filename: str):
        """Export results to specified filename"""
        if not self.current_results:
            return
        
        # Determine format from extension
        if filename.lower().endswith('.md'):
            content = self.generate_markdown(self.current_results)
        elif filename.lower().endswith('.json'):
            content = json.dumps(self.current_results, indent=2, ensure_ascii=False)
        else:
            content = self.generate_summary(self.current_results)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            show_info("Export Successful", f"Results exported to {filename}")
        except Exception as e:
            show_error("Export Error", f"Failed to export: {str(e)}")
    
    def clear(self):
        """Clear results without confirmation"""
        self.current_results = None
        
        # Clear all display areas
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.config(state=tk.DISABLED)
        
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        self.connections_text.config(state=tk.NORMAL)
        self.connections_text.delete(1.0, tk.END)
        self.connections_text.config(state=tk.DISABLED)

        # Clear mind map
        self.mindmap_path = None
        if hasattr(self, 'mindmap_status_var'):
            self.mindmap_status_var.set("No mind map generated")

        # Clear mind map display and show placeholder
        if hasattr(self, 'mindmap_display_frame'):
            for widget in self.mindmap_display_frame.winfo_children():
                widget.destroy()
            self.mindmap_placeholder = ttk.Label(
                self.mindmap_display_frame,
                text="Click 'Generate Mind Map' to create an interactive visualization\nof your analysis results.",
                justify=tk.CENTER,
                foreground="gray"
            )
            self.mindmap_placeholder.pack(expand=True)

        self.raw_text.config(state=tk.NORMAL)
        self.raw_text.delete(1.0, tk.END)
        self.raw_text.config(state=tk.DISABLED)
    
    def cleanup(self):
        """Cleanup resources"""
        pass
