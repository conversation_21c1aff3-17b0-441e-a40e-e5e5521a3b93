"""
Mind Map Generator for AI Analysis Program
Converts analysis results into interactive mind map visualizations
"""

import json
import math
import os
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import sanitize_filename, generate_hash

class MindMapGenerator(LoggerMixin):
    """Generates interactive mind maps from analysis results"""
    
    def __init__(self, config: Config):
        self.config = config
        self.template_dir = Path("templates")
        self.template_dir.mkdir(exist_ok=True)
        self.output_dir = Path("exports/mindmaps")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create HTML template if it doesn't exist
        self.ensure_template_exists()
    
    def ensure_template_exists(self):
        """Ensure the HTML template file exists"""
        template_path = self.template_dir / "mindmap_template.html"
        if not template_path.exists():
            self.create_html_template(template_path)
    
    def create_html_template(self, template_path: Path):
        """Create the HTML template for mind maps"""
        template_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Analysis Mind Map</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }
        
        #container {
            display: flex;
            height: 100vh;
        }
        
        #mindmap-area {
            flex: 1;
            position: relative;
            background: white;
        }
        
        #mindmap-svg {
            width: 100%;
            height: 100%;
            cursor: grab;
        }
        
        #mindmap-svg:active {
            cursor: grabbing;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #details-panel {
            width: 300px;
            background: #fff;
            border-left: 1px solid #ddd;
            padding: 20px;
            overflow-y: auto;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }
        
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            stroke: #333;
            stroke-width: 2px;
        }
        
        .node.selected {
            stroke: #ff6b6b;
            stroke-width: 3px;
        }
        
        .node-label {
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            pointer-events: none;
            fill: #333;
        }
        
        .link {
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 2px;
            fill: none;
        }
        
        .link.connection {
            stroke: #ff9800;
            stroke-dasharray: 5,5;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            max-width: 200px;
        }
        
        .control-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .control-button:hover {
            background: #45a049;
        }
        
        .search-box {
            width: 150px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 2px;
        }
        
        #node-details h3 {
            margin-top: 0;
            color: #333;
        }
        
        #node-details p {
            line-height: 1.5;
            color: #666;
        }
        
        .analysis-content {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="mindmap-area">
            <svg id="mindmap-svg"></svg>
            <div id="controls">
                <button class="control-button" onclick="resetZoom()">Reset View</button>
                <button class="control-button" onclick="exportPNG()">Export PNG</button>
                <button class="control-button" onclick="toggleLayout()">Toggle Layout</button>
                <br>
                <input type="text" class="search-box" id="search-input" placeholder="Search nodes..." onkeyup="searchNodes()">
            </div>
            <div class="tooltip" id="tooltip" style="display: none;"></div>
        </div>
        <div id="details-panel">
            <div id="node-details">
                <h3>Select a node to view details</h3>
                <p>Click on any node in the mind map to see detailed analysis information.</p>
            </div>
        </div>
    </div>

    <script>
        // Mind map data will be injected here
        const mindmapData = {{MINDMAP_DATA}};
        
        // Initialize mind map
        let svg, g, simulation, nodes, links;
        let width, height;
        let selectedNode = null;
        let currentLayout = 'force';
        
        function initMindMap() {
            // Set up SVG
            svg = d3.select("#mindmap-svg");
            width = parseInt(svg.style("width"));
            height = parseInt(svg.style("height"));
            
            // Create main group for zooming/panning
            g = svg.append("g");
            
            // Set up zoom behavior
            const zoom = d3.zoom()
                .scaleExtent([0.1, 4])
                .on("zoom", (event) => {
                    g.attr("transform", event.transform);
                });
            
            svg.call(zoom);
            
            // Process data
            nodes = mindmapData.nodes.map(d => ({...d}));
            links = mindmapData.links.map(d => ({...d}));
            
            // Create force simulation
            createForceSimulation();
            
            // Render mind map
            renderMindMap();
        }
        
        function createForceSimulation() {
            simulation = d3.forceSimulation(nodes)
                .force("link", d3.forceLink(links).id(d => d.id).distance(100))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(width / 2, height / 2))
                .force("collision", d3.forceCollide().radius(d => d.size + 10));
        }
        
        function renderMindMap() {
            // Clear existing elements
            g.selectAll("*").remove();
            
            // Create links
            const link = g.selectAll(".link")
                .data(links)
                .enter().append("line")
                .attr("class", d => `link ${d.type || ''}`)
                .attr("stroke-width", d => Math.sqrt(d.strength * 5) || 2);
            
            // Create nodes
            const node = g.selectAll(".node")
                .data(nodes)
                .enter().append("g")
                .attr("class", "node")
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended))
                .on("click", nodeClicked)
                .on("mouseover", nodeMouseOver)
                .on("mouseout", nodeMouseOut);
            
            // Add circles to nodes
            node.append("circle")
                .attr("r", d => d.size || 20)
                .attr("fill", d => d.color || "#4CAF50")
                .attr("stroke", "#fff")
                .attr("stroke-width", 2);
            
            // Add labels to nodes
            node.append("text")
                .attr("class", "node-label")
                .attr("dy", ".35em")
                .text(d => d.label || d.id)
                .style("font-size", d => `${Math.max(10, (d.size || 20) / 3)}px`);
            
            // Update positions on simulation tick
            simulation.on("tick", () => {
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                node.attr("transform", d => `translate(${d.x},${d.y})`);
            });
        }
        
        function nodeClicked(event, d) {
            // Update selection
            d3.selectAll(".node").classed("selected", false);
            d3.select(this).classed("selected", true);
            selectedNode = d;
            
            // Update details panel
            updateDetailsPanel(d);
        }
        
        function nodeMouseOver(event, d) {
            const tooltip = d3.select("#tooltip");
            tooltip.style("display", "block")
                .html(`<strong>${d.label}</strong><br>${d.type || 'Node'}<br>Click for details`)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px");
        }
        
        function nodeMouseOut() {
            d3.select("#tooltip").style("display", "none");
        }
        
        function updateDetailsPanel(node) {
            const detailsDiv = document.getElementById("node-details");
            
            let html = `<h3>${node.label}</h3>`;
            html += `<p><strong>Type:</strong> ${node.type || 'Unknown'}</p>`;
            
            if (node.analysis) {
                html += `<div class="analysis-content">`;
                html += `<h4>Analysis:</h4>`;
                html += `<p>${node.analysis}</p>`;
                html += `</div>`;
            }
            
            if (node.connections && node.connections.length > 0) {
                html += `<h4>Connections:</h4>`;
                html += `<ul>`;
                node.connections.forEach(conn => {
                    html += `<li>${conn}</li>`;
                });
                html += `</ul>`;
            }
            
            detailsDiv.innerHTML = html;
        }
        
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }
        
        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }
        
        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
        
        function resetZoom() {
            svg.transition().duration(750).call(
                d3.zoom().transform,
                d3.zoomIdentity
            );
        }
        
        function exportPNG() {
            // This would implement PNG export functionality
            alert("PNG export functionality would be implemented here");
        }
        
        function toggleLayout() {
            if (currentLayout === 'force') {
                // Switch to hierarchical layout
                currentLayout = 'hierarchical';
                createHierarchicalLayout();
            } else {
                // Switch back to force layout
                currentLayout = 'force';
                createForceSimulation();
            }
        }
        
        function createHierarchicalLayout() {
            // Implement hierarchical layout
            simulation.stop();
            
            // Simple hierarchical positioning
            const root = nodes.find(n => n.type === 'root');
            if (root) {
                root.fx = width / 2;
                root.fy = height / 2;
                
                const children = nodes.filter(n => n.parent === root.id);
                children.forEach((child, i) => {
                    const angle = (2 * Math.PI * i) / children.length;
                    child.fx = root.fx + 150 * Math.cos(angle);
                    child.fy = root.fy + 150 * Math.sin(angle);
                });
            }
            
            simulation.alpha(1).restart();
        }
        
        function searchNodes() {
            const searchTerm = document.getElementById("search-input").value.toLowerCase();
            
            d3.selectAll(".node").style("opacity", 1);
            
            if (searchTerm) {
                d3.selectAll(".node")
                    .style("opacity", d => 
                        d.label.toLowerCase().includes(searchTerm) || 
                        (d.analysis && d.analysis.toLowerCase().includes(searchTerm)) ? 1 : 0.3
                    );
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', initMindMap);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            width = parseInt(svg.style("width"));
            height = parseInt(svg.style("height"));
            simulation.force("center", d3.forceCenter(width / 2, height / 2));
            simulation.alpha(1).restart();
        });
    </script>
</body>
</html>'''
        
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        self.logger.info(f"Created HTML template at {template_path}")
    
    def convert_analysis_to_mindmap(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert analysis results to mind map data structure"""
        nodes = []
        links = []
        
        analysis_type = analysis_results.get('type', 'unknown')
        main_topic = analysis_results.get('topic', 'Main Topic')
        
        # Create root node
        root_node = {
            "id": "root",
            "label": main_topic,
            "type": "root",
            "size": 50,
            "color": "#4CAF50",
            "analysis": analysis_results.get('main_analysis', ''),
            "x": 400,
            "y": 300
        }
        nodes.append(root_node)
        
        if analysis_type == 'iterative':
            self._process_iterative_analysis(analysis_results, nodes, links)
        elif analysis_type == 'recursive':
            self._process_recursive_analysis(analysis_results, nodes, links)
        
        # Add connection nodes if available
        connections = analysis_results.get('connections', '')
        if connections:
            self._add_connection_analysis(connections, nodes, links)
        
        return {
            "nodes": nodes,
            "links": links,
            "metadata": analysis_results.get('metadata', {})
        }
    
    def _process_iterative_analysis(self, results: Dict[str, Any], nodes: List[Dict], links: List[Dict]):
        """Process iterative analysis results"""
        sub_analyses = results.get('sub_analyses', [])
        
        for i, sub_analysis in enumerate(sub_analyses):
            topic = sub_analysis.get('topic', f'Sub-topic {i+1}')
            analysis = sub_analysis.get('analysis', '')
            
            node_id = f"subtopic_{i}"
            node = {
                "id": node_id,
                "label": topic,
                "type": "subtopic",
                "size": 30,
                "color": "#2196F3",
                "analysis": analysis,
                "parent": "root"
            }
            nodes.append(node)
            
            # Create link to root
            link = {
                "source": "root",
                "target": node_id,
                "strength": 0.8,
                "type": "hierarchical"
            }
            links.append(link)
    
    def _process_recursive_analysis(self, results: Dict[str, Any], nodes: List[Dict], links: List[Dict]):
        """Process recursive analysis results"""
        analysis_tree = results.get('analysis_tree', {})
        if analysis_tree:
            self._process_recursive_node(analysis_tree, nodes, links, "root", 0)
    
    def _process_recursive_node(self, node_data: Dict[str, Any], nodes: List[Dict], links: List[Dict], parent_id: str, depth: int):
        """Process a recursive analysis node"""
        subtopics = node_data.get('subtopics', [])
        
        for i, subtopic in enumerate(subtopics):
            topic = subtopic.get('topic', f'Topic {i+1}')
            analysis = subtopic.get('analysis', '')
            current_depth = subtopic.get('depth', depth + 1)
            
            node_id = f"{parent_id}_sub_{i}"
            
            # Color based on depth
            colors = ["#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#F44336"]
            color = colors[min(current_depth, len(colors) - 1)]
            
            # Size based on depth (smaller as depth increases)
            size = max(20, 40 - (current_depth * 5))
            
            node = {
                "id": node_id,
                "label": topic,
                "type": f"depth_{current_depth}",
                "size": size,
                "color": color,
                "analysis": analysis,
                "parent": parent_id,
                "depth": current_depth
            }
            nodes.append(node)
            
            # Create link to parent
            link = {
                "source": parent_id,
                "target": node_id,
                "strength": max(0.3, 1.0 - (current_depth * 0.2)),
                "type": "hierarchical"
            }
            links.append(link)
            
            # Process subtopics recursively
            self._process_recursive_node(subtopic, nodes, links, node_id, current_depth)
    
    def _add_connection_analysis(self, connections: str, nodes: List[Dict], links: List[Dict]):
        """Add connection analysis as a special node"""
        connection_node = {
            "id": "connections",
            "label": "Connections",
            "type": "connections",
            "size": 35,
            "color": "#FF9800",
            "analysis": connections
        }
        nodes.append(connection_node)
        
        # Link to root
        link = {
            "source": "root",
            "target": "connections",
            "strength": 0.6,
            "type": "connection"
        }
        links.append(link)
    
    def generate_interactive_mindmap(self, analysis_results: Dict[str, Any], output_filename: Optional[str] = None) -> str:
        """Generate interactive HTML mind map"""
        try:
            # Convert analysis to mind map data
            mindmap_data = self.convert_analysis_to_mindmap(analysis_results)
            
            # Generate filename if not provided
            if not output_filename:
                topic = analysis_results.get('topic', 'analysis')
                safe_topic = sanitize_filename(topic)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"mindmap_{safe_topic}_{timestamp}.html"
            
            output_path = self.output_dir / output_filename
            
            # Load template
            template_path = self.template_dir / "mindmap_template.html"
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # Inject data into template
            html_content = template_content.replace(
                '{{MINDMAP_DATA}}',
                json.dumps(mindmap_data, indent=2)
            )
            
            # Save HTML file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"Generated interactive mind map: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Failed to generate mind map: {e}")
            raise
    
    def get_mindmap_data_json(self, analysis_results: Dict[str, Any]) -> str:
        """Get mind map data as JSON string for embedding"""
        mindmap_data = self.convert_analysis_to_mindmap(analysis_results)
        return json.dumps(mindmap_data, indent=2)
