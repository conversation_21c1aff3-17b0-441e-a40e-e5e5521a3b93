"""
Context Framework for Enhanced Analysis
Provides hierarchical context management for topic analysis
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from datetime import datetime
import json

class ContextLevel(Enum):
    """Analysis context levels"""
    ROOT = "root"           # Main topic
    BRANCH = "branch"       # Primary sub-topic
    LEAF = "leaf"          # Secondary sub-topic
    CROSS_REF = "cross_ref" # Cross-referenced topic

@dataclass
class AnalysisInsight:
    """Structured insight from analysis"""
    type: str  # "key_point", "relationship", "implication", "question"
    content: str
    confidence: float
    connections: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AnalysisResult:
    """Result of a single topic analysis"""
    topic: str
    analysis: str
    insights: List[AnalysisInsight]
    context_level: ContextLevel
    connections: List[str]
    quality_score: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AnalysisContext:
    """Comprehensive analysis context"""
    main_topic: str
    topic_hierarchy: Dict[str, List[str]] = field(default_factory=dict)
    analysis_goals: List[str] = field(default_factory=list)
    domain_knowledge: Dict[str, Any] = field(default_factory=dict)
    previous_analyses: List[AnalysisResult] = field(default_factory=list)
    relationship_map: Dict[str, List[str]] = field(default_factory=dict)
    analysis_framework: Dict[str, Any] = field(default_factory=dict)
    running_insights: List[AnalysisInsight] = field(default_factory=list)

    def update_with_analysis(self, result: AnalysisResult):
        """Update context with new analysis result"""
        self.previous_analyses.append(result)

        # Update relationship map
        if result.topic not in self.relationship_map:
            self.relationship_map[result.topic] = []
        self.relationship_map[result.topic].extend(result.connections)

        # Add insights to running collection
        self.running_insights.extend(result.insights)

        # Update domain knowledge
        if result.context_level == ContextLevel.ROOT:
            self.analysis_framework = result.metadata.get('framework', {})

    def get_related_analyses(self, topic: str) -> List[AnalysisResult]:
        """Get analyses related to a specific topic"""
        related = []
        for analysis in self.previous_analyses:
            if (topic in analysis.connections or
                analysis.topic in self.relationship_map.get(topic, [])):
                related.append(analysis)
        return related

    def get_context_summary(self) -> str:
        """Get a summary of current context"""
        summary_parts = [f"Main Topic: {self.main_topic}"]

        if self.analysis_framework:
            summary_parts.append(f"Analysis Framework: {self.analysis_framework}")

        if self.previous_analyses:
            topics_analyzed = [a.topic for a in self.previous_analyses]
            summary_parts.append(f"Topics Analyzed: {', '.join(topics_analyzed)}")

        if self.running_insights:
            key_insights = [i.content for i in self.running_insights[:3]]
            summary_parts.append(f"Key Insights: {'; '.join(key_insights)}")

        return "\n".join(summary_parts)

class ContextualPromptBuilder:
    """Builds context-aware prompts for analysis"""

    def __init__(self):
        self.templates = {
            ContextLevel.ROOT: self._get_root_template(),
            ContextLevel.BRANCH: self._get_branch_template(),
            ContextLevel.CROSS_REF: self._get_cross_ref_template()
        }

    def _get_root_template(self) -> str:
        return """You are analyzing the main topic: "{main_topic}"

Context:
- This is the central concept that will guide all subsequent analysis
- Sub-topics to be analyzed: {sub_topics}
- Analysis goals: {goals}

Please provide:
1. Comprehensive overview of the topic
2. Key dimensions for sub-topic analysis
3. Framework for understanding relationships
4. Success criteria for complete analysis

Focus on establishing the foundation for coherent sub-topic analysis.
Provide your response in a structured format with clear sections."""

    def _get_branch_template(self) -> str:
        return """You are analyzing the sub-topic: "{sub_topic}"

Main Topic Context: "{main_topic}"
- Main topic analysis summary: {main_analysis_summary}
- Analysis framework: {analysis_framework}
- Related sub-topics: {related_subtopics}
- Previous insights: {previous_insights}

Please analyze "{sub_topic}" specifically in relation to "{main_topic}":
1. How does this sub-topic contribute to understanding the main topic?
2. What unique perspective does it provide?
3. How does it relate to other sub-topics: {related_subtopics}?
4. What implications does this have for the overall analysis?
5. What connections can you identify with previous analyses?

Maintain coherence with the main topic while providing specific insights.
Build upon previous analyses and identify new connections."""

    def _get_cross_ref_template(self) -> str:
        return """You are analyzing connections between topics within the context of "{main_topic}":

Topics to connect: {topics}
Individual analyses: {topic_analyses}
Main topic framework: {framework}
Current insights: {current_insights}

Please identify:
1. Direct relationships between topics
2. Indirect connections through the main topic
3. Synergies and conflicts
4. Emergent patterns
5. Implications for the main topic understanding

Focus on meaningful connections that enhance overall comprehension.
Provide specific examples and evidence for each connection identified."""

    def build_prompt(
        self,
        topic: str,
        context: AnalysisContext,
        level: ContextLevel,
        additional_params: Dict[str, Any] = None
    ) -> str:
        """Build a context-aware prompt"""
        template = self.templates[level]

        # Prepare template variables
        variables = {
            'main_topic': context.main_topic,
            'sub_topic': topic,
            'sub_topics': ', '.join(context.topic_hierarchy.get('sub_topics', [])),
            'goals': ', '.join(context.analysis_goals),
            'related_subtopics': ', '.join(context.relationship_map.get(topic, [])),
            'topics': topic,
            'framework': json.dumps(context.analysis_framework, indent=2) if context.analysis_framework else "Not yet established"
        }

        # Add context-specific variables
        if level == ContextLevel.BRANCH:
            main_analysis = next((a for a in context.previous_analyses
                                if a.context_level == ContextLevel.ROOT), None)
            variables['main_analysis_summary'] = (
                main_analysis.analysis[:200] + "..." if main_analysis else "Not available"
            )
            variables['previous_insights'] = "; ".join([
                i.content for i in context.running_insights[-3:]
            ]) if context.running_insights else "None yet"

        elif level == ContextLevel.CROSS_REF:
            variables['topic_analyses'] = "\n".join([
                f"- {a.topic}: {a.analysis[:100]}..."
                for a in context.previous_analyses[-3:]
            ])
            variables['current_insights'] = "\n".join([
                f"- {i.content}" for i in context.running_insights[-5:]
            ])

        # Add additional parameters
        if additional_params:
            variables.update(additional_params)

        # Format template
        try:
            return template.format(**variables)
        except KeyError as e:
            # Fallback for missing variables
            return template.format_map(variables)