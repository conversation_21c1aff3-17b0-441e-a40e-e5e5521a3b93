"""
Ollama API client for AI Analysis Program
"""

import requests
import json
import time
from typing import Dict, Any, List, Optional, Generator
from utils.logging_setup import LoggerMixin
from utils.config import Config

class OllamaClient(LoggerMixin):
    """Client for interacting with Ollama API"""
    
    def __init__(self, config: Config):
        self.config = config
        self.base_url = config.get("ollama.base_url", "http://localhost:11434")
        self.timeout = config.get("ollama.timeout", 30)
        self.default_model = config.get("ollama.default_model", "llama2")
        self.session = requests.Session()
        
    def is_available(self) -> bool:
        """Check if Ollama service is available"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Ollama service not available: {e}")
            return False
    
    def list_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=self.timeout)
            response.raise_for_status()
            data = response.json()
            return data.get("models", [])
        except Exception as e:
            self.logger.error(f"Failed to list models: {e}")
            return []
    
    def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry"""
        try:
            self.logger.info(f"Pulling model: {model_name}")
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                timeout=300  # 5 minutes for model download
            )
            response.raise_for_status()
            return True
        except Exception as e:
            self.logger.error(f"Failed to pull model {model_name}: {e}")
            return False
    
    def generate(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
        stream: bool = False
    ) -> Optional[str]:
        """
        Generate text using Ollama
        
        Args:
            prompt: Input prompt
            model: Model name (uses default if None)
            temperature: Generation temperature
            max_tokens: Maximum tokens to generate
            system_prompt: System prompt for context
            stream: Whether to stream response
            
        Returns:
            Generated text or None if failed
        """
        try:
            model = model or self.default_model
            temperature = temperature or self.config.get("ollama.temperature", 0.7)
            max_tokens = max_tokens or self.config.get("ollama.max_tokens", 2048)
            
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            self.logger.debug(f"Generating with model {model}, prompt length: {len(prompt)}")
            
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            if stream:
                return self._handle_stream_response(response)
            else:
                data = response.json()
                return data.get("response", "")
                
        except Exception as e:
            self.logger.error(f"Generation failed: {e}")
            return None
    
    def generate_stream(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None
    ) -> Generator[str, None, None]:
        """
        Generate text with streaming response
        
        Args:
            prompt: Input prompt
            model: Model name
            temperature: Generation temperature
            max_tokens: Maximum tokens
            system_prompt: System prompt
            
        Yields:
            Text chunks as they are generated
        """
        try:
            model = model or self.default_model
            temperature = temperature or self.config.get("ollama.temperature", 0.7)
            max_tokens = max_tokens or self.config.get("ollama.max_tokens", 2048)
            
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": True,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                stream=True,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'response' in data:
                            yield data['response']
                        if data.get('done', False):
                            break
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            self.logger.error(f"Streaming generation failed: {e}")
            yield ""
    
    def _handle_stream_response(self, response: requests.Response) -> str:
        """Handle streaming response and return complete text"""
        complete_text = ""
        try:
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'response' in data:
                            complete_text += data['response']
                        if data.get('done', False):
                            break
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            self.logger.error(f"Error handling stream response: {e}")
        
        return complete_text
    
    def analyze_topic(
        self,
        topic: str,
        context: Optional[str] = None,
        analysis_type: str = "general"
    ) -> Optional[str]:
        """
        Analyze a topic using AI
        
        Args:
            topic: Topic to analyze
            context: Additional context
            analysis_type: Type of analysis (general, detailed, connections)
            
        Returns:
            Analysis result or None if failed
        """
        prompts = {
            "general": f"Analyze the following topic and provide insights: {topic}",
            "detailed": f"Provide a detailed analysis of: {topic}. Include key aspects, implications, and related concepts.",
            "connections": f"Analyze the topic '{topic}' and identify potential connections to other concepts, ideas, or domains."
        }
        
        system_prompts = {
            "general": "You are an expert analyst. Provide clear, structured insights.",
            "detailed": "You are a research analyst. Provide comprehensive, well-structured analysis.",
            "connections": "You are a systems thinker. Focus on identifying relationships and connections."
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        system_prompt = system_prompts.get(analysis_type, system_prompts["general"])
        
        if context:
            prompt += f"\n\nAdditional context: {context}"
        
        return self.generate(
            prompt=prompt,
            system_prompt=system_prompt
        )
    
    def find_connections(self, topics: List[str]) -> Optional[str]:
        """
        Find connections between multiple topics
        
        Args:
            topics: List of topics to analyze
            
        Returns:
            Connection analysis or None if failed
        """
        topics_str = ", ".join(topics)
        prompt = f"Analyze the connections and relationships between these topics: {topics_str}. Identify common themes, contrasts, and potential synergies."
        
        system_prompt = "You are an expert in systems thinking and pattern recognition. Focus on identifying meaningful connections and relationships."
        
        return self.generate(
            prompt=prompt,
            system_prompt=system_prompt
        )
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific model"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/show",
                json={"name": model_name},
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Failed to get model info for {model_name}: {e}")
            return None
