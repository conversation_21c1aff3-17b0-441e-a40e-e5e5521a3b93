"""
Topic input panel for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Optional
import json

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import validate_topic_structure, show_error, show_info
from gui.widgets import CollapsibleFrame, ToolTip

class TopicInputPanel(LoggerMixin):
    """Panel for topic input and management"""
    
    def __init__(self, parent: tk.Widget, config: Config, theme=None):
        self.parent = parent
        self.config = config
        self.theme = theme
        # Main topic section
        main_topic_frame = CollapsibleFrame(
            self.parent,
            title="Main Topic",
            collapsed=False,
            theme=self.theme
        )
        main_topic_frame.pack(fill=tk.X, pady=(0, self.theme.spacing.pad_md if self.theme else 10))
        # Topic title
        title_label = ttk.Label(main_topic_frame.content_frame, text="Title:")
        title_label.pack(anchor=tk.W)
        self.topic_title_var = tk.StringVar()
        self.topic_title_entry = ttk.Entry(main_topic_frame.content_frame, textvariable=self.topic_title_var)
        self.topic_title_entry.pack(fill=tk.X, pady=(2, 0))
        # Topic description
        desc_label = ttk.Label(main_topic_frame.content_frame, text="Description:")
        desc_label.pack(anchor=tk.W)
        self.topic_desc_text = tk.Text(main_topic_frame.content_frame, height=4, wrap=tk.WORD)
        self.topic_desc_text.pack(fill=tk.X, pady=(2, 0))
        # Sub-topics section (tree)
        subtopics_frame = CollapsibleFrame(
            self.parent,
            title="Sub-topics (up to 5 levels)",
            collapsed=False,
            theme=self.theme
        )
        subtopics_frame.pack(fill=tk.BOTH, expand=True, pady=(0, self.theme.spacing.pad_md if self.theme else 10))
        tree_frame = ttk.Frame(subtopics_frame.content_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        self.subtopics_treeview = ttk.Treeview(tree_frame, columns=("Description",), show="tree headings")
        self.subtopics_treeview.heading("#0", text="Title")
        self.subtopics_treeview.heading("Description", text="Description")
        self.subtopics_treeview.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.subtopics_treeview.yview)
        self.subtopics_treeview.configure(yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        # Toolbar for tree operations
        toolbar_frame = ttk.Frame(subtopics_frame.content_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, self.theme.spacing.pad_sm if self.theme else 5))
        add_btn = ttk.Button(toolbar_frame, text="➕ Add Sub-topic", command=self.add_subtopic_dialog)
        add_btn.pack(side=tk.LEFT)
        remove_btn = ttk.Button(toolbar_frame, text="➖ Remove Selected", command=self.remove_selected_subtopic)
        remove_btn.pack(side=tk.LEFT, padx=(5, 0))
        edit_btn = ttk.Button(toolbar_frame, text="✏️ Edit Selected", command=self.edit_selected_subtopic)
        edit_btn.pack(side=tk.LEFT, padx=(5, 0))
        # File operations section - collapsible
        file_frame = CollapsibleFrame(
            self.parent,
            title="File Operations",
            collapsed=True,  # Start collapsed
            theme=self.theme
        )
        file_frame.pack(fill=tk.X)
        file_buttons_frame = ttk.Frame(file_frame.content_frame)
        file_buttons_frame.pack(fill=tk.X, pady=self.theme.spacing.pad_sm if self.theme else 5)
        # Load button with icon
        load_btn = ttk.Button(
            file_buttons_frame,
            text="📂 Load from File",
            command=self.load_from_file
        )
        load_btn.pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm if self.theme else 5))
        if self.theme:
            ToolTip(load_btn, "Load topic data from a JSON file")
        # Save button with icon
        save_btn = ttk.Button(
            file_buttons_frame,
            text="💾 Save to File",
            command=self.save_to_file
        )
        save_btn.pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm if self.theme else 5))
        if self.theme:
            ToolTip(save_btn, "Save current topic data to a JSON file")
        # Import button with icon
        import_btn = ttk.Button(
            file_buttons_frame,
            text="📊 Import CSV",
            command=self.import_csv
        )
        import_btn.pack(side=tk.LEFT)
        if self.theme:
            ToolTip(import_btn, "Import sub-topics from a CSV file")
        self.max_subtopic_depth = 5

        # Backward compatibility property
        self.subtopics_data = []

    def add_subtopic_dialog(self):
        """Add a new sub-topic at the selected level (up to 5 levels)"""
        selected = self.subtopics_treeview.selection()
        parent = selected[0] if selected else ''
        depth = self.get_tree_depth(parent)
        if depth >= self.max_subtopic_depth:
            messagebox.showerror("Max Depth", f"Cannot add more than {self.max_subtopic_depth} levels of sub-topics.")
            return
        dialog = SubtopicDialog(self.parent, "Add Sub-topic")
        if dialog.result:
            title = dialog.result['title']
            desc = dialog.result['description']
            self.subtopics_treeview.insert(parent, 'end', text=title, values=(desc,))

    def remove_selected_subtopic(self):
        """Remove selected sub-topic (and its children)"""
        selected = self.subtopics_treeview.selection()
        for item in selected:
            self.subtopics_treeview.delete(item)

    def edit_selected_subtopic(self):
        """Edit the selected sub-topic"""
        selected = self.subtopics_treeview.selection()
        if not selected:
            return
        item = selected[0]
        title = self.subtopics_treeview.item(item, 'text')
        desc = self.subtopics_treeview.set(item, 'Description')
        dialog = SubtopicDialog(self.parent, "Edit Sub-topic", {'title': title, 'description': desc})
        if dialog.result:
            self.subtopics_treeview.item(item, text=dialog.result['title'])
            self.subtopics_treeview.set(item, 'Description', dialog.result['description'])

    def get_tree_depth(self, item_id):
        """Get the depth of a tree item (root is 1)"""
        depth = 1
        while item_id:
            item_id = self.subtopics_treeview.parent(item_id)
            if item_id:
                depth += 1
        return depth

    def get_topic_data(self) -> Optional[Dict[str, Any]]:
        """Get current topic data with nested sub-topics"""
        title = self.topic_title_var.get().strip()
        if not title:
            return None
        description = self.topic_desc_text.get(1.0, tk.END).strip()
        def build_subtopic_tree(item_id):
            children = self.subtopics_treeview.get_children(item_id)
            return [
                {
                    'title': self.subtopics_treeview.item(child, 'text'),
                    'description': self.subtopics_treeview.set(child, 'Description'),
                    'sub_topics': build_subtopic_tree(child)
                }
                for child in children
            ]
        sub_topics_tree = build_subtopic_tree('')

        # Create flattened sub_topics list for backward compatibility
        def flatten_subtopics(tree):
            result = []
            for item in tree:
                result.append(item['title'])
                if item.get('sub_topics'):
                    result.extend(flatten_subtopics(item['sub_topics']))
            return result

        topic_data = {
            'title': title,
            'description': description,
            'sub_topics_tree': sub_topics_tree,
            'sub_topics': flatten_subtopics(sub_topics_tree)  # Backward compatibility
        }
        return topic_data

        # (Removed unreachable duplicate file operation button code and old subtopics_data logic)

    
    # (Removed duplicate/old get_topic_data)
    
    def set_topic_data(self, topic_data: Dict[str, Any]):
        """Set topic data (for loading from file)"""
        self.topic_title_var.set(topic_data.get('title', ''))
        description = topic_data.get('description', '')
        self.topic_desc_text.delete(1.0, tk.END)
        self.topic_desc_text.insert(1.0, description)
        # Clear and repopulate treeview
        self.subtopics_treeview.delete(*self.subtopics_treeview.get_children())
        def insert_subtopics(parent, subtopics):
            for sub in subtopics:
                item = self.subtopics_treeview.insert(parent, 'end', text=sub.get('title', ''), values=(sub.get('description', ''),))
                if sub.get('sub_topics'):
                    insert_subtopics(item, sub['sub_topics'])

        # Handle both new format (sub_topics_tree) and old format (sub_topics_detailed)
        if 'sub_topics_tree' in topic_data:
            insert_subtopics('', topic_data['sub_topics_tree'])
        elif 'sub_topics_detailed' in topic_data:
            # Convert old format to new format
            for sub in topic_data['sub_topics_detailed']:
                self.subtopics_treeview.insert('', 'end', text=sub.get('title', ''), values=(sub.get('description', ''),))
        elif 'sub_topics' in topic_data and isinstance(topic_data['sub_topics'], list):
            # Handle simple list of sub-topic titles
            for sub_title in topic_data['sub_topics']:
                if isinstance(sub_title, str):
                    self.subtopics_treeview.insert('', 'end', text=sub_title, values=('',))

        # Update backward compatibility property
        self.subtopics_data = topic_data.get('sub_topics', [])
    
    def clear(self):
        """Clear all input"""
        self.topic_title_var.set('')
        self.topic_desc_text.delete(1.0, tk.END)
        self.subtopics_treeview.delete(*self.subtopics_treeview.get_children())
    
    def load_from_file(self):
        """Load topic data from file"""
        filename = filedialog.askopenfilename(
            title="Load Topic Data",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if validate_topic_structure(data):
                    self.set_topic_data(data)
                    show_info("Load Successful", f"Topic data loaded from {filename}")
                else:
                    show_error("Invalid Format", "The file does not contain valid topic data.")
                    
            except Exception as e:
                show_error("Load Error", f"Failed to load file: {str(e)}")
    
    def save_to_file(self):
        """Save topic data to file"""
        topic_data = self.get_topic_data()
        if not topic_data:
            show_error("No Data", "Please enter topic data before saving.")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Save Topic Data",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(topic_data, f, indent=2, ensure_ascii=False)
                show_info("Save Successful", f"Topic data saved to {filename}")
            except Exception as e:
                show_error("Save Error", f"Failed to save file: {str(e)}")
    
    def import_csv(self):
        """Import sub-topics from CSV file (flat, as root-level sub-topics)"""
        filename = filedialog.askopenfilename(
            title="Import Sub-topics from CSV",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            try:
                import csv
                with open(filename, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    imported_count = 0
                    for row in reader:
                        if row and row[0].strip():
                            title = row[0].strip()
                            description = row[1].strip() if len(row) > 1 else ''
                            self.subtopics_treeview.insert('', 'end', text=title, values=(description,))
                            imported_count += 1
                show_info("Import Successful", f"Imported {imported_count} sub-topics from CSV.")
            except Exception as e:
                show_error("Import Error", f"Failed to import CSV: {str(e)}")
    
    def cleanup(self):
        """Cleanup resources"""
        pass


class SubtopicDialog:
    """Dialog for adding/editing sub-topics"""
    
    def __init__(self, parent: tk.Widget, title: str, initial_data: Optional[Dict[str, Any]] = None):
        self.result = None
        # Ensure parent is a tk.Tk or tk.Toplevel for transient
        master = parent.winfo_toplevel() if hasattr(parent, 'winfo_toplevel') else parent
        self.dialog = tk.Toplevel(master)
        self.dialog.title(title)
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        # Only set transient if master is a Toplevel or Tk (not a generic Widget)
        if isinstance(master, (tk.Tk, tk.Toplevel)):
            self.dialog.transient(master)
        self.dialog.grab_set()
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (200 // 2)
        self.dialog.geometry(f"400x200+{x}+{y}")
        self.setup_ui(initial_data)
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def setup_ui(self, initial_data: Optional[Dict[str, Any]]):
        """Set up dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title field
        ttk.Label(main_frame, text="Title:").pack(anchor=tk.W)
        self.title_var = tk.StringVar()
        title_entry = ttk.Entry(main_frame, textvariable=self.title_var)
        title_entry.pack(fill=tk.X, pady=(2, 10))
        title_entry.focus()
        
        # Description field
        ttk.Label(main_frame, text="Description:").pack(anchor=tk.W)
        self.desc_text = tk.Text(main_frame, height=4, wrap=tk.WORD)
        self.desc_text.pack(fill=tk.BOTH, expand=True, pady=(2, 10))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.RIGHT)
        
        # Load initial data
        if initial_data:
            self.title_var.set(initial_data.get('title', ''))
            self.desc_text.insert(1.0, initial_data.get('description', ''))
        
        # Bind Enter key to OK
        self.dialog.bind('<Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
    
    def ok_clicked(self):
        """Handle OK button click"""
        title = self.title_var.get().strip()
        if not title:
            messagebox.showerror("Invalid Input", "Please enter a title.", parent=self.dialog)
            return
        
        description = self.desc_text.get(1.0, tk.END).strip()
        
        self.result = {
            'title': title,
            'description': description
        }
        
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Handle Cancel button click"""
        self.dialog.destroy()
