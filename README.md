# AI Analysis Program

A comprehensive tool for topical AI analysis using the Ollama API, featuring interactive mind map visualizations and iterative/recursive analysis capabilities.

## Features

### Core Analysis Capabilities
- **Iterative Analysis**: Analyze main topics and sub-topics sequentially
- **Recursive Analysis**: Deep-dive analysis with configurable depth levels
- **Connection Finding**: Identify relationships and patterns between topics
- **Multiple AI Models**: Support for various Ollama models (llama2, mistral, etc.)

### Interactive Mind Maps
- **Visual Representation**: Convert analysis results into interactive mind maps
- **D3.js Powered**: Rich, interactive visualizations with zoom, pan, and navigation
- **Node Interactions**: Click nodes to view detailed analysis content
- **Export Options**: Save as HTML, PNG, or share interactive versions

### User Interface
- **Modern GUI**: Clean, intuitive Tkinter-based interface
- **Tabbed Results**: Organized display of summary, details, connections, and mind maps
- **Real-time Progress**: Live updates during analysis processing
- **File Operations**: Save/load analysis projects and export results

## Installation

### Prerequisites
- Python 3.8 or higher
- Ollama installed and running locally
- Internet connection for D3.js library (used in mind maps)

### Setup Steps

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd AI_Analysis_Program
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install and start Ollama**
   - Download from [ollama.ai](https://ollama.ai)
   - Install a model: `ollama pull llama2`
   - Start the service: `ollama serve`

4. **Run the program**
   ```bash
   python main.py
   ```

## Usage Guide

### Basic Analysis Workflow

1. **Enter Topic Information**
   - Add your main topic title and description
   - Add sub-topics for detailed analysis
   - Import topics from CSV files if needed

2. **Configure Analysis**
   - Choose analysis type (Iterative or Recursive)
   - Select AI model from available Ollama models
   - Adjust parameters (temperature, depth, etc.)

3. **Run Analysis**
   - Click "Start Analysis" to begin processing
   - Monitor progress in the status bar
   - View results in real-time

4. **Explore Results**
   - **Summary Tab**: Overview of analysis results
   - **Detailed Results**: Hierarchical view of all analyses
   - **Connections Tab**: Relationship analysis between topics
   - **Mind Map Tab**: Interactive visual representation
   - **Raw Data Tab**: Complete JSON data

5. **Export and Share**
   - Export results to Markdown or JSON
   - Generate interactive mind map HTML files
   - Save analysis projects for later use

### Analysis Types

#### Iterative Analysis
- Analyzes main topic first
- Processes each sub-topic sequentially
- Finds connections between all topics
- Best for: Structured topic exploration

#### Recursive Analysis
- Starts with main topic
- Automatically generates sub-topics
- Analyzes each level to specified depth
- Best for: Deep exploration of complex topics

### Mind Map Features

#### Interactive Elements
- **Zoom and Pan**: Navigate large mind maps smoothly
- **Node Clicking**: View detailed analysis in side panel
- **Hover Tooltips**: Quick preview of node information
- **Search**: Find specific topics or content
- **Layout Toggle**: Switch between force-directed and hierarchical layouts

#### Visual Design
- **Color Coding**: Different colors for analysis types and depths
- **Node Sizing**: Size reflects content importance or depth
- **Connection Lines**: Visual links showing topic relationships
- **Responsive Design**: Adapts to different screen sizes

## Configuration

### Application Settings
The program uses a `config.json` file for customization:

```json
{
  "ollama": {
    "base_url": "http://localhost:11434",
    "default_model": "llama2",
    "timeout": 30,
    "temperature": 0.7
  },
  "analysis": {
    "max_recursive_depth": 5,
    "connection_threshold": 0.7,
    "enable_caching": true
  },
  "export": {
    "default_format": "markdown",
    "output_directory": "exports"
  }
}
```

### Ollama Models
Supported models include:
- `llama2` - General purpose, good balance
- `mistral` - Fast and efficient
- `codellama` - Code-focused analysis
- `neural-chat` - Conversational analysis
- Custom models you've installed

## File Structure

```
AI_Analysis_Program/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── config.json            # Configuration file
├── README.md              # This file
├── test_program.py        # Test suite
│
├── utils/                 # Utility modules
│   ├── config.py          # Configuration management
│   ├── logging_setup.py   # Logging configuration
│   └── helpers.py         # Helper functions
│
├── gui/                   # User interface
│   ├── main_window.py     # Main application window
│   ├── topic_input.py     # Topic input panel
│   ├── analysis_panel.py  # Analysis control panel
│   └── results_viewer.py  # Results display
│
├── analysis/              # Analysis engine
│   ├── ollama_client.py   # Ollama API client
│   └── mindmap_generator.py # Mind map generation
│
├── data/                  # Data storage
├── templates/             # HTML templates
├── exports/               # Export output
│   └── mindmaps/         # Generated mind maps
└── logs/                  # Application logs
```

## Testing

Run the test suite to verify installation:

```bash
python test_program.py
```

The test suite checks:
- Module imports
- Configuration system
- Mind map generation
- File structure integrity
- Sample analysis processing

## Troubleshooting

### Common Issues

**Ollama Connection Failed**
- Ensure Ollama is installed and running
- Check if the service is accessible at `http://localhost:11434`
- Verify at least one model is installed: `ollama list`

**Mind Map Not Displaying**
- Check internet connection (required for D3.js)
- Ensure modern browser for HTML export viewing
- Verify export directory permissions

**Analysis Fails**
- Check selected model is available in Ollama
- Verify topic input is not empty
- Check logs in `logs/` directory for detailed errors

**GUI Issues**
- Ensure Python Tkinter is installed (usually included)
- Check display settings and screen resolution
- Try running with different Python versions

### Performance Tips

- Use smaller models (like `mistral`) for faster analysis
- Limit recursive depth for complex topics
- Enable caching for repeated analyses
- Close unused browser tabs when viewing mind maps

## Development

### Adding New Features

The program is designed for extensibility:

- **New Analysis Types**: Add to `analysis/` directory
- **GUI Components**: Extend existing panels or create new ones
- **Export Formats**: Add to export managers
- **Visualization Types**: Create new generators in `analysis/`

### Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 🚀 Enhanced Features

This application has been significantly enhanced with:

- **Professional GUI Design**: Modern interface with theming, collapsible sections, and enhanced visual feedback
- **Context-Aware Analysis**: Advanced analysis engine that understands topic relationships for better results
- **Comprehensive Testing**: Full test suite covering GUI components, analysis logic, and data flow
- **Enhanced User Experience**: Tooltips, keyboard shortcuts, progress indicators, and intuitive workflows

### Additional Documentation

- **[ENHANCEMENTS.md](ENHANCEMENTS.md)**: Detailed documentation of all improvements and new features
- **[USAGE_GUIDE.md](USAGE_GUIDE.md)**: Step-by-step guide for using enhanced features
- **[GUI_Improvements_Plan.md](GUI_Improvements_Plan.md)**: Technical details of GUI enhancements
- **[Enhanced_Analysis_Design.md](Enhanced_Analysis_Design.md)**: Analysis logic improvement specifications

## License

This project is open source. See LICENSE file for details.

## Support

For issues, questions, or contributions:
- Check the troubleshooting section above
- Review logs in the `logs/` directory
- Run the test suite to identify problems
- Create detailed issue reports with error messages

## Acknowledgments

- **Ollama**: For providing the local AI model infrastructure
- **D3.js**: For powerful data visualization capabilities
- **Python Community**: For excellent libraries and tools
