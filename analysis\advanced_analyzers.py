"""
Advanced Analysis Types for AI Analysis Program
Implements specialized analysis methods for different use cases
"""

import re
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
import math

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import calculate_similarity, extract_keywords

class AnalysisType(ABC, LoggerMixin):
    """Base class for all analysis types"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.parameters = {}
        self.output_format = {}
    
    @abstractmethod
    def analyze(self, data: Dict[str, Any], ollama_client) -> Dict[str, Any]:
        """Perform the analysis"""
        pass
    
    @abstractmethod
    def get_visualization_config(self) -> Dict[str, Any]:
        """Get configuration for visualization"""
        pass
    
    def validate_input(self, data: Dict[str, Any]) -> bool:
        """Validate input data"""
        return True

class ComparativeAnalyzer(AnalysisType):
    """Comparative analysis between multiple topics"""
    
    def __init__(self):
        super().__init__(
            "comparative",
            "Compare and contrast multiple topics or concepts"
        )
        self.parameters = {
            "max_topics": 10,
            "comparison_criteria": ["quality", "cost", "complexity", "feasibility"],
            "similarity_threshold": 0.7
        }
    
    def analyze(self, data: Dict[str, Any], ollama_client) -> Dict[str, Any]:
        """Perform comparative analysis"""
        topics = data.get('topics', [])
        if len(topics) < 2:
            return {"error": "At least 2 topics required for comparison"}
        
        # Get detailed analysis for each topic
        topic_analyses = {}
        for topic in topics:
            analysis = ollama_client.analyze_topic(
                topic, 
                analysis_type="detailed"
            )
            topic_analyses[topic] = analysis or f"Analysis for {topic}"
        
        # Generate comparison matrix
        comparison_matrix = self._generate_comparison_matrix(topics, topic_analyses, ollama_client)
        
        # Calculate similarities
        similarities = self._calculate_similarities(topic_analyses)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(topics, comparison_matrix, ollama_client)
        
        return {
            "type": "comparative",
            "topics": topics,
            "topic_analyses": topic_analyses,
            "comparison_matrix": comparison_matrix,
            "similarities": similarities,
            "recommendations": recommendations,
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "topic_count": len(topics)
            }
        }
    
    def _generate_comparison_matrix(self, topics: List[str], analyses: Dict[str, str], ollama_client) -> Dict[str, Any]:
        """Generate comparison matrix with scores"""
        criteria = self.parameters["comparison_criteria"]
        
        # Ask AI to score each topic on each criterion
        comparison_prompt = f"""
        Compare these topics: {', '.join(topics)}
        
        Rate each topic on a scale of 1-10 for each criterion:
        {', '.join(criteria)}
        
        Provide scores in JSON format:
        {{
            "Topic Name": {{"criterion1": score, "criterion2": score, ...}},
            ...
        }}
        
        Base your scores on the following analyses:
        {json.dumps(analyses, indent=2)}
        """
        
        response = ollama_client.generate(comparison_prompt)
        
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                scores = json.loads(json_match.group())
            else:
                # Fallback: generate default scores
                scores = {topic: {criterion: 5 for criterion in criteria} for topic in topics}
        except:
            # Fallback scores
            scores = {topic: {criterion: 5 for criterion in criteria} for topic in topics}
        
        return {
            "criteria": criteria,
            "scores": scores,
            "raw_response": response
        }
    
    def _calculate_similarities(self, analyses: Dict[str, str]) -> Dict[Tuple[str, str], float]:
        """Calculate similarity scores between topics"""
        similarities = {}
        topics = list(analyses.keys())
        
        for i in range(len(topics)):
            for j in range(i + 1, len(topics)):
                topic1, topic2 = topics[i], topics[j]
                similarity = calculate_similarity(analyses[topic1], analyses[topic2])
                similarities[(topic1, topic2)] = similarity
        
        return similarities
    
    def _generate_recommendations(self, topics: List[str], comparison_matrix: Dict[str, Any], ollama_client) -> str:
        """Generate recommendations based on comparison"""
        recommendation_prompt = f"""
        Based on the comparison analysis of these topics: {', '.join(topics)}
        
        Comparison scores: {json.dumps(comparison_matrix['scores'], indent=2)}
        
        Provide strategic recommendations including:
        1. Which topic performs best overall
        2. Strengths and weaknesses of each option
        3. Situational recommendations (when to choose each)
        4. Key decision factors to consider
        """
        
        return ollama_client.generate(recommendation_prompt) or "Unable to generate recommendations"
    
    def get_visualization_config(self) -> Dict[str, Any]:
        return {
            "type": "comparative",
            "charts": ["comparison_table", "radar_chart", "similarity_heatmap"],
            "colors": ["#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#F44336"]
        }

class SWOTAnalyzer(AnalysisType):
    """SWOT (Strengths, Weaknesses, Opportunities, Threats) Analysis"""
    
    def __init__(self):
        super().__init__(
            "swot",
            "Structured analysis of Strengths, Weaknesses, Opportunities, and Threats"
        )
        self.parameters = {
            "max_items_per_quadrant": 8,
            "prioritization_method": "impact_probability"
        }
    
    def analyze(self, data: Dict[str, Any], ollama_client) -> Dict[str, Any]:
        """Perform SWOT analysis"""
        topic = data.get('topic', '')
        context = data.get('context', '')
        
        if not topic:
            return {"error": "Topic is required for SWOT analysis"}
        
        # Generate SWOT analysis
        swot_matrix = self._generate_swot_matrix(topic, context, ollama_client)
        
        # Generate strategic recommendations
        recommendations = self._generate_strategic_recommendations(topic, swot_matrix, ollama_client)
        
        # Prioritize items
        prioritized_swot = self._prioritize_swot_items(swot_matrix, ollama_client)
        
        return {
            "type": "swot",
            "topic": topic,
            "context": context,
            "swot_matrix": swot_matrix,
            "prioritized_swot": prioritized_swot,
            "strategic_recommendations": recommendations,
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "total_items": sum(len(items) for items in swot_matrix.values())
            }
        }
    
    def _generate_swot_matrix(self, topic: str, context: str, ollama_client) -> Dict[str, List[str]]:
        """Generate SWOT matrix"""
        swot_prompt = f"""
        Perform a comprehensive SWOT analysis for: {topic}
        
        Context: {context}
        
        Identify and list:
        
        STRENGTHS (Internal positive factors):
        - List 4-6 key strengths
        
        WEAKNESSES (Internal negative factors):
        - List 4-6 key weaknesses
        
        OPPORTUNITIES (External positive factors):
        - List 4-6 key opportunities
        
        THREATS (External negative factors):
        - List 4-6 key threats
        
        Format your response clearly with each category and bullet points.
        """
        
        response = ollama_client.generate(swot_prompt)
        
        # Parse response to extract SWOT items
        swot_matrix = self._parse_swot_response(response)
        
        return swot_matrix
    
    def _parse_swot_response(self, response: str) -> Dict[str, List[str]]:
        """Parse SWOT response into structured format"""
        swot_matrix = {
            "strengths": [],
            "weaknesses": [],
            "opportunities": [],
            "threats": []
        }
        
        current_section = None
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Identify section headers
            if 'strength' in line.lower():
                current_section = 'strengths'
            elif 'weakness' in line.lower():
                current_section = 'weaknesses'
            elif 'opportunit' in line.lower():
                current_section = 'opportunities'
            elif 'threat' in line.lower():
                current_section = 'threats'
            elif line.startswith('-') or line.startswith('•') or line.startswith('*'):
                # Extract bullet point
                item = line[1:].strip()
                if current_section and item:
                    swot_matrix[current_section].append(item)
        
        # Ensure each category has at least some items
        for category in swot_matrix:
            if not swot_matrix[category]:
                swot_matrix[category] = [f"No {category} identified"]
        
        return swot_matrix
    
    def _prioritize_swot_items(self, swot_matrix: Dict[str, List[str]], ollama_client) -> Dict[str, List[Dict[str, Any]]]:
        """Prioritize SWOT items by impact and probability"""
        prioritized = {}
        
        for category, items in swot_matrix.items():
            prioritized_items = []
            
            for item in items:
                # Ask AI to score impact and probability
                scoring_prompt = f"""
                Rate this {category[:-1]} on a scale of 1-10:
                "{item}"
                
                Provide scores for:
                1. Impact (how significant is this factor?)
                2. Probability/Certainty (how likely/certain is this?)
                
                Respond with just two numbers: Impact Probability
                """
                
                response = ollama_client.generate(scoring_prompt)
                
                # Parse scores
                try:
                    scores = re.findall(r'\d+', response)
                    impact = int(scores[0]) if len(scores) > 0 else 5
                    probability = int(scores[1]) if len(scores) > 1 else 5
                except:
                    impact, probability = 5, 5
                
                priority_score = (impact * probability) / 10  # Normalize to 1-10 scale
                
                prioritized_items.append({
                    "item": item,
                    "impact": impact,
                    "probability": probability,
                    "priority_score": priority_score
                })
            
            # Sort by priority score
            prioritized_items.sort(key=lambda x: x["priority_score"], reverse=True)
            prioritized[category] = prioritized_items
        
        return prioritized
    
    def _generate_strategic_recommendations(self, topic: str, swot_matrix: Dict[str, List[str]], ollama_client) -> List[str]:
        """Generate strategic recommendations based on SWOT"""
        strategy_prompt = f"""
        Based on this SWOT analysis for {topic}:
        
        Strengths: {', '.join(swot_matrix['strengths'])}
        Weaknesses: {', '.join(swot_matrix['weaknesses'])}
        Opportunities: {', '.join(swot_matrix['opportunities'])}
        Threats: {', '.join(swot_matrix['threats'])}
        
        Provide 4-6 strategic recommendations using these approaches:
        1. SO Strategy (Strengths + Opportunities)
        2. WO Strategy (Weaknesses + Opportunities)
        3. ST Strategy (Strengths + Threats)
        4. WT Strategy (Weaknesses + Threats)
        
        Format as numbered list of actionable recommendations.
        """
        
        response = ollama_client.generate(strategy_prompt)
        
        # Parse recommendations
        recommendations = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if re.match(r'^\d+\.', line) or line.startswith('-'):
                recommendation = re.sub(r'^\d+\.\s*', '', line)
                recommendation = re.sub(r'^-\s*', '', recommendation)
                if recommendation:
                    recommendations.append(recommendation)
        
        return recommendations or ["Strategic recommendations could not be generated"]
    
    def get_visualization_config(self) -> Dict[str, Any]:
        return {
            "type": "swot",
            "charts": ["swot_matrix", "priority_quadrants", "strategy_map"],
            "colors": {
                "strengths": "#4CAF50",
                "weaknesses": "#F44336",
                "opportunities": "#2196F3",
                "threats": "#FF9800"
            }
        }

class TemporalAnalyzer(AnalysisType):
    """Temporal analysis across time dimensions"""
    
    def __init__(self):
        super().__init__(
            "temporal",
            "Analyze topics across time dimensions with trends and predictions"
        )
        self.parameters = {
            "time_periods": ["past", "present", "future"],
            "trend_analysis_depth": 5,
            "prediction_horizon": "5 years"
        }
    
    def analyze(self, data: Dict[str, Any], ollama_client) -> Dict[str, Any]:
        """Perform temporal analysis"""
        topic = data.get('topic', '')
        time_frame = data.get('time_frame', '10 years')
        
        if not topic:
            return {"error": "Topic is required for temporal analysis"}
        
        # Generate timeline analysis
        timeline = self._generate_timeline(topic, time_frame, ollama_client)
        
        # Identify trends
        trends = self._identify_trends(topic, timeline, ollama_client)
        
        # Generate predictions
        predictions = self._generate_predictions(topic, trends, ollama_client)
        
        # Analyze causality
        causality = self._analyze_causality(topic, timeline, ollama_client)
        
        return {
            "type": "temporal",
            "topic": topic,
            "time_frame": time_frame,
            "timeline": timeline,
            "trends": trends,
            "predictions": predictions,
            "causality": causality,
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "analysis_depth": len(timeline)
            }
        }
    
    def _generate_timeline(self, topic: str, time_frame: str, ollama_client) -> List[Dict[str, Any]]:
        """Generate timeline of key events and developments"""
        timeline_prompt = f"""
        Create a chronological timeline for: {topic}
        Time frame: {time_frame}
        
        Identify 8-12 key events, milestones, or developments in chronological order.
        For each event, provide:
        - Year or time period
        - Brief description
        - Significance/impact
        
        Format as a timeline with clear time markers.
        """
        
        response = ollama_client.generate(timeline_prompt)
        
        # Parse timeline response
        timeline = self._parse_timeline_response(response)
        
        return timeline
    
    def _parse_timeline_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse timeline response into structured format"""
        timeline = []
        lines = response.split('\n')
        
        current_event = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Look for year patterns
            year_match = re.search(r'(\d{4})', line)
            if year_match:
                if current_event:
                    timeline.append(current_event)
                
                current_event = {
                    "period": year_match.group(1),
                    "description": line,
                    "significance": ""
                }
            elif current_event and line:
                # Add to description or significance
                if "significance" in line.lower() or "impact" in line.lower():
                    current_event["significance"] = line
                else:
                    current_event["description"] += " " + line
        
        if current_event:
            timeline.append(current_event)
        
        # Ensure we have some timeline data
        if not timeline:
            timeline = [{
                "period": "Recent",
                "description": f"Development in {response[:100]}...",
                "significance": "Significant development"
            }]
        
        return timeline
    
    def _identify_trends(self, topic: str, timeline: List[Dict[str, Any]], ollama_client) -> Dict[str, List[str]]:
        """Identify trends from timeline analysis"""
        trends_prompt = f"""
        Based on this timeline for {topic}:
        
        {json.dumps(timeline, indent=2)}
        
        Identify key trends in these categories:
        
        INCREASING TRENDS (what's growing/expanding):
        - List 3-5 increasing trends
        
        DECREASING TRENDS (what's declining/reducing):
        - List 3-5 decreasing trends
        
        EMERGING TRENDS (what's new/developing):
        - List 3-5 emerging trends
        
        CYCLICAL PATTERNS (what repeats/cycles):
        - List 2-3 cyclical patterns
        """
        
        response = ollama_client.generate(trends_prompt)
        
        # Parse trends
        trends = self._parse_trends_response(response)
        
        return trends
    
    def _parse_trends_response(self, response: str) -> Dict[str, List[str]]:
        """Parse trends response"""
        trends = {
            "increasing": [],
            "decreasing": [],
            "emerging": [],
            "cyclical": []
        }
        
        current_category = None
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Identify category
            if 'increasing' in line.lower():
                current_category = 'increasing'
            elif 'decreasing' in line.lower() or 'declining' in line.lower():
                current_category = 'decreasing'
            elif 'emerging' in line.lower():
                current_category = 'emerging'
            elif 'cyclical' in line.lower():
                current_category = 'cyclical'
            elif line.startswith('-') or line.startswith('•'):
                # Extract trend item
                trend = line[1:].strip()
                if current_category and trend:
                    trends[current_category].append(trend)
        
        return trends
    
    def _generate_predictions(self, topic: str, trends: Dict[str, List[str]], ollama_client) -> str:
        """Generate future predictions based on trends"""
        prediction_prompt = f"""
        Based on these trends for {topic}:
        
        Increasing: {', '.join(trends.get('increasing', []))}
        Decreasing: {', '.join(trends.get('decreasing', []))}
        Emerging: {', '.join(trends.get('emerging', []))}
        Cyclical: {', '.join(trends.get('cyclical', []))}
        
        Provide predictions for the next 5-10 years including:
        1. Most likely developments
        2. Potential disruptions
        3. Key uncertainties
        4. Strategic implications
        
        Be specific and actionable in your predictions.
        """
        
        return ollama_client.generate(prediction_prompt) or "Predictions could not be generated"
    
    def _analyze_causality(self, topic: str, timeline: List[Dict[str, Any]], ollama_client) -> str:
        """Analyze cause-effect relationships in timeline"""
        causality_prompt = f"""
        Analyze the cause-effect relationships in this timeline for {topic}:
        
        {json.dumps(timeline, indent=2)}
        
        Identify:
        1. Key causal relationships between events
        2. Root causes of major developments
        3. Cascading effects and consequences
        4. External factors that influenced the timeline
        
        Explain the causal chains and their significance.
        """
        
        return ollama_client.generate(causality_prompt) or "Causality analysis could not be generated"
    
    def get_visualization_config(self) -> Dict[str, Any]:
        return {
            "type": "temporal",
            "charts": ["timeline", "trend_graph", "prediction_chart", "causality_diagram"],
            "colors": ["#4CAF50", "#2196F3", "#FF9800", "#9C27B0"]
        }

class AnalysisRegistry(LoggerMixin):
    """Registry for managing different analysis types"""
    
    def __init__(self):
        self.analyzers = {}
        self._register_default_analyzers()
    
    def _register_default_analyzers(self):
        """Register default analysis types"""
        self.register(ComparativeAnalyzer())
        self.register(SWOTAnalyzer())
        self.register(TemporalAnalyzer())
    
    def register(self, analyzer: AnalysisType):
        """Register a new analysis type"""
        self.analyzers[analyzer.name] = analyzer
        self.logger.info(f"Registered analysis type: {analyzer.name}")
    
    def get_analyzer(self, name: str) -> Optional[AnalysisType]:
        """Get analyzer by name"""
        return self.analyzers.get(name)
    
    def list_available(self) -> List[Dict[str, str]]:
        """List all available analysis types"""
        return [
            {
                "name": analyzer.name,
                "description": analyzer.description
            }
            for analyzer in self.analyzers.values()
        ]
    
    def analyze(self, analysis_type: str, data: Dict[str, Any], ollama_client) -> Dict[str, Any]:
        """Perform analysis using specified type"""
        analyzer = self.get_analyzer(analysis_type)
        if not analyzer:
            return {"error": f"Unknown analysis type: {analysis_type}"}
        
        try:
            if not analyzer.validate_input(data):
                return {"error": "Invalid input data for analysis type"}
            
            return analyzer.analyze(data, ollama_client)
        except Exception as e:
            self.logger.error(f"Analysis failed for type {analysis_type}: {e}")
            return {"error": f"Analysis failed: {str(e)}"}

# Global registry instance
analysis_registry = AnalysisRegistry()
