"""
Theme management for AI Analysis Program
Provides consistent styling and theming across the application
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class ColorScheme:
    """Color scheme definition"""
    # Primary colors
    primary: str = "#2E3440"
    accent: str = "#5E81AC"
    success: str = "#A3BE8C"
    warning: str = "#EBCB8B"
    error: str = "#BF616A"

    # Background colors
    bg_main: str = "#ECEFF4"
    bg_secondary: str = "#E5E9F0"
    bg_panel: str = "#FFFFFF"
    bg_hover: str = "#D8DEE9"
    bg_selected: str = "#88C0D0"

    # Text colors
    text_primary: str = "#2E3440"
    text_secondary: str = "#4C566A"
    text_muted: str = "#5E81AC"
    text_inverse: str = "#ECEFF4"

    # Border colors
    border_light: str = "#D8DEE9"
    border_medium: str = "#4C566A"
    border_dark: str = "#2E3440"

@dataclass
class Typography:
    """Typography definitions"""
    # Font families
    font_main: str = "Segoe UI"
    font_mono: str = "Consolas"
    font_fallback: str = "Arial"

    # Font sizes
    size_small: int = 8
    size_normal: int = 10
    size_medium: int = 11
    size_large: int = 12
    size_xlarge: int = 14
    size_xxlarge: int = 16

    # Font weights
    weight_normal: str = "normal"
    weight_bold: str = "bold"

@dataclass
class Spacing:
    """Spacing and sizing definitions"""
    # Padding
    pad_xs: int = 4
    pad_sm: int = 8
    pad_md: int = 12
    pad_lg: int = 16
    pad_xl: int = 24

    # Margins
    margin_xs: int = 2
    margin_sm: int = 4
    margin_md: int = 8
    margin_lg: int = 12
    margin_xl: int = 16

    # Border radius
    radius_sm: int = 3
    radius_md: int = 5
    radius_lg: int = 8

class ThemeManager:
    """Manages application theming and styling"""

    def __init__(self, root: tk.Tk):
        self.root = root
        self.style = ttk.Style()
        self.colors = ColorScheme()
        self.typography = Typography()
        self.spacing = Spacing()

        # Initialize theme
        self.setup_theme()

    def setup_theme(self):
        """Set up the application theme"""
        # Configure ttk style
        self.style.theme_use('clam')  # Use clam as base theme

        # Configure root window
        self.root.configure(bg=self.colors.bg_main)

        # Set up custom styles
        self.setup_button_styles()
        self.setup_frame_styles()
        self.setup_label_styles()
        self.setup_entry_styles()
        self.setup_notebook_styles()
        self.setup_treeview_styles()
        self.setup_progressbar_styles()
        self.setup_scale_styles()

    def setup_button_styles(self):
        """Configure button styles"""
        # Primary button
        self.style.configure(
            "Primary.TButton",
            background=self.colors.accent,
            foreground=self.colors.text_inverse,
            borderwidth=0,
            focuscolor="none",
            padding=(self.spacing.pad_md, self.spacing.pad_sm),
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        self.style.map(
            "Primary.TButton",
            background=[
                ("active", self.colors.bg_selected),
                ("pressed", self.colors.primary)
            ],
            foreground=[
                ("active", self.colors.text_inverse),
                ("pressed", self.colors.text_inverse)
            ]
        )

        # Success button
        self.style.configure(
            "Success.TButton",
            background=self.colors.success,
            foreground=self.colors.text_primary,
            borderwidth=0,
            focuscolor="none",
            padding=(self.spacing.pad_md, self.spacing.pad_sm),
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        # Warning button
        self.style.configure(
            "Warning.TButton",
            background=self.colors.warning,
            foreground=self.colors.text_primary,
            borderwidth=0,
            focuscolor="none",
            padding=(self.spacing.pad_md, self.spacing.pad_sm),
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        # Error button
        self.style.configure(
            "Error.TButton",
            background=self.colors.error,
            foreground=self.colors.text_inverse,
            borderwidth=0,
            focuscolor="none",
            padding=(self.spacing.pad_md, self.spacing.pad_sm),
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        # Default button improvements
        self.style.configure(
            "TButton",
            background=self.colors.bg_panel,
            foreground=self.colors.text_primary,
            borderwidth=1,
            bordercolor=self.colors.border_light,
            focuscolor="none",
            padding=(self.spacing.pad_md, self.spacing.pad_sm),
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        self.style.map(
            "TButton",
            background=[
                ("active", self.colors.bg_hover),
                ("pressed", self.colors.bg_secondary)
            ],
            bordercolor=[
                ("active", self.colors.border_medium),
                ("pressed", self.colors.border_dark)
            ]
        )

    def setup_frame_styles(self):
        """Configure frame styles"""
        # Main frame
        self.style.configure(
            "Main.TFrame",
            background=self.colors.bg_main,
            borderwidth=0
        )

        # Panel frame
        self.style.configure(
            "Panel.TFrame",
            background=self.colors.bg_panel,
            borderwidth=1,
            bordercolor=self.colors.border_light,
            relief="solid"
        )

        # Card frame
        self.style.configure(
            "Card.TFrame",
            background=self.colors.bg_panel,
            borderwidth=1,
            bordercolor=self.colors.border_light,
            relief="solid"
        )

        # Default frame improvements
        self.style.configure(
            "TFrame",
            background=self.colors.bg_panel,
            borderwidth=0
        )

        # LabelFrame styles
        self.style.configure(
            "TLabelFrame",
            background=self.colors.bg_panel,
            borderwidth=1,
            bordercolor=self.colors.border_light,
            relief="solid"
        )

        self.style.configure(
            "Panel.TLabelFrame",
            background=self.colors.bg_panel,
            borderwidth=1,
            bordercolor=self.colors.border_light,
            relief="solid"
        )

        self.style.configure(
            "TLabelFrame.Label",
            background=self.colors.bg_panel,
            foreground=self.colors.text_primary,
            font=(self.typography.font_main, self.typography.size_medium, self.typography.weight_bold)
        )

    def setup_label_styles(self):
        """Configure label styles"""
        # Header labels
        self.style.configure(
            "Header.TLabel",
            background=self.colors.bg_panel,
            foreground=self.colors.text_primary,
            font=(self.typography.font_main, self.typography.size_large, self.typography.weight_bold)
        )

        # Subheader labels
        self.style.configure(
            "Subheader.TLabel",
            background=self.colors.bg_panel,
            foreground=self.colors.text_primary,
            font=(self.typography.font_main, self.typography.size_medium, self.typography.weight_bold)
        )

        # Muted labels
        self.style.configure(
            "Muted.TLabel",
            background=self.colors.bg_panel,
            foreground=self.colors.text_muted,
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        # Success labels
        self.style.configure(
            "Success.TLabel",
            background=self.colors.bg_panel,
            foreground=self.colors.success,
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        # Error labels
        self.style.configure(
            "Error.TLabel",
            background=self.colors.bg_panel,
            foreground=self.colors.error,
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        # Default label improvements
        self.style.configure(
            "TLabel",
            background=self.colors.bg_panel,
            foreground=self.colors.text_primary,
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

    def setup_entry_styles(self):
        """Configure entry and text widget styles"""
        self.style.configure(
            "TEntry",
            fieldbackground=self.colors.bg_panel,
            foreground=self.colors.text_primary,
            borderwidth=1,
            bordercolor=self.colors.border_light,
            insertcolor=self.colors.text_primary,
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        self.style.map(
            "TEntry",
            bordercolor=[
                ("focus", self.colors.accent),
                ("active", self.colors.border_medium)
            ]
        )

    def setup_notebook_styles(self):
        """Configure notebook (tab) styles"""
        self.style.configure(
            "TNotebook",
            background=self.colors.bg_panel,
            borderwidth=0
        )

        self.style.configure(
            "TNotebook.Tab",
            background=self.colors.bg_secondary,
            foreground=self.colors.text_primary,
            padding=(self.spacing.pad_md, self.spacing.pad_sm),
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        self.style.map(
            "TNotebook.Tab",
            background=[
                ("selected", self.colors.bg_panel),
                ("active", self.colors.bg_hover)
            ],
            foreground=[
                ("selected", self.colors.text_primary),
                ("active", self.colors.text_primary)
            ]
        )

    def setup_treeview_styles(self):
        """Configure treeview styles"""
        self.style.configure(
            "Treeview",
            background=self.colors.bg_panel,
            foreground=self.colors.text_primary,
            fieldbackground=self.colors.bg_panel,
            borderwidth=1,
            bordercolor=self.colors.border_light,
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_normal)
        )

        self.style.configure(
            "Treeview.Heading",
            background=self.colors.bg_secondary,
            foreground=self.colors.text_primary,
            font=(self.typography.font_main, self.typography.size_normal, self.typography.weight_bold)
        )

        self.style.map(
            "Treeview",
            background=[("selected", self.colors.bg_selected)],
            foreground=[("selected", self.colors.text_primary)]
        )

    def setup_progressbar_styles(self):
        """Configure progress bar styles"""
        self.style.configure(
            "TProgressbar",
            background=self.colors.accent,
            troughcolor=self.colors.bg_secondary,
            borderwidth=0,
            lightcolor=self.colors.accent,
            darkcolor=self.colors.accent
        )

    def setup_scale_styles(self):
        """Configure scale (slider) styles"""
        self.style.configure(
            "TScale",
            background=self.colors.bg_panel,
            troughcolor=self.colors.bg_secondary,
            borderwidth=0,
            slidercolor=self.colors.accent
        )

    def get_font(self, size: str = "normal", weight: str = "normal", family: str = "main") -> tuple:
        """Get a font tuple with specified parameters"""
        font_family = getattr(self.typography, f"font_{family}", self.typography.font_main)
        font_size = getattr(self.typography, f"size_{size}", self.typography.size_normal)
        font_weight = getattr(self.typography, f"weight_{weight}", self.typography.weight_normal)

        return (font_family, font_size, font_weight)

    def get_color(self, color_name: str) -> str:
        """Get a color value by name"""
        return getattr(self.colors, color_name, self.colors.text_primary)

    def get_spacing(self, spacing_name: str) -> int:
        """Get a spacing value by name"""
        return getattr(self.spacing, spacing_name, self.spacing.pad_md)