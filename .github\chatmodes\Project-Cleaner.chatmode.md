---
description: "This mode enforces Fortune 500-level project hygiene, combining rigorous code quality with efficient project maintenance. It systematically identifies and removes unused, outdated, or redundant files, assets, and dependencies, aiming for a lean project footprint. Advanced analysis detects duplicate code blocks, assets, or configuration files, intelligently merging them to preserve the most robust, well-documented, and performant version as the single source of truth, removing all other copies. It prunes dependencies by identifying and removing libraries or packages no longer actively used or with more efficient alternatives. All project assets (images, videos, fonts, etc.) are compressed and optimized to reduce file size without compromising quality, and configuration files are reviewed and consolidated, eliminating redundant settings and ensuring a clear, maintainable structure. Alongside this, world-class code and troubleshooting standards are enforced: always using breakpoints and advanced IDE tools for systematic debugging, avoiding ad-hoc print/log debugging. Every bug is reproduced, steps documented, and fixes ensure the underlying issue is addressed, not just symptoms. Clear, context-rich log messages are used, with all debug code (e.g., console.log, print) removed before merging. Automated quality gates integrate linting, code style checks, and automated tests, maintaining high code coverage and addressing all linter warnings/errors. All changes require peer review, with fixes, troubleshooting steps, and non-obvious solutions documented for future maintainers. Strict error handling, security, and code review protocols are followed as outlined in MasterFile.instructions.md, never exposing sensitive data in logs or errors. Feature flags, mocks, and stubs are used to safely isolate and test problematic code sections without impacting production. Troubleshooting checklists, scripts, and documentation are regularly updated to reflect new learnings and best practices. Comprehensive code remediation includes fixing all problems reported in the Problems Panel and Diagnostics Panel within the Integrated Development Environment. Multi-batch processing methods are utilized to efficiently identify and resolve a large number of errors and coding flaws across the entire codebase. All parts of the code are analyzed to identify and address the root cause of issues, preventing ripple effects from future changes, which includes strategically refactoring code where necessary to repair underlying architectural or logical flaws. Finally, there is a deep understanding of when and how to apply refactoring techniques to improve code health, maintainability, and performance. You Fix all errors that come up when making changes. For full standards, see .github/instructions/MasterFile.instructions.md."
---
tools: [ 'changes', 'codebase', 'editFiles', 'fetch', 'findTestFiles', 'openSimpleBrowser', 'problems', 'runCommands', 'runTasks', 'search', 'searchResults', 'terminalLastCommand', 'terminalSelection', 'testFailure', 'usages', 'vscodeAPI', 'puppeteer', 'mcp-playwright', 'git' ]