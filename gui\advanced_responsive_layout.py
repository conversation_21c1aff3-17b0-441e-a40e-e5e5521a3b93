"""
Advanced Responsive Layout Manager
Implements dynamic panel sizing and adaptive layouts based on screen size and content requirements.
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, <PERSON><PERSON>, Callable
from utils.logging_setup import LoggerMixin


class ScreenSizeDetector:
    """Detects and categorizes screen sizes for responsive layouts."""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.breakpoints = {
            'small': 1400,
            'medium': 1800, 
            'large': 2200,
            'ultra_wide': 2500
        }
        self.current_category = None
        self.resize_callbacks = []
        
    def get_screen_dimensions(self) -> Tuple[int, int]:
        """Get current screen dimensions."""
        return self.root.winfo_screenwidth(), self.root.winfo_screenheight()
        
    def get_window_dimensions(self) -> Tuple[int, int]:
        """Get current window dimensions."""
        return self.root.winfo_width(), self.root.winfo_height()
        
    def get_screen_category(self) -> str:
        """Determine screen size category based on width."""
        width, _ = self.get_screen_dimensions()
        
        if width < self.breakpoints['small']:
            return 'small'
        elif width < self.breakpoints['medium']:
            return 'medium'
        elif width < self.breakpoints['large']:
            return 'large'
        elif width < self.breakpoints['ultra_wide']:
            return 'ultra_wide'
        else:
            return 'ultra_wide_plus'
            
    def get_effective_category(self) -> str:
        """Get effective category based on current window size."""
        window_width, _ = self.get_window_dimensions()
        
        if window_width < self.breakpoints['small']:
            return 'small'
        elif window_width < self.breakpoints['medium']:
            return 'medium'
        elif window_width < self.breakpoints['large']:
            return 'large'
        elif window_width < self.breakpoints['ultra_wide']:
            return 'ultra_wide'
        else:
            return 'ultra_wide_plus'
            
    def register_resize_callback(self, callback: Callable):
        """Register callback for window resize events."""
        self.resize_callbacks.append(callback)
        
    def check_for_category_change(self):
        """Check if screen category has changed and notify callbacks."""
        new_category = self.get_effective_category()
        if new_category != self.current_category:
            old_category = self.current_category
            self.current_category = new_category
            
            # Notify all callbacks
            for callback in self.resize_callbacks:
                try:
                    callback(new_category, old_category)
                except Exception as e:
                    print(f"Error in resize callback: {e}")


class AdaptivePanelManager:
    """Manages adaptive panel sizing and layout distribution."""
    
    def __init__(self, main_container: tk.Widget):
        self.main_container = main_container
        self.panels = {}
        self.panel_weights = {}
        self.min_widths = {
            'topic_input': 350,
            'analysis_panel': 400,
            'results_viewer': 500
        }
        
        # Responsive ratios for different screen categories
        self.weight_ratios = {
            'small': {'topic_input': 1, 'analysis_panel': 1, 'results_viewer': 2},
            'medium': {'topic_input': 1, 'analysis_panel': 1.5, 'results_viewer': 2.5},
            'large': {'topic_input': 1, 'analysis_panel': 2, 'results_viewer': 3},
            'ultra_wide': {'topic_input': 1, 'analysis_panel': 2, 'results_viewer': 4},
            'ultra_wide_plus': {'topic_input': 1, 'analysis_panel': 2.5, 'results_viewer': 4.5}
        }
        
    def register_panel(self, panel_id: str, panel_widget: tk.Widget, initial_weight: int = 1):
        """Register a panel for adaptive management."""
        self.panels[panel_id] = panel_widget
        self.panel_weights[panel_id] = initial_weight
        
    def apply_layout_for_category(self, category: str):
        """Apply layout weights for specific screen category."""
        if category not in self.weight_ratios:
            category = 'medium'  # Default fallback
            
        ratios = self.weight_ratios[category]
        
        # Apply weights to registered panels
        for panel_id, panel_widget in self.panels.items():
            if panel_id in ratios:
                weight = int(ratios[panel_id] * 100)  # Scale for better precision
                self.panel_weights[panel_id] = weight
                
                # Apply weight if panel is in a PanedWindow
                parent = panel_widget.master
                if isinstance(parent, (tk.PanedWindow, ttk.PanedWindow)):
                    try:
                        # Find the pane containing this widget
                        panes = parent.panes()
                        for i, pane in enumerate(panes):
                            if str(pane) == str(panel_widget):
                                parent.paneconfigure(pane, weight=weight)
                                break
                    except Exception as e:
                        print(f"Error configuring pane weight: {e}")
                        
    def get_optimal_widths(self, total_width: int, category: str) -> Dict[str, int]:
        """Calculate optimal widths for panels based on total available width."""
        if category not in self.weight_ratios:
            category = 'medium'
            
        ratios = self.weight_ratios[category]
        total_ratio = sum(ratios.values())
        
        # Calculate base widths
        widths = {}
        remaining_width = total_width
        
        # First, ensure minimum widths
        for panel_id in ratios:
            min_width = self.min_widths.get(panel_id, 300)
            widths[panel_id] = min_width
            remaining_width -= min_width
            
        # Distribute remaining width proportionally
        if remaining_width > 0:
            for panel_id, ratio in ratios.items():
                additional_width = int((ratio / total_ratio) * remaining_width)
                widths[panel_id] += additional_width
                
        return widths


class ContentOverflowManager:
    """Manages content overflow and adaptive content display."""
    
    def __init__(self):
        self.overflow_handlers = {}
        self.content_priority = {}
        self.overflow_indicators = {}
        
    def register_content_area(self, area_id: str, widget: tk.Widget, priority: int = 1):
        """Register a content area for overflow management."""
        self.overflow_handlers[area_id] = widget
        self.content_priority[area_id] = priority
        
    def check_overflow(self, area_id: str) -> bool:
        """Check if content area has overflow."""
        if area_id not in self.overflow_handlers:
            return False
            
        widget = self.overflow_handlers[area_id]
        
        try:
            # For Text widgets
            if isinstance(widget, tk.Text):
                # Check if scrollbar is needed
                widget.update_idletasks()
                total_lines = int(widget.index('end-1c').split('.')[0])
                visible_lines = int(widget.winfo_height() / widget.tk.call('font', 'metrics', widget['font'], '-linespace'))
                return total_lines > visible_lines
                
            # For Frame widgets with scrollable content
            elif hasattr(widget, 'canvas') and hasattr(widget, 'scrollable_frame'):
                widget.canvas.update_idletasks()
                bbox = widget.canvas.bbox("all")
                if bbox:
                    content_height = bbox[3] - bbox[1]
                    visible_height = widget.canvas.winfo_height()
                    return content_height > visible_height
                    
        except Exception:
            pass
            
        return False
        
    def add_overflow_indicator(self, area_id: str, parent: tk.Widget):
        """Add visual overflow indicator."""
        if area_id not in self.overflow_indicators:
            indicator = tk.Label(
                parent,
                text="▼ More content below",
                font=('Arial', 8),
                fg='blue',
                cursor='hand2'
            )
            self.overflow_indicators[area_id] = indicator
            
        return self.overflow_indicators[area_id]
        
    def update_overflow_indicators(self):
        """Update all overflow indicators based on current content."""
        for area_id in self.overflow_handlers:
            has_overflow = self.check_overflow(area_id)
            if area_id in self.overflow_indicators:
                indicator = self.overflow_indicators[area_id]
                if has_overflow:
                    indicator.pack(side='bottom', pady=2)
                else:
                    indicator.pack_forget()


class ResponsiveLayoutManager(LoggerMixin):
    """Main responsive layout manager that coordinates all adaptive behaviors."""
    
    def __init__(self, root: tk.Tk):
        super().__init__()
        self.root = root
        self.screen_detector = ScreenSizeDetector(root)
        self.panel_manager = None
        self.overflow_manager = ContentOverflowManager()
        
        # Layout state
        self.current_layout = None
        self.layout_callbacks = []
        self.content_callbacks = []
        
        # Setup responsive behavior
        self.setup_responsive_behavior()
        
    def setup_responsive_behavior(self):
        """Setup responsive behavior and event bindings."""
        # Register for screen size changes
        self.screen_detector.register_resize_callback(self.on_screen_category_changed)
        
        # Bind window resize events
        self.root.bind('<Configure>', self.on_window_resize)
        
        # Initial layout application
        self.root.after(100, self.apply_initial_layout)
        
    def set_main_container(self, container: tk.Widget):
        """Set the main container for panel management."""
        self.panel_manager = AdaptivePanelManager(container)
        
    def register_panel(self, panel_id: str, panel_widget: tk.Widget, initial_weight: int = 1):
        """Register a panel for responsive management."""
        if self.panel_manager:
            self.panel_manager.register_panel(panel_id, panel_widget, initial_weight)
            
    def register_content_area(self, area_id: str, widget: tk.Widget, priority: int = 1):
        """Register content area for overflow management."""
        self.overflow_manager.register_content_area(area_id, widget, priority)
        
    def register_layout_callback(self, callback: Callable):
        """Register callback for layout changes."""
        self.layout_callbacks.append(callback)
        
    def register_content_callback(self, callback: Callable):
        """Register callback for content changes."""
        self.content_callbacks.append(callback)
        
    def apply_initial_layout(self):
        """Apply initial responsive layout."""
        category = self.screen_detector.get_effective_category()
        self.apply_layout_for_category(category)
        self.logger.info(f"Applied initial responsive layout for category: {category}")
        
    def on_screen_category_changed(self, new_category: str, old_category: str):
        """Handle screen category changes."""
        self.logger.info(f"Screen category changed from {old_category} to {new_category}")
        self.apply_layout_for_category(new_category)
        
        # Notify layout callbacks
        for callback in self.layout_callbacks:
            try:
                callback(new_category, old_category)
            except Exception as e:
                self.logger.error(f"Error in layout callback: {e}")
                
    def on_window_resize(self, event):
        """Handle window resize events."""
        if event.widget == self.root:
            # Debounce resize events
            if hasattr(self, '_resize_timer'):
                self.root.after_cancel(self._resize_timer)
                
            self._resize_timer = self.root.after(250, self.handle_resize_complete)
            
    def handle_resize_complete(self):
        """Handle completed resize operation."""
        self.screen_detector.check_for_category_change()
        self.overflow_manager.update_overflow_indicators()
        
        # Notify content callbacks
        for callback in self.content_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"Error in content callback: {e}")
                
    def apply_layout_for_category(self, category: str):
        """Apply layout configuration for specific screen category."""
        if self.panel_manager:
            self.panel_manager.apply_layout_for_category(category)
            
        self.current_layout = category
        
        # Apply category-specific configurations
        self.apply_category_specific_settings(category)
        
    def apply_category_specific_settings(self, category: str):
        """Apply category-specific UI settings."""
        # Font size adjustments
        font_sizes = {
            'small': 9,
            'medium': 10,
            'large': 11,
            'ultra_wide': 12,
            'ultra_wide_plus': 13
        }
        
        # Button size adjustments
        button_paddings = {
            'small': (5, 2),
            'medium': (8, 3),
            'large': (10, 4),
            'ultra_wide': (12, 5),
            'ultra_wide_plus': (15, 6)
        }
        
        # Apply settings through style configuration
        style = ttk.Style()
        
        try:
            font_size = font_sizes.get(category, 10)
            padding = button_paddings.get(category, (8, 3))
            
            # Configure button styles
            style.configure('Responsive.TButton', padding=padding)
            style.configure('Responsive.TLabel', font=('Arial', font_size))
            
        except Exception as e:
            self.logger.warning(f"Could not apply category-specific styles: {e}")
            
    def get_current_layout_info(self) -> Dict:
        """Get current layout information."""
        category = self.screen_detector.get_effective_category()
        window_width, window_height = self.screen_detector.get_window_dimensions()
        
        layout_info = {
            'category': category,
            'window_width': window_width,
            'window_height': window_height,
            'panel_weights': self.panel_manager.panel_weights if self.panel_manager else {},
            'has_overflow': {}
        }
        
        # Check overflow status for all registered areas
        for area_id in self.overflow_manager.overflow_handlers:
            layout_info['has_overflow'][area_id] = self.overflow_manager.check_overflow(area_id)
            
        return layout_info
        
    def force_layout_update(self):
        """Force a complete layout update."""
        self.handle_resize_complete()
        
    def get_optimal_panel_widths(self, total_width: int) -> Dict[str, int]:
        """Get optimal panel widths for current layout."""
        if self.panel_manager:
            category = self.screen_detector.get_effective_category()
            return self.panel_manager.get_optimal_widths(total_width, category)
        return {}


class ResponsiveWidget:
    """Base class for widgets that adapt to responsive layout changes."""
    
    def __init__(self, widget: tk.Widget, layout_manager: ResponsiveLayoutManager):
        self.widget = widget
        self.layout_manager = layout_manager
        self.responsive_properties = {}
        
        # Register for layout updates
        layout_manager.register_layout_callback(self.on_layout_changed)
        
    def set_responsive_property(self, property_name: str, values_by_category: Dict[str, str]):
        """Set property values for different screen categories."""
        self.responsive_properties[property_name] = values_by_category
        
    def on_layout_changed(self, new_category: str, old_category: str):
        """Handle layout category changes."""
        for property_name, category_values in self.responsive_properties.items():
            if new_category in category_values:
                value = category_values[new_category]
                try:
                    self.widget.configure(**{property_name: value})
                except Exception as e:
                    print(f"Error setting responsive property {property_name}: {e}")


def create_responsive_layout_manager(root: tk.Tk) -> ResponsiveLayoutManager:
    """Factory function to create and configure a responsive layout manager."""
    return ResponsiveLayoutManager(root)
