<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Analysis Mind Map</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }
        
        #container {
            display: flex;
            height: 100vh;
        }
        
        #mindmap-area {
            flex: 1;
            position: relative;
            background: white;
        }
        
        #mindmap-svg {
            width: 100%;
            height: 100%;
            cursor: grab;
        }
        
        #mindmap-svg:active {
            cursor: grabbing;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #details-panel {
            width: 300px;
            background: #fff;
            border-left: 1px solid #ddd;
            padding: 20px;
            overflow-y: auto;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }
        
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            stroke: #333;
            stroke-width: 2px;
        }
        
        .node.selected {
            stroke: #ff6b6b;
            stroke-width: 3px;
        }
        
        .node-label {
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            pointer-events: none;
            fill: #333;
        }
        
        .link {
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 2px;
            fill: none;
        }
        
        .link.connection {
            stroke: #ff9800;
            stroke-dasharray: 5,5;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            max-width: 200px;
        }
        
        .control-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .control-button:hover {
            background: #45a049;
        }
        
        .search-box {
            width: 150px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 2px;
        }
        
        #node-details h3 {
            margin-top: 0;
            color: #333;
        }
        
        #node-details p {
            line-height: 1.5;
            color: #666;
        }
        
        .analysis-content {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="mindmap-area">
            <svg id="mindmap-svg"></svg>
            <div id="controls">
                <button class="control-button" onclick="resetZoom()">Reset View</button>
                <button class="control-button" onclick="exportPNG()">Export PNG</button>
                <button class="control-button" onclick="toggleLayout()">Toggle Layout</button>
                <br>
                <input type="text" class="search-box" id="search-input" placeholder="Search nodes..." onkeyup="searchNodes()">
            </div>
            <div class="tooltip" id="tooltip" style="display: none;"></div>
        </div>
        <div id="details-panel">
            <div id="node-details">
                <h3>Select a node to view details</h3>
                <p>Click on any node in the mind map to see detailed analysis information.</p>
            </div>
        </div>
    </div>

    <script>
        // Mind map data will be injected here
        const mindmapData = {
  "nodes": [
    {
      "id": "root",
      "label": "Test",
      "type": "root",
      "size": 50,
      "color": "#4CAF50",
      "analysis": "",
      "x": 400,
      "y": 300
    }
  ],
  "links": [],
  "metadata": {
    "model": "tinyllama:latest",
    "timestamp": "2025-07-20T15:31:56.239202"
  }
};
        
        // Initialize mind map
        let svg, g, simulation, nodes, links;
        let width, height;
        let selectedNode = null;
        let currentLayout = 'force';
        
        function initMindMap() {
            // Set up SVG
            svg = d3.select("#mindmap-svg");
            width = parseInt(svg.style("width"));
            height = parseInt(svg.style("height"));
            
            // Create main group for zooming/panning
            g = svg.append("g");
            
            // Set up zoom behavior
            const zoom = d3.zoom()
                .scaleExtent([0.1, 4])
                .on("zoom", (event) => {
                    g.attr("transform", event.transform);
                });
            
            svg.call(zoom);
            
            // Process data
            nodes = mindmapData.nodes.map(d => ({...d}));
            links = mindmapData.links.map(d => ({...d}));
            
            // Create force simulation
            createForceSimulation();
            
            // Render mind map
            renderMindMap();
        }
        
        function createForceSimulation() {
            simulation = d3.forceSimulation(nodes)
                .force("link", d3.forceLink(links).id(d => d.id).distance(100))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(width / 2, height / 2))
                .force("collision", d3.forceCollide().radius(d => d.size + 10));
        }
        
        function renderMindMap() {
            // Clear existing elements
            g.selectAll("*").remove();
            
            // Create links
            const link = g.selectAll(".link")
                .data(links)
                .enter().append("line")
                .attr("class", d => `link ${d.type || ''}`)
                .attr("stroke-width", d => Math.sqrt(d.strength * 5) || 2);
            
            // Create nodes
            const node = g.selectAll(".node")
                .data(nodes)
                .enter().append("g")
                .attr("class", "node")
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended))
                .on("click", nodeClicked)
                .on("mouseover", nodeMouseOver)
                .on("mouseout", nodeMouseOut);
            
            // Add circles to nodes
            node.append("circle")
                .attr("r", d => d.size || 20)
                .attr("fill", d => d.color || "#4CAF50")
                .attr("stroke", "#fff")
                .attr("stroke-width", 2);
            
            // Add labels to nodes
            node.append("text")
                .attr("class", "node-label")
                .attr("dy", ".35em")
                .text(d => d.label || d.id)
                .style("font-size", d => `${Math.max(10, (d.size || 20) / 3)}px`);
            
            // Update positions on simulation tick
            simulation.on("tick", () => {
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                node.attr("transform", d => `translate(${d.x},${d.y})`);
            });
        }
        
        function nodeClicked(event, d) {
            // Update selection
            d3.selectAll(".node").classed("selected", false);
            d3.select(this).classed("selected", true);
            selectedNode = d;
            
            // Update details panel
            updateDetailsPanel(d);
        }
        
        function nodeMouseOver(event, d) {
            const tooltip = d3.select("#tooltip");
            tooltip.style("display", "block")
                .html(`<strong>${d.label}</strong><br>${d.type || 'Node'}<br>Click for details`)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px");
        }
        
        function nodeMouseOut() {
            d3.select("#tooltip").style("display", "none");
        }
        
        function updateDetailsPanel(node) {
            const detailsDiv = document.getElementById("node-details");
            
            let html = `<h3>${node.label}</h3>`;
            html += `<p><strong>Type:</strong> ${node.type || 'Unknown'}</p>`;
            
            if (node.analysis) {
                html += `<div class="analysis-content">`;
                html += `<h4>Analysis:</h4>`;
                html += `<p>${node.analysis}</p>`;
                html += `</div>`;
            }
            
            if (node.connections && node.connections.length > 0) {
                html += `<h4>Connections:</h4>`;
                html += `<ul>`;
                node.connections.forEach(conn => {
                    html += `<li>${conn}</li>`;
                });
                html += `</ul>`;
            }
            
            detailsDiv.innerHTML = html;
        }
        
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }
        
        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }
        
        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
        
        function resetZoom() {
            svg.transition().duration(750).call(
                d3.zoom().transform,
                d3.zoomIdentity
            );
        }
        
        function exportPNG() {
            // This would implement PNG export functionality
            alert("PNG export functionality would be implemented here");
        }
        
        function toggleLayout() {
            if (currentLayout === 'force') {
                // Switch to hierarchical layout
                currentLayout = 'hierarchical';
                createHierarchicalLayout();
            } else {
                // Switch back to force layout
                currentLayout = 'force';
                createForceSimulation();
            }
        }
        
        function createHierarchicalLayout() {
            // Implement hierarchical layout
            simulation.stop();
            
            // Simple hierarchical positioning
            const root = nodes.find(n => n.type === 'root');
            if (root) {
                root.fx = width / 2;
                root.fy = height / 2;
                
                const children = nodes.filter(n => n.parent === root.id);
                children.forEach((child, i) => {
                    const angle = (2 * Math.PI * i) / children.length;
                    child.fx = root.fx + 150 * Math.cos(angle);
                    child.fy = root.fy + 150 * Math.sin(angle);
                });
            }
            
            simulation.alpha(1).restart();
        }
        
        function searchNodes() {
            const searchTerm = document.getElementById("search-input").value.toLowerCase();
            
            d3.selectAll(".node").style("opacity", 1);
            
            if (searchTerm) {
                d3.selectAll(".node")
                    .style("opacity", d => 
                        d.label.toLowerCase().includes(searchTerm) || 
                        (d.analysis && d.analysis.toLowerCase().includes(searchTerm)) ? 1 : 0.3
                    );
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', initMindMap);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            width = parseInt(svg.style("width"));
            height = parseInt(svg.style("height"));
            simulation.force("center", d3.forceCenter(width / 2, height / 2));
            simulation.alpha(1).restart();
        });
    </script>
</body>
</html>