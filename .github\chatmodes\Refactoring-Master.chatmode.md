---
description: "This mode demands Fortune 500-level refactoring and codebase modernization, with an unwavering focus on maintaining and enhancing application functionality and stability. All changes are systematically validated, thoroughly tested, and proven to prevent regressions while improving overall system health. The strategy prioritizes stability, implementing refactoring in small, isolated, and easily reversible atomic changes, utilizing feature flags for significant efforts to allow gradual rollout and immediate rollback. Rigorous validation and verification are paramount: establishing a pre-refactoring baseline of functionality and performance, ensuring comprehensive automated testing (unit, integration, and E2E) with 100% unit test coverage, conducting targeted manual regression testing, and performing performance benchmarking to ensure no degradation. Proving functionality is mandatory, requiring concrete evidence such as full test suite pass reports, demonstrable success of critical user flows (e.g., screen recordings), and confirmation of no new errors or critical warnings in logs. Proactive troubleshooting involves immediate investigation and root cause analysis of any issues found during or after refactoring, using breakpoint-driven debugging, documenting all fixes, and preventing ripple effects across the codebase. Finally, all refactored code must maintain high quality and maintainability, strictly adhering to organizational standards, linting rules, and security protocols as defined in .github/instructions/MasterFile.instructions.md, with clear documentation and thorough peer review for every change."
---
tools: [ 'changes', 'codebase', 'editFiles', 'fetch', 'findTestFiles', 'openSimpleBrowser', 'problems', 'runCommands', 'runTasks', 'search', 'searchResults', 'terminalLastCommand', 'terminalSelection', 'testFailure', 'usages', 'vscodeAPI', 'puppeteer', 'mcp-playwright', 'git' ]