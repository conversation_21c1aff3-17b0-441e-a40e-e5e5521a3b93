"""
High-Performance Scrolling System
Optimized scrolling implementation to eliminate blur and lag issues.
"""

import tkinter as tk
import time


class PerformanceMonitor:
    """Monitor and track scrolling performance metrics."""
    
    def __init__(self):
        self.frame_times = []
        self.max_samples = 60
        self.performance_threshold = 16.67  # 60fps target
        self.last_frame_time = 0
        
    def record_frame(self, frame_time: float):
        """Record frame time for performance analysis."""
        self.frame_times.append(frame_time)
        if len(self.frame_times) > self.max_samples:
            self.frame_times.pop(0)
            
    def get_average_fps(self) -> float:
        """Calculate average FPS from recorded frame times."""
        if not self.frame_times:
            return 0
        avg_frame_time = sum(self.frame_times) / len(self.frame_times)
        return 1000 / avg_frame_time if avg_frame_time > 0 else 0
        
    def is_performance_poor(self) -> bool:
        """Check if performance is below acceptable threshold."""
        return self.get_average_fps() < 30
        
    def start_frame(self):
        """Mark the start of a frame for timing."""
        self.last_frame_time = time.time() * 1000
        
    def end_frame(self):
        """Mark the end of a frame and record timing."""
        if self.last_frame_time > 0:
            frame_duration = (time.time() * 1000) - self.last_frame_time
            self.record_frame(frame_duration)


class EasingFunctions:
    """Collection of easing functions for smooth animations."""
    
    @staticmethod
    def ease_out_cubic(t: float) -> float:
        """Cubic ease-out function for natural motion."""
        return 1 - pow(1 - t, 3)
        
    @staticmethod
    def ease_in_out_cubic(t: float) -> float:
        """Cubic ease-in-out function for balanced motion."""
        return 4 * t * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 3) / 2
        
    @staticmethod
    def ease_out_quart(t: float) -> float:
        """Quartic ease-out for very smooth deceleration."""
        return 1 - pow(1 - t, 4)


class OptimizedScrolling:
    """High-performance scrolling with adaptive frame rates and smart animations."""
    
    def __init__(self, canvas: tk.Canvas, performance_mode: bool = True):
        self.canvas = canvas
        self.performance_mode = performance_mode
        self.performance_monitor = PerformanceMonitor()
        
        # Scrolling state
        self.is_scrolling = False
        self.scroll_animation_id = None
        self.scroll_queue = []
        
        # Performance settings
        self.target_fps = 60
        self.frame_time = 1000 / self.target_fps  # 16.67ms
        self.min_frame_time = 8  # Minimum 8ms between frames
        self.max_frame_time = 33  # Maximum 33ms (30fps fallback)
        
        # Animation settings
        self.scroll_duration = 150  # 150ms total animation
        self.scroll_threshold = 5  # Minimum pixels to trigger smooth scroll
        
        # Setup optimized event bindings
        self.setup_event_bindings()
        
    def setup_event_bindings(self):
        """Setup optimized mouse and keyboard event bindings."""
        # Mouse wheel with high performance handling
        self.canvas.bind("<MouseWheel>", self.optimized_mousewheel, "+")
        self.canvas.bind("<Button-4>", self.optimized_mousewheel, "+")
        self.canvas.bind("<Button-5>", self.optimized_mousewheel, "+")
        
        # Keyboard navigation
        self.canvas.bind("<Key-Up>", lambda e: self.scroll_by_units(-3), "+")
        self.canvas.bind("<Key-Down>", lambda e: self.scroll_by_units(3), "+")
        self.canvas.bind("<Key-Page_Up>", lambda e: self.scroll_by_page(-1), "+")
        self.canvas.bind("<Key-Page_Down>", lambda e: self.scroll_by_page(1), "+")
        self.canvas.bind("<Key-Home>", lambda e: self.scroll_to_top(), "+")
        self.canvas.bind("<Key-End>", lambda e: self.scroll_to_bottom(), "+")
        
    def optimized_mousewheel(self, event):
        """Optimized mouse wheel handler with performance considerations."""
        # Calculate scroll delta
        if hasattr(event, 'delta'):
            delta = -1 * (event.delta / 120)
        elif event.num == 4:
            delta = -1
        elif event.num == 5:
            delta = 1
        else:
            return "break"
            
        # Performance mode: direct scrolling for maximum responsiveness
        if self.performance_mode or self.performance_monitor.is_performance_poor():
            self.direct_scroll(delta)
        else:
            self.smooth_scroll(delta)
            
        return "break"
        
    def direct_scroll(self, delta: float):
        """Direct scrolling without animation for maximum performance."""
        scroll_amount = int(delta * 3)  # 3 units per delta
        self.canvas.yview_scroll(scroll_amount, "units")
        
    def smooth_scroll(self, delta: float):
        """Smooth scrolling with performance optimization."""
        # Cancel existing animation if running
        if self.scroll_animation_id:
            self.canvas.after_cancel(self.scroll_animation_id)
            self.scroll_animation_id = None
            
        # Queue scroll if already scrolling
        if self.is_scrolling:
            self.scroll_queue.append(delta)
            return
            
        # Calculate scroll distance
        scroll_distance = delta * 120  # Pixels to scroll
        
        # Use direct scroll for small movements
        if abs(scroll_distance) < self.scroll_threshold:
            self.direct_scroll(delta)
            return
            
        # Start smooth scroll animation
        self.start_smooth_scroll(scroll_distance)
        
    def start_smooth_scroll(self, scroll_distance: float):
        """Start optimized smooth scroll animation."""
        self.is_scrolling = True
        self.performance_monitor.start_frame()
        
        # Get current scroll position
        scroll_top = self.canvas.canvasy(0)
        scrollregion = self.canvas.cget("scrollregion").split()
        
        if len(scrollregion) == 4:
            total_height = float(scrollregion[3])
            max_scroll = max(0, total_height - self.canvas.winfo_height())
            
            # Calculate target position
            target_scroll = max(0, min(max_scroll, scroll_top + scroll_distance))
            
            # Start animation
            start_time = time.time() * 1000
            self.animate_scroll(start_time, scroll_top, target_scroll)
            
    def animate_scroll(self, start_time: float, start_pos: float, target_pos: float):
        """Optimized scroll animation with adaptive frame rate."""
        current_time = time.time() * 1000
        elapsed = current_time - start_time
        
        # Check if animation is complete
        if elapsed >= self.scroll_duration:
            self.finish_scroll(target_pos)
            return
            
        # Calculate progress with easing
        progress = elapsed / self.scroll_duration
        eased_progress = EasingFunctions.ease_out_cubic(progress)
        
        # Calculate current position
        current_pos = start_pos + (target_pos - start_pos) * eased_progress
        
        # Update scroll position
        self.set_scroll_position(current_pos)
        
        # Record performance
        self.performance_monitor.end_frame()
        
        # Calculate adaptive frame time
        avg_fps = self.performance_monitor.get_average_fps()
        if avg_fps > 0:
            adaptive_frame_time = max(self.min_frame_time, 
                                    min(self.max_frame_time, 1000 / avg_fps))
        else:
            adaptive_frame_time = self.frame_time
            
        # Schedule next frame
        self.scroll_animation_id = self.canvas.after(
            int(adaptive_frame_time),
            lambda: self.animate_scroll(start_time, start_pos, target_pos)
        )
        
        self.performance_monitor.start_frame()
        
    def finish_scroll(self, target_pos: float):
        """Finish scroll animation and process queue."""
        # Set final position
        self.set_scroll_position(target_pos)
        
        # Clear animation state
        self.is_scrolling = False
        self.scroll_animation_id = None
        
        # Process queued scrolls
        if self.scroll_queue:
            next_delta = self.scroll_queue.pop(0)
            self.smooth_scroll(next_delta)
            
    def set_scroll_position(self, position: float):
        """Set scroll position efficiently."""
        scrollregion = self.canvas.cget("scrollregion").split()
        if len(scrollregion) == 4:
            total_height = float(scrollregion[3])
            if total_height > 0:
                fraction = position / total_height
                self.canvas.yview_moveto(fraction)
                
    def scroll_by_units(self, units: int):
        """Scroll by specified units."""
        if self.performance_mode:
            self.canvas.yview_scroll(units, "units")
        else:
            self.smooth_scroll(units / 3.0)
            
    def scroll_by_page(self, pages: int):
        """Scroll by page."""
        if self.performance_mode:
            self.canvas.yview_scroll(pages, "pages")
        else:
            page_delta = pages * 10  # Approximate page size
            self.smooth_scroll(page_delta)
            
    def scroll_to_top(self):
        """Scroll to the top of content."""
        if self.performance_mode:
            self.canvas.yview_moveto(0)
        else:
            current_pos = self.canvas.canvasy(0)
            self.start_smooth_scroll(-current_pos)
            
    def scroll_to_bottom(self):
        """Scroll to the bottom of content."""
        if self.performance_mode:
            self.canvas.yview_moveto(1)
        else:
            scrollregion = self.canvas.cget("scrollregion").split()
            if len(scrollregion) == 4:
                total_height = float(scrollregion[3])
                max_scroll = max(0, total_height - self.canvas.winfo_height())
                current_pos = self.canvas.canvasy(0)
                scroll_distance = max_scroll - current_pos
                self.start_smooth_scroll(scroll_distance)
                
    def set_performance_mode(self, enabled: bool):
        """Toggle performance mode."""
        self.performance_mode = enabled
        
        # Cancel any running animations when switching to performance mode
        if enabled and self.scroll_animation_id:
            self.canvas.after_cancel(self.scroll_animation_id)
            self.scroll_animation_id = None
            self.is_scrolling = False
            self.scroll_queue.clear()
            
    def get_performance_stats(self) -> dict:
        """Get current performance statistics."""
        return {
            'average_fps': self.performance_monitor.get_average_fps(),
            'performance_mode': self.performance_mode,
            'is_scrolling': self.is_scrolling,
            'queue_length': len(self.scroll_queue)
        }


class HighPerformanceScrollableFrame(tk.Frame):
    """Enhanced scrollable frame with high-performance scrolling."""
    
    def __init__(self, parent, performance_mode=True, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Performance settings
        self.performance_mode = performance_mode
        
        # Create scrollable canvas setup
        self.setup_scrollable_canvas()
        
        # Setup high-performance scrolling
        self.scrolling = OptimizedScrolling(self.canvas, performance_mode)
        
        # Configure canvas for optimal performance
        self.optimize_canvas()
        
    def setup_scrollable_canvas(self):
        """Setup the scrollable canvas with optimized configuration."""
        # Create canvas with performance optimizations
        self.canvas = tk.Canvas(
            self,
            highlightthickness=0,
            borderwidth=0,
            relief='flat',
            takefocus=True  # Allow keyboard focus
        )
        
        # Create scrollbars
        self.v_scrollbar = tk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.h_scrollbar = tk.Scrollbar(self, orient="horizontal", command=self.canvas.xview)
        
        # Configure canvas scrolling
        self.canvas.configure(
            yscrollcommand=self.v_scrollbar.set,
            xscrollcommand=self.h_scrollbar.set
        )
        
        # Create inner frame for content
        self.scrollable_frame = tk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window(
            (0, 0), 
            window=self.scrollable_frame, 
            anchor="nw"
        )
        
        # Layout scrollbars and canvas
        self.canvas.grid(row=0, column=0, sticky="nsew")
        self.v_scrollbar.grid(row=0, column=1, sticky="ns")
        self.h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Configure grid weights
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # Bind resize events
        self.scrollable_frame.bind("<Configure>", self.on_frame_configure)
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        
    def optimize_canvas(self):
        """Apply canvas optimizations for better performance."""
        # Configure canvas for performance
        self.canvas.configure(
            # Disable expensive visual features when needed
            bg=self.cget('bg') or 'white',
            # Optimize scrolling behavior
            confine=True,
            # Reduce visual complexity
            selectbackground='lightblue',
            selectforeground='black'
        )
        
        # Enable focus for keyboard events
        self.canvas.focus_set()
        
    def on_frame_configure(self, event=None):
        """Handle frame resize with performance optimization."""
        # Update scroll region efficiently
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def on_canvas_configure(self, event):
        """Handle canvas resize with performance optimization."""
        # Update inner frame width to match canvas
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)
        
    def add_widget(self, widget_class, **kwargs):
        """Add widget to scrollable frame."""
        widget = widget_class(self.scrollable_frame, **kwargs)
        return widget
        
    def set_performance_mode(self, enabled: bool):
        """Set performance mode for scrolling."""
        self.performance_mode = enabled
        self.scrolling.set_performance_mode(enabled)
        
    def get_performance_stats(self) -> dict:
        """Get scrolling performance statistics."""
        return self.scrolling.get_performance_stats()
        
    def scroll_to_top(self):
        """Scroll to top of content."""
        self.scrolling.scroll_to_top()
        
    def scroll_to_bottom(self):
        """Scroll to bottom of content."""
        self.scrolling.scroll_to_bottom()


class PerformanceSettings:
    """Performance settings and adaptive quality management."""
    
    def __init__(self):
        self.settings = {
            'performance_mode': True,
            'adaptive_quality': True,
            'smooth_scrolling': False,  # Default off for better performance
            'animation_duration': 150,
            'target_fps': 60,
            'auto_performance_adjust': True
        }
        
    def get_setting(self, key: str):
        """Get performance setting value."""
        return self.settings.get(key)
        
    def set_setting(self, key: str, value):
        """Set performance setting value."""
        self.settings[key] = value
        
    def apply_performance_profile(self, profile: str):
        """Apply predefined performance profile."""
        profiles = {
            'maximum_performance': {
                'performance_mode': True,
                'smooth_scrolling': False,
                'animation_duration': 0,
                'adaptive_quality': False
            },
            'balanced': {
                'performance_mode': False,
                'smooth_scrolling': True,
                'animation_duration': 150,
                'adaptive_quality': True
            },
            'smooth_experience': {
                'performance_mode': False,
                'smooth_scrolling': True,
                'animation_duration': 200,
                'adaptive_quality': False
            }
        }
        
        if profile in profiles:
            self.settings.update(profiles[profile])
            
    def get_recommended_settings(self, performance_stats: dict) -> dict:
        """Get recommended settings based on performance."""
        avg_fps = performance_stats.get('average_fps', 60)
        
        if avg_fps < 30:
            return {'performance_mode': True, 'smooth_scrolling': False}
        elif avg_fps < 45:
            return {'performance_mode': False, 'smooth_scrolling': True, 'animation_duration': 100}
        else:
            return {'performance_mode': False, 'smooth_scrolling': True, 'animation_duration': 150}
