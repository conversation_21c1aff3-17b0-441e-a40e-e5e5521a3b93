{"ollama": {"base_url": "http://localhost:11434", "default_model": "llama2", "timeout": 30, "temperature": 0.7, "max_tokens": 2048}, "analysis": {"max_recursive_depth": 5, "connection_threshold": 0.7, "batch_size": 10, "enable_caching": true}, "export": {"default_format": "markdown", "output_directory": "exports", "include_metadata": true, "timestamp_format": "%Y%m%d_%H%M%S"}, "gui": {"theme": "dark", "font_size": 10, "auto_save": true, "save_interval": 300}, "logging": {"level": "INFO", "file_logging": true, "console_logging": true, "log_directory": "logs"}, "performance": {"max_concurrent_analyses": 4, "cache_ttl_hours": 24, "max_memory_mb": 512, "enable_streaming": true, "ui_update_interval_ms": 100}}