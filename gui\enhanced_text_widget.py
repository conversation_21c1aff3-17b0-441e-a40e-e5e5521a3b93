"""
Enhanced Text Widget with Smart Content Management
Provides advanced text handling, multi-column layouts, dynamic font scaling, and content overflow management.
"""

import tkinter as tk
from tkinter import font
import re
import textwrap
from typing import Dict, List
from utils.logging_setup import LoggerMixin


class SmartTextFormatter:
    """Handles intelligent text formatting and layout."""
    
    def __init__(self):
        self.line_length_target = 80
        self.paragraph_spacing = 1
        self.heading_spacing = 2
        
    def format_text_multicolumn(self, text: str, width: int, columns: int = 1) -> str:
        """Format text into multiple columns for wide displays."""
        if columns <= 1:
            return self.format_text_single_column(text, width)
            
        # Split text into paragraphs
        paragraphs = text.split('\n\n')
        formatted_paragraphs = []
        
        # Calculate column width (accounting for spacing)
        column_spacing = 4  # spaces between columns
        column_width = (width - (column_spacing * (columns - 1))) // columns
        
        for paragraph in paragraphs:
            if len(paragraph.strip()) == 0:
                formatted_paragraphs.append('')
                continue
                
            # Wrap paragraph to column width
            wrapped = textwrap.fill(paragraph, width=column_width)
            formatted_paragraphs.append(wrapped)
            
        # Distribute paragraphs across columns
        return self.distribute_to_columns(formatted_paragraphs, columns, column_width)
        
    def format_text_single_column(self, text: str, width: int) -> str:
        """Format text for single column display."""
        paragraphs = text.split('\n\n')
        formatted_paragraphs = []
        
        for paragraph in paragraphs:
            if len(paragraph.strip()) == 0:
                formatted_paragraphs.append('')
                continue
                
            # Detect if this is a heading (short line, possibly all caps or title case)
            if self.is_heading(paragraph):
                formatted_paragraphs.append(paragraph.strip())
            else:
                wrapped = textwrap.fill(paragraph, width=width)
                formatted_paragraphs.append(wrapped)
                
        return '\n\n'.join(formatted_paragraphs)
        
    def distribute_to_columns(self, paragraphs: List[str], columns: int, column_width: int) -> str:
        """Distribute paragraphs across multiple columns."""
        # For now, implement simple column distribution
        # More sophisticated balancing can be added later
        
        column_texts = [[] for _ in range(columns)]
        current_column = 0
        
        for paragraph in paragraphs:
            if paragraph.strip():
                column_texts[current_column].append(paragraph)
                current_column = (current_column + 1) % columns
            else:
                # Add empty paragraph to current column
                if column_texts[current_column]:
                    column_texts[current_column].append('')
                    
        # Format columns side by side
        max_lines = max(len('\n'.join(col).split('\n')) for col in column_texts if col)
        
        formatted_lines = []
        for line_idx in range(max_lines):
            line_parts = []
            for col in column_texts:
                col_text = '\n'.join(col)
                col_lines = col_text.split('\n')
                if line_idx < len(col_lines):
                    line_content = col_lines[line_idx].ljust(column_width)
                else:
                    line_content = ' ' * column_width
                line_parts.append(line_content)
            formatted_lines.append('    '.join(line_parts))
            
        return '\n'.join(formatted_lines)
        
    def is_heading(self, text: str) -> bool:
        """Determine if text line is likely a heading."""
        text = text.strip()
        if len(text) == 0:
            return False
            
        # Heuristics for heading detection
        if len(text) < 60 and (text.isupper() or text.istitle()):
            return True
        if text.endswith(':') and len(text) < 80:
            return True
        if re.match(r'^#+\s', text):  # Markdown headers
            return True
        if re.match(r'^\d+\.\s', text):  # Numbered sections
            return True
            
        return False
        
    def calculate_optimal_columns(self, total_width: int, font_size: int) -> int:
        """Calculate optimal number of columns based on width and font size."""
        # Estimate character width based on font size
        char_width = font_size * 0.6  # Rough estimate
        chars_per_line = total_width / char_width
        
        # Optimal reading length is 50-75 characters per line
        if chars_per_line > 150:
            return 3
        elif chars_per_line > 100:
            return 2
        else:
            return 1


class DynamicFontManager:
    """Manages dynamic font scaling based on available space and content."""
    
    def __init__(self):
        self.base_font_size = 10
        self.min_font_size = 8
        self.max_font_size = 16
        self.font_cache = {}
        
    def calculate_optimal_font_size(self, text: str, available_width: int, available_height: int) -> int:
        """Calculate optimal font size to fit text in available space."""
        # Start with base font size and adjust
        font_size = self.base_font_size
        
        # Create a test font for measurement
        test_font = font.Font(family="Arial", size=font_size)
        
        # Measure text dimensions
        text_width = test_font.measure(text)
        text_height = test_font.metrics("linespace") * text.count('\n')
        
        # Adjust font size if text is too large
        while (text_width > available_width or text_height > available_height) and font_size > self.min_font_size:
            font_size -= 1
            test_font.configure(size=font_size)
            text_width = test_font.measure(text)
            text_height = test_font.metrics("linespace") * text.count('\n')
            
        # Increase font size if there's extra space
        while font_size < self.max_font_size:
            test_size = font_size + 1
            test_font.configure(size=test_size)
            test_width = test_font.measure(text)
            test_height = test_font.metrics("linespace") * text.count('\n')
            
            if test_width <= available_width and test_height <= available_height:
                font_size = test_size
            else:
                break
                
        return max(self.min_font_size, min(self.max_font_size, font_size))
        
    def get_font(self, family: str, size: int, weight: str = "normal") -> font.Font:
        """Get cached font or create new one."""
        cache_key = (family, size, weight)
        if cache_key not in self.font_cache:
            font_weight = "bold" if weight == "bold" else "normal"
            self.font_cache[cache_key] = font.Font(family=family, size=size, weight=font_weight)
        return self.font_cache[cache_key]
        
    def scale_font_for_category(self, base_size: int, category: str) -> int:
        """Scale font size based on screen category."""
        scale_factors = {
            'small': 0.9,
            'medium': 1.0,
            'large': 1.1,
            'ultra_wide': 1.2,
            'ultra_wide_plus': 1.3
        }
        
        factor = scale_factors.get(category, 1.0)
        return max(self.min_font_size, min(self.max_font_size, int(base_size * factor)))


class ExpandableSection:
    """Creates collapsible/expandable content sections."""
    
    def __init__(self, parent: tk.Widget, title: str, content: str, initial_state: str = 'collapsed'):
        self.parent = parent
        self.title = title
        self.content = content
        self.is_expanded = initial_state == 'expanded'
        
        self.create_section()
        
    def create_section(self):
        """Create the expandable section UI."""
        # Section frame
        self.section_frame = tk.Frame(self.parent, relief='groove', bd=1)
        
        # Header with expand/collapse button
        self.header_frame = tk.Frame(self.section_frame, bg='lightgray')
        self.header_frame.pack(fill='x', padx=2, pady=2)
        
        # Toggle button
        self.toggle_button = tk.Button(
            self.header_frame,
            text='▼' if self.is_expanded else '▶',
            command=self.toggle_expansion,
            font=('Arial', 8),
            width=3,
            relief='flat'
        )
        self.toggle_button.pack(side='left', padx=(5, 2))
        
        # Title label
        self.title_label = tk.Label(
            self.header_frame,
            text=self.title,
            font=('Arial', 10, 'bold'),
            bg='lightgray'
        )
        self.title_label.pack(side='left', padx=(2, 5))
        
        # Content frame
        self.content_frame = tk.Frame(self.section_frame)
        if self.is_expanded:
            self.content_frame.pack(fill='both', expand=True, padx=5, pady=5)
            
        # Content text
        self.content_text = tk.Text(
            self.content_frame,
            wrap='word',
            height=5,
            font=('Arial', 9)
        )
        self.content_text.pack(fill='both', expand=True)
        self.content_text.insert('1.0', self.content)
        self.content_text.configure(state='disabled')
        
    def toggle_expansion(self):
        """Toggle section expansion state."""
        self.is_expanded = not self.is_expanded
        
        if self.is_expanded:
            self.toggle_button.configure(text='▼')
            self.content_frame.pack(fill='both', expand=True, padx=5, pady=5)
        else:
            self.toggle_button.configure(text='▶')
            self.content_frame.pack_forget()
            
    def pack(self, **kwargs):
        """Pack the section frame."""
        self.section_frame.pack(**kwargs)
        
    def grid(self, **kwargs):
        """Grid the section frame."""
        self.section_frame.grid(**kwargs)


class EnhancedTextWidget(tk.Text, LoggerMixin):
    """Enhanced text widget with smart content management and responsive features."""
    
    def __init__(self, parent, layout_manager=None, **kwargs):
        # Remove custom kwargs before passing to Text
        self.layout_manager = layout_manager
        self.enable_multicolumn = kwargs.pop('enable_multicolumn', True)
        self.enable_dynamic_font = kwargs.pop('enable_dynamic_font', True)
        self.enable_smart_wrapping = kwargs.pop('enable_smart_wrapping', True)
        
        super().__init__(parent, **kwargs)
        LoggerMixin.__init__(self)
        
        # Initialize components
        self.formatter = SmartTextFormatter()
        self.font_manager = DynamicFontManager()
        
        # State tracking
        self.current_columns = 1
        self.current_font_size = 10
        self.original_content = ""
        self.formatted_content = ""
        
        # Setup enhanced features
        self.setup_enhanced_features()
        
        # Register with layout manager
        if self.layout_manager:
            self.layout_manager.register_content_callback(self.on_layout_changed)
            
    def setup_enhanced_features(self):
        """Setup enhanced text widget features."""
        # Configure text wrapping
        self.configure(wrap='word')
        
        # Bind resize events
        self.bind('<Configure>', self.on_widget_resize)
        
        # Setup context menu
        self.setup_context_menu()
        
        # Setup search functionality
        self.setup_search()
        
    def setup_context_menu(self):
        """Setup right-click context menu."""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Copy", command=self.copy_selection)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Select All", command=self.select_all)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Export Content", command=self.export_content)
        self.context_menu.add_command(label="Search Content", command=self.show_search)
        
        self.bind("<Button-3>", self.show_context_menu)
        
    def setup_search(self):
        """Setup search functionality."""
        self.search_frame = None
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        
    def show_context_menu(self, event):
        """Show context menu."""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
            
    def copy_selection(self):
        """Copy selected text to clipboard."""
        try:
            selected_text = self.get(tk.SEL_FIRST, tk.SEL_LAST)
            self.clipboard_clear()
            self.clipboard_append(selected_text)
        except tk.TclError:
            pass
            
    def select_all(self):
        """Select all text."""
        self.tag_add(tk.SEL, "1.0", tk.END)
        self.mark_set(tk.INSERT, "1.0")
        self.see(tk.INSERT)
        
    def export_content(self):
        """Export content to file."""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.get("1.0", tk.END))
                self.logger.info(f"Content exported to {filename}")
            except Exception as e:
                self.logger.error(f"Failed to export content: {e}")
                
    def show_search(self):
        """Show search interface."""
        if self.search_frame is None:
            self.create_search_frame()
        else:
            self.toggle_search_frame()
            
    def create_search_frame(self):
        """Create search interface."""
        self.search_frame = tk.Frame(self.master)
        
        tk.Label(self.search_frame, text="Search:").pack(side='left', padx=2)
        
        self.search_entry = tk.Entry(self.search_frame, textvariable=self.search_var, width=20)
        self.search_entry.pack(side='left', padx=2)
        
        tk.Button(self.search_frame, text="Next", command=self.search_next, width=6).pack(side='left', padx=1)
        tk.Button(self.search_frame, text="Close", command=self.close_search, width=6).pack(side='left', padx=1)
        
        self.search_frame.pack(side='top', fill='x', padx=5, pady=2)
        self.search_entry.focus_set()
        
    def toggle_search_frame(self):
        """Toggle search frame visibility."""
        if self.search_frame and self.search_frame.winfo_viewable():
            self.search_frame.pack_forget()
        elif self.search_frame:
            self.search_frame.pack(side='top', fill='x', padx=5, pady=2)
            self.search_entry.focus_set()
            
    def close_search(self):
        """Close search interface."""
        if self.search_frame:
            self.search_frame.pack_forget()
            self.tag_remove('search_highlight', '1.0', tk.END)
            
    def on_search_changed(self, *args):
        """Handle search text changes."""
        search_text = self.search_var.get()
        self.tag_remove('search_highlight', '1.0', tk.END)
        
        if search_text:
            self.highlight_search_results(search_text)
            
    def highlight_search_results(self, search_text: str):
        """Highlight search results in text."""
        self.tag_configure('search_highlight', background='yellow', foreground='black')
        
        start_pos = '1.0'
        while True:
            pos = self.search(search_text, start_pos, stopindex=tk.END, nocase=True)
            if not pos:
                break
                
            end_pos = f"{pos}+{len(search_text)}c"
            self.tag_add('search_highlight', pos, end_pos)
            start_pos = end_pos
            
    def search_next(self):
        """Jump to next search result."""
        # Implementation for jumping to next search result
        pass
        
    def set_content(self, content: str, format_content: bool = True):
        """Set content with optional formatting."""
        self.original_content = content
        
        if format_content and self.enable_smart_wrapping:
            self.format_and_display_content()
        else:
            self.delete('1.0', tk.END)
            self.insert('1.0', content)
            
    def format_and_display_content(self):
        """Format content based on current settings and display."""
        if not self.original_content:
            return
            
        # Get current widget dimensions
        width = self.winfo_width()
        height = self.winfo_height()
        
        if width <= 1 or height <= 1:
            # Widget not fully initialized yet, try again later
            self.after(100, self.format_and_display_content)
            return
            
        # Calculate optimal columns
        if self.enable_multicolumn:
            self.current_columns = self.formatter.calculate_optimal_columns(width, self.current_font_size)
        else:
            self.current_columns = 1
            
        # Calculate character width based on font
        chars_per_line = max(40, int(width / (self.current_font_size * 0.6)))
        
        # Format content
        self.formatted_content = self.formatter.format_text_multicolumn(
            self.original_content, 
            chars_per_line, 
            self.current_columns
        )
        
        # Update display
        self.delete('1.0', tk.END)
        self.insert('1.0', self.formatted_content)
        
    def on_widget_resize(self, event=None):
        """Handle widget resize events."""
        if event and event.widget == self:
            # Debounce resize events
            if hasattr(self, '_resize_timer'):
                self.after_cancel(self._resize_timer)
                
            self._resize_timer = self.after(200, self.handle_resize_complete)
            
    def handle_resize_complete(self):
        """Handle completed resize operation."""
        if self.original_content and self.enable_smart_wrapping:
            self.format_and_display_content()
            
    def on_layout_changed(self):
        """Handle layout manager changes."""
        if self.enable_dynamic_font and self.layout_manager:
            layout_info = self.layout_manager.get_current_layout_info()
            category = layout_info.get('category', 'medium')
            
            # Adjust font size based on layout category
            new_font_size = self.font_manager.scale_font_for_category(self.current_font_size, category)
            if new_font_size != self.current_font_size:
                self.current_font_size = new_font_size
                new_font = self.font_manager.get_font("Arial", new_font_size)
                self.configure(font=new_font)
                
        # Reformat content for new layout
        if self.original_content:
            self.format_and_display_content()
            
    def add_expandable_section(self, title: str, content: str, initial_state: str = 'collapsed') -> ExpandableSection:
        """Add an expandable section to the content."""
        # This would typically be added to a parent container, not directly to the text widget
        parent_widget = self.master if self.master else self
        section = ExpandableSection(parent_widget, title, content, initial_state)  # type: ignore
        return section
        
    def get_content_stats(self) -> Dict:
        """Get statistics about current content."""
        content = self.get('1.0', tk.END)
        
        stats = {
            'character_count': len(content),
            'word_count': len(content.split()),
            'line_count': content.count('\n'),
            'current_columns': self.current_columns,
            'current_font_size': self.current_font_size,
            'widget_width': self.winfo_width(),
            'widget_height': self.winfo_height()
        }
        
        return stats
