"""
Phase 2: Multi-Column Layout System
Implements newspaper-style columns for wide screens with dynamic column adjustment
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Dict
from utils.logging_setup import LoggerMixin


class ColumnLayoutManager(LoggerMixin):
    """Manages multi-column text layouts for wide displays."""
    
    def __init__(self):
        super().__init__()
        self.column_configs = {
            'narrow': {'max_columns': 1, 'min_width': 300},
            'medium': {'max_columns': 2, 'min_width': 400},
            'wide': {'max_columns': 3, 'min_width': 450},
            'ultra_wide': {'max_columns': 4, 'min_width': 500}
        }
        
    def calculate_optimal_columns(self, available_width: int, content_length: int) -> int:
        """Calculate optimal number of columns based on available width and content."""
        
        # Determine screen category
        if available_width < 800:
            category = 'narrow'
        elif available_width < 1400:
            category = 'medium'
        elif available_width < 2000:
            category = 'wide'
        else:
            category = 'ultra_wide'
            
        config = self.column_configs[category]
        
        # Calculate maximum possible columns based on width
        max_columns_by_width = available_width // config['min_width']
        
        # Don't exceed category maximum
        max_columns = min(max_columns_by_width, config['max_columns'])
        
        # Adjust based on content length (avoid very short columns)
        if content_length < 500:  # Short content
            return min(max_columns, 1)
        elif content_length < 1500:  # Medium content
            return min(max_columns, 2)
        else:  # Long content
            return max_columns
            
    def split_text_into_columns(self, text: str, num_columns: int) -> List[str]:
        """Split text into approximately equal columns."""
        if num_columns <= 1:
            return [text]
            
        # Split by paragraphs first
        paragraphs = text.split('\n\n')
        if not paragraphs:
            return [text]
            
        # Calculate target length per column
        total_length = len(text)
        target_length = total_length // num_columns
        
        columns = []
        current_column = []
        current_length = 0
        
        for paragraph in paragraphs:
            paragraph_length = len(paragraph) + 2  # +2 for \n\n
            
            # If adding this paragraph would exceed target and we have content
            if (current_length + paragraph_length > target_length and 
                current_column and len(columns) < num_columns - 1):
                
                columns.append('\n\n'.join(current_column))
                current_column = [paragraph]
                current_length = paragraph_length
            else:
                current_column.append(paragraph)
                current_length += paragraph_length
                
        # Add the last column
        if current_column:
            columns.append('\n\n'.join(current_column))
            
        # Ensure we have exactly num_columns (pad with empty if needed)
        while len(columns) < num_columns:
            columns.append("")
            
        return columns


class MultiColumnTextWidget(tk.Frame, LoggerMixin):
    """A text widget that automatically arranges content in multiple columns."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent)
        LoggerMixin.__init__(self)
        
        self.column_manager = ColumnLayoutManager()
        self.text_widgets = []
        self.current_columns = 1
        self.content = ""
        self.text_options = kwargs
        
        # Configure the frame
        self.configure(bg=kwargs.get('bg', 'white'))
        
        # Create initial single column
        self.create_columns(1)
        
        # Bind resize events
        self.bind('<Configure>', self.on_resize)
        
    def create_columns(self, num_columns: int):
        """Create the specified number of text columns."""
        # Clear existing widgets
        for widget in self.text_widgets:
            widget.destroy()
        self.text_widgets.clear()
        
        # Create new columns
        for i in range(num_columns):
            # Create frame for each column
            column_frame = tk.Frame(self)
            column_frame.grid(row=0, column=i, sticky='nsew', padx=2)
            
            # Create text widget
            text_widget = tk.Text(column_frame, **self.text_options)
            scrollbar = tk.Scrollbar(column_frame, orient='vertical', command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            # Pack text widget and scrollbar
            text_widget.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')
            
            self.text_widgets.append(text_widget)
            
            # Configure grid weights
            self.grid_columnconfigure(i, weight=1)
            
        self.grid_rowconfigure(0, weight=1)
        self.current_columns = num_columns
        
    def set_content(self, content: str):
        """Set content and distribute across columns."""
        self.content = content
        self.redistribute_content()
        
    def redistribute_content(self):
        """Redistribute content across current columns."""
        if not self.content or not self.text_widgets:
            return
            
        # Split content into columns
        column_texts = self.column_manager.split_text_into_columns(
            self.content, self.current_columns
        )
        
        # Populate text widgets
        for i, text_widget in enumerate(self.text_widgets):
            text_widget.delete('1.0', tk.END)
            if i < len(column_texts):
                text_widget.insert('1.0', column_texts[i])
                
    def on_resize(self, event):
        """Handle resize events to adjust column layout."""
        if event.widget != self:
            return
            
        # Calculate optimal columns for new width
        available_width = self.winfo_width()
        if available_width < 10:  # Not yet properly sized
            return
            
        optimal_columns = self.column_manager.calculate_optimal_columns(
            available_width, len(self.content)
        )
        
        # Recreate columns if needed
        if optimal_columns != self.current_columns:
            self.create_columns(optimal_columns)
            self.redistribute_content()
            
    def insert(self, index, content):
        """Insert content (compatible with Text widget interface)."""
        if index == '1.0' or index == tk.END:
            self.content += content
        else:
            # For simplicity, append to content
            self.content += content
        self.redistribute_content()
        
    def delete(self, start, end=None):
        """Delete content (compatible with Text widget interface)."""
        if start == '1.0' and (end == tk.END or end is None):
            self.content = ""
            self.redistribute_content()
            
    def get(self, start, end=None):
        """Get content (compatible with Text widget interface)."""
        return self.content


class TabbledResultsDisplay(tk.Frame, LoggerMixin):
    """Enhanced results display with tabbed interface and collapsible sections."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent)
        LoggerMixin.__init__(self)
        
        self.tab_contents = {}
        self.collapsible_sections = {}
        self.export_callbacks = {}
        
        # Create the main interface
        self.create_interface()
        
    def create_interface(self):
        """Create the tabbed interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Define default tabs
        self.default_tabs = [
            ('Summary', 'summary'),
            ('Details', 'details'), 
            ('Connections', 'connections'),
            ('Mind Map', 'mindmap'),
            ('Raw Data', 'raw_data')
        ]
        
        # Create default tabs
        for tab_name, tab_id in self.default_tabs:
            self.add_tab(tab_name, tab_id)
            
    def add_tab(self, tab_name: str, tab_id: str):
        """Add a new tab to the results display."""
        # Create tab frame
        tab_frame = tk.Frame(self.notebook)
        self.notebook.add(tab_frame, text=tab_name)
        
        # Create toolbar for this tab
        toolbar = tk.Frame(tab_frame, height=30)
        toolbar.pack(fill='x', padx=5, pady=(5, 0))
        
        # Add export button
        export_btn = tk.Button(
            toolbar, 
            text=f"Export {tab_name}",
            command=lambda: self.export_tab_content(tab_id)
        )
        export_btn.pack(side='right', padx=5)
        
        # Add search functionality
        search_frame = tk.Frame(toolbar)
        search_frame.pack(side='left', fill='x', expand=True)
        
        tk.Label(search_frame, text="Search:").pack(side='left', padx=(0, 5))
        search_entry = tk.Entry(search_frame)
        search_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))
        search_entry.bind('<KeyRelease>', lambda e: self.search_in_tab(tab_id, search_entry.get()))
        
        # Create main content area with multi-column support
        content_frame = tk.Frame(tab_frame)
        content_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Create multi-column text widget
        text_widget = MultiColumnTextWidget(
            content_frame,
            wrap='word',
            font=('Arial', 10),
            bg='white',
            relief='sunken',
            bd=1
        )
        text_widget.pack(fill='both', expand=True)
        
        # Store references
        self.tab_contents[tab_id] = {
            'frame': tab_frame,
            'text_widget': text_widget,
            'search_entry': search_entry,
            'toolbar': toolbar
        }
        
        self.logger.info(f"Added tab: {tab_name} ({tab_id})")
        
    def set_tab_content(self, tab_id: str, content: str, content_type: str = 'text'):
        """Set content for a specific tab."""
        if tab_id not in self.tab_contents:
            self.logger.warning(f"Tab {tab_id} not found")
            return
            
        tab_info = self.tab_contents[tab_id]
        text_widget = tab_info['text_widget']
        
        # Clear existing content
        text_widget.delete('1.0', tk.END)
        
        # Add new content based on type
        if content_type == 'sections':
            self.add_collapsible_content(tab_id, content)
        else:
            text_widget.set_content(content)
            
        self.logger.info(f"Set content for tab {tab_id}: {len(content)} characters")
        
    def add_collapsible_content(self, tab_id: str, sections_data):
        """Add content with collapsible sections."""
        if tab_id not in self.tab_contents:
            return
            
        tab_info = self.tab_contents[tab_id]
        text_widget = tab_info['text_widget']
        
        # For this implementation, we'll create a summary view with expand options
        summary_content = []
        
        if isinstance(sections_data, dict):
            for section_name, section_content in sections_data.items():
                # Create summary for each section
                preview = section_content[:200] + "..." if len(section_content) > 200 else section_content
                summary_content.append(f"=== {section_name} ===\n{preview}\n[Click to expand full section]\n")
                
        elif isinstance(sections_data, str):
            # Split by common section delimiters
            sections = sections_data.split('\n\n')
            for i, section in enumerate(sections):
                if len(section.strip()) > 0:
                    preview = section[:150] + "..." if len(section) > 150 else section
                    summary_content.append(f"Section {i+1}:\n{preview}\n")
                    
        text_widget.set_content('\n'.join(summary_content))
        
    def search_in_tab(self, tab_id: str, search_term: str):
        """Search for text within a specific tab."""
        if not search_term or tab_id not in self.tab_contents:
            return
            
        tab_info = self.tab_contents[tab_id]
        text_widget = tab_info['text_widget']
        
        # Simple highlight implementation
        # In a full implementation, this would highlight matches
        content = text_widget.get('1.0', tk.END)
        if search_term.lower() in content.lower():
            self.logger.info(f"Found '{search_term}' in tab {tab_id}")
        
    def export_tab_content(self, tab_id: str):
        """Export content from a specific tab."""
        if tab_id not in self.tab_contents:
            return
            
        # Get callback if registered
        if tab_id in self.export_callbacks:
            self.export_callbacks[tab_id]()
        else:
            # Default export behavior
            self.default_export(tab_id)
            
    def default_export(self, tab_id: str):
        """Default export implementation."""
        tab_info = self.tab_contents[tab_id]
        content = tab_info['text_widget'].get('1.0', tk.END)
        
        # Simple export to clipboard (in real implementation, would use file dialog)
        try:
            self.clipboard_clear()
            self.clipboard_append(content)
            self.logger.info(f"Exported tab {tab_id} to clipboard")
        except Exception as e:
            self.logger.error(f"Export failed: {e}")
            
    def register_export_callback(self, tab_id: str, callback):
        """Register custom export callback for a tab."""
        self.export_callbacks[tab_id] = callback
        
    def select_tab(self, tab_id: str):
        """Programmatically select a tab."""
        for i, (_, stored_tab_id) in enumerate(self.default_tabs):
            if stored_tab_id == tab_id:
                self.notebook.select(i)
                break
                
    def get_current_tab(self) -> str:
        """Get the currently selected tab ID."""
        current_index = self.notebook.index(self.notebook.select())
        if current_index < len(self.default_tabs):
            return self.default_tabs[current_index][1]
        return ""


class SmartNavigationSystem(tk.Frame, LoggerMixin):
    """Smart navigation with minimap, breadcrumbs, and quick jump functionality."""
    
    def __init__(self, parent, target_widget=None):
        super().__init__(parent)
        LoggerMixin.__init__(self)
        
        self.target_widget = target_widget
        self.breadcrumbs = []
        self.content_sections = []
        self.quick_jump_shortcuts = {}
        
        self.create_navigation_interface()
        
    def create_navigation_interface(self):
        """Create the navigation interface."""
        # Breadcrumb navigation
        self.breadcrumb_frame = tk.Frame(self, height=25)
        self.breadcrumb_frame.pack(fill='x', padx=5, pady=2)
        
        # Quick jump controls
        jump_frame = tk.Frame(self)
        jump_frame.pack(fill='x', padx=5, pady=2)
        
        tk.Label(jump_frame, text="Quick Jump:").pack(side='left')
        
        self.jump_combo = ttk.Combobox(jump_frame, state='readonly')
        self.jump_combo.pack(side='left', fill='x', expand=True, padx=5)
        self.jump_combo.bind('<<ComboboxSelected>>', self.on_quick_jump)
        
        # Minimap (simplified version)
        minimap_frame = tk.LabelFrame(self, text="Content Map", height=100)
        minimap_frame.pack(fill='x', padx=5, pady=5)
        minimap_frame.pack_propagate(False)
        
        self.minimap_canvas = tk.Canvas(minimap_frame, height=80)
        self.minimap_canvas.pack(fill='both', expand=True, padx=5, pady=5)
        self.minimap_canvas.bind('<Button-1>', self.on_minimap_click)
        
    def update_breadcrumbs(self, path: List[str]):
        """Update breadcrumb navigation."""
        # Clear existing breadcrumbs
        for widget in self.breadcrumb_frame.winfo_children():
            widget.destroy()
            
        self.breadcrumbs = path.copy()
        
        # Create breadcrumb buttons
        for i, crumb in enumerate(path):
            if i > 0:
                tk.Label(self.breadcrumb_frame, text=" > ").pack(side='left')
                
            btn = tk.Button(
                self.breadcrumb_frame,
                text=crumb,
                relief='flat',
                command=lambda idx=i: self.navigate_to_breadcrumb(idx)
            )
            btn.pack(side='left')
            
    def navigate_to_breadcrumb(self, index: int):
        """Navigate to a specific breadcrumb level."""
        if index < len(self.breadcrumbs):
            target_crumb = self.breadcrumbs[index]
            self.logger.info(f"Navigating to breadcrumb: {target_crumb}")
            # Implementation would scroll to the appropriate section
            
    def update_content_sections(self, sections: List[Dict]):
        """Update available content sections for quick jump."""
        self.content_sections = sections
        
        # Update combo box
        section_names = [section.get('name', f"Section {i}") for i, section in enumerate(sections)]
        self.jump_combo['values'] = section_names
        
        # Update minimap
        self.draw_minimap()
        
    def draw_minimap(self):
        """Draw a simple minimap of content sections."""
        self.minimap_canvas.delete('all')
        
        if not self.content_sections:
            return
            
        canvas_width = self.minimap_canvas.winfo_width()
        canvas_height = self.minimap_canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            return
            
        # Draw sections as rectangles
        section_height = max(5, canvas_height // len(self.content_sections))
        
        for i, section in enumerate(self.content_sections):
            y1 = i * section_height
            y2 = min(y1 + section_height - 1, canvas_height)
            
            # Color based on section type or length
            color = self.get_section_color(section)
            
            self.minimap_canvas.create_rectangle(
                0, y1, canvas_width, y2,
                fill=color, outline='gray', tags=f"section_{i}"
            )
            
            # Add label if space allows
            if section_height > 15:
                section_name = section.get('name', f"Section {i}")[:15]
                self.minimap_canvas.create_text(
                    canvas_width // 2, (y1 + y2) // 2,
                    text=section_name, font=('Arial', 8)
                )
                
    def get_section_color(self, section: Dict) -> str:
        """Get color for section based on its properties."""
        section_type = section.get('type', 'default')
        colors = {
            'summary': '#e6f3ff',
            'details': '#fff2e6',
            'analysis': '#e6ffe6',
            'connections': '#ffe6f3',
            'raw_data': '#f0f0f0',
            'default': '#f5f5f5'
        }
        return colors.get(section_type, colors['default'])
        
    def on_minimap_click(self, event):
        """Handle clicks on the minimap."""
        canvas_height = self.minimap_canvas.winfo_height()
        click_y = event.y
        
        if self.content_sections and canvas_height > 0:
            section_index = int((click_y / canvas_height) * len(self.content_sections))
            section_index = max(0, min(section_index, len(self.content_sections) - 1))
            
            self.jump_to_section(section_index)
            
    def on_quick_jump(self, event):
        """Handle quick jump selection."""
        selection = self.jump_combo.current()
        if selection >= 0:
            self.jump_to_section(selection)
            
    def jump_to_section(self, section_index: int):
        """Jump to a specific section."""
        if 0 <= section_index < len(self.content_sections):
            section = self.content_sections[section_index]
            self.logger.info(f"Jumping to section: {section.get('name', section_index)}")
            
            # Update breadcrumbs
            section_name = section.get('name', f"Section {section_index}")
            self.update_breadcrumbs(['Content', section_name])
            
    def register_shortcut(self, key: str, section_index: int):
        """Register keyboard shortcut for quick jumping."""
        self.quick_jump_shortcuts[key] = section_index
        
    def handle_keypress(self, event):
        """Handle keyboard shortcuts."""
        key = event.keysym
        if key in self.quick_jump_shortcuts:
            section_index = self.quick_jump_shortcuts[key]
            self.jump_to_section(section_index)
            return 'break'  # Prevent default handling


# Integration class for Phase 2 features
class Phase2AdvancedFeatures(LoggerMixin):
    """Integration class for Phase 2 advanced features."""
    
    def __init__(self, parent_widget):
        super().__init__()
        self.parent = parent_widget
        
    def create_advanced_results_panel(self, parent) -> TabbledResultsDisplay:
        """Create enhanced results display with all Phase 2 features."""
        
        # Main container
        container = tk.Frame(parent)
        
        # Navigation system
        navigation = SmartNavigationSystem(container)
        navigation.pack(fill='x', pady=(0, 5))
        
        # Tabbed results display
        results_display = TabbledResultsDisplay(container)
        results_display.pack(fill='both', expand=True)
        
        # Connect navigation to results display
        self.connect_navigation_to_results(navigation, results_display)
        
        container.pack(fill='both', expand=True)
        
        self.logger.info("Created advanced results panel with Phase 2 features")
        return results_display
        
    def connect_navigation_to_results(self, navigation: SmartNavigationSystem, 
                                    results: TabbledResultsDisplay):
        """Connect navigation system to results display."""
        
        # Set up content sections for navigation
        default_sections = [
            {'name': 'Summary', 'type': 'summary'},
            {'name': 'Details', 'type': 'details'},
            {'name': 'Connections', 'type': 'connections'},
            {'name': 'Mind Map', 'type': 'analysis'},
            {'name': 'Raw Data', 'type': 'raw_data'}
        ]
        
        navigation.update_content_sections(default_sections)
        
        # Register keyboard shortcuts
        shortcuts = {'F1': 0, 'F2': 1, 'F3': 2, 'F4': 3, 'F5': 4}
        for key, index in shortcuts.items():
            navigation.register_shortcut(key, index)


# Demo function for Phase 2 features
def demo_phase2_features():
    """Demonstrate Phase 2 advanced features."""
    root = tk.Tk()
    root.title("Phase 2 Advanced Features Demo")
    root.geometry("1200x800")
    
    # Create Phase 2 features
    phase2 = Phase2AdvancedFeatures(root)
    results_display = phase2.create_advanced_results_panel(root)
    
    # Add sample content to tabs
    sample_content = {
        'summary': """This is a comprehensive analysis summary that demonstrates the multi-column layout capabilities. 

The system automatically adjusts the number of columns based on available screen width and content length. For wide displays, content is distributed across multiple columns for better readability.

Key findings include:
• Improved information density
• Better space utilization  
• Enhanced readability on wide screens
• Responsive column adjustment

The layout dynamically adapts as you resize the window, ensuring optimal presentation regardless of screen size.""",

        'details': """Detailed Analysis Results

This section contains extensive analysis details that showcase the advanced text rendering capabilities of Phase 2 improvements.

Section 1: Technical Implementation
The multi-column layout system uses intelligent text distribution algorithms to ensure balanced columns while maintaining paragraph integrity. Content is split at natural boundaries to preserve readability.

Section 2: Performance Considerations  
The system implements lazy loading and efficient text manipulation to maintain smooth performance even with large documents. Virtual scrolling ensures memory efficiency for extensive content.

Section 3: User Experience Enhancements
- Responsive column adjustment based on screen width
- Smooth transitions between layout changes
- Preserved reading position during layout updates
- Intelligent text wrapping and formatting

Section 4: Advanced Features
The enhanced results display includes tabbed interfaces, collapsible sections, and integrated search functionality. Export capabilities allow content to be saved in various formats.

This comprehensive system ensures that all analysis results are presented in the most readable and accessible format possible.""",

        'connections': """Content Connections and Relationships

This tab demonstrates how related information is grouped and presented for easy navigation and understanding.

Primary Connections:
→ Analysis Results ↔ Summary Data
→ Technical Details ↔ Implementation Notes  
→ User Interface ↔ Experience Metrics
→ Performance Data ↔ Optimization Strategies

Secondary Relationships:
• Layout Management → Responsive Design
• Content Organization → Information Architecture
• Navigation Systems → User Accessibility
• Export Functions → Data Portability

The intelligent connection system helps users understand how different aspects of the analysis relate to each other, enabling better insights and decision-making."""
    }
    
    # Set content for tabs
    for tab_id, content in sample_content.items():
        results_display.set_tab_content(tab_id, content)
    
    # Add instructions
    instructions = tk.Label(
        root,
        text="Demo Instructions: Try resizing the window to see responsive columns • Use F1-F5 for quick navigation • Search within tabs",
        bg='lightgray'
    )
    instructions.pack(fill='x', side='bottom')
    
    print("Phase 2 Advanced Features Demo")
    print("Features demonstrated:")
    print("• Multi-column text layout with responsive adjustment")
    print("• Tabbed results display with search functionality")
    print("• Smart navigation with minimap and breadcrumbs")
    print("• Export capabilities for each tab")
    print("• Keyboard shortcuts for quick navigation")
    
    root.mainloop()


if __name__ == "__main__":
    demo_phase2_features()
