"""
Responsive Layout Manager for AI Analysis Program
Handles dynamic layout adjustments based on screen size and content
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Callable
from utils.logging_setup import LoggerMixin


class ResponsiveLayoutManager(LoggerMixin):
    """Manages responsive layout behavior for the application"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.screen_breakpoints = {
            'small': 1400,
            'medium': 1800,
            'large': 2200
        }
        self.current_layout = None
        self.layout_callbacks = {}
        self.panel_weights = {}
        
        # Bind to window resize events
        self.root.bind('<Configure>', self.on_window_resize)
        
    def get_screen_category(self) -> str:
        """Determine screen size category based on current window width"""
        try:
            width = self.root.winfo_width()
            if width < self.screen_breakpoints['small']:
                return 'small'
            elif width < self.screen_breakpoints['medium']:
                return 'medium'
            elif width < self.screen_breakpoints['large']:
                return 'large'
            return 'ultra_wide'
        except tk.TclError:
            # Fallback if window not yet initialized
            return 'medium'
    
    def register_layout_callback(self, name: str, callback: Callable):
        """Register a callback for layout changes"""
        self.layout_callbacks[name] = callback
    
    def get_panel_weights(self, category: str) -> Dict[str, int]:
        """Get panel weight distribution for screen category"""
        weight_configs = {
            'small': {
                'topic_input': 1,
                'analysis_panel': 1,
                'results_viewer': 2
            },
            'medium': {
                'topic_input': 1,
                'analysis_panel': 1,
                'results_viewer': 3
            },
            'large': {
                'topic_input': 1,
                'analysis_panel': 2,
                'results_viewer': 4
            },
            'ultra_wide': {
                'topic_input': 1,
                'analysis_panel': 2,
                'results_viewer': 5
            }
        }
        return weight_configs.get(category, weight_configs['medium'])
    
    def get_minimum_widths(self, category: str) -> Dict[str, int]:
        """Get minimum widths for panels based on screen category"""
        min_widths = {
            'small': {
                'topic_input': 300,
                'analysis_panel': 350,
                'results_viewer': 400
            },
            'medium': {
                'topic_input': 350,
                'analysis_panel': 400,
                'results_viewer': 500
            },
            'large': {
                'topic_input': 400,
                'analysis_panel': 450,
                'results_viewer': 600
            },
            'ultra_wide': {
                'topic_input': 450,
                'analysis_panel': 500,
                'results_viewer': 700
            }
        }
        return min_widths.get(category, min_widths['medium'])
    
    def apply_layout(self, force_update: bool = False):
        """Apply appropriate layout based on current screen size"""
        new_category = self.get_screen_category()
        
        if not force_update and self.current_layout == new_category:
            return
            
        self.current_layout = new_category
        self.logger.debug(f"Applying {new_category} layout")
        
        # Get configuration for this layout
        weights = self.get_panel_weights(new_category)
        min_widths = self.get_minimum_widths(new_category)
        
        # Notify all registered callbacks
        for name, callback in self.layout_callbacks.items():
            try:
                callback(new_category, weights, min_widths)
            except Exception as e:
                self.logger.error(f"Error in layout callback {name}: {e}")
    
    def on_window_resize(self, event=None):
        """Handle window resize events"""
        if event and event.widget == self.root:
            # Debounce resize events
            self.root.after_idle(self.apply_layout)
    
    def get_font_size(self, category: str, base_size: int = 10) -> int:
        """Get appropriate font size for screen category"""
        font_scales = {
            'small': 0.9,
            'medium': 1.0,
            'large': 1.1,
            'ultra_wide': 1.2
        }
        scale = font_scales.get(category, 1.0)
        return max(8, int(base_size * scale))
    
    def get_spacing(self, category: str, spacing_type: str = 'normal') -> int:
        """Get appropriate spacing for screen category"""
        spacing_configs = {
            'small': {'small': 2, 'normal': 4, 'large': 6},
            'medium': {'small': 3, 'normal': 6, 'large': 10},
            'large': {'small': 4, 'normal': 8, 'large': 12},
            'ultra_wide': {'small': 5, 'normal': 10, 'large': 15}
        }
        config = spacing_configs.get(category, spacing_configs['medium'])
        return config.get(spacing_type, config['normal'])


class AdaptivePanedWindow(ttk.PanedWindow):
    """Enhanced PanedWindow with responsive behavior"""
    
    def __init__(self, parent, layout_manager: ResponsiveLayoutManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.layout_manager = layout_manager
        self.panel_info = {}
        
        # Register for layout updates
        self.layout_manager.register_layout_callback(
            f"paned_{id(self)}", self.update_layout
        )
    
    def add_panel(self, widget, name: str, **kwargs):
        """Add a panel with responsive configuration"""
        self.add(widget, **kwargs)
        self.panel_info[name] = {
            'widget': widget,
            'index': len(self.panel_info)
        }
    
    def update_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update panel layout based on responsive configuration"""
        try:
            # Update weights for each panel
            for name, info in self.panel_info.items():
                if name in weights:
                    weight = weights[name]
                    min_width = min_widths.get(name, 300)
                    
                    # Configure the panel
                    self.paneconfig(info['widget'], weight=weight, minsize=min_width)
                    
        except Exception:
            # Silently handle configuration errors during initialization
            pass


class ContentAwareFrame(ttk.Frame):
    """Frame that adapts its layout based on content size"""
    
    def __init__(self, parent, layout_manager: ResponsiveLayoutManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.layout_manager = layout_manager
        self.content_widgets = []
        self.min_height = 100
        self.max_height = None
        
        # Register for layout updates
        self.layout_manager.register_layout_callback(
            f"content_{id(self)}", self.update_content_layout
        )
    
    def add_content_widget(self, widget, expand_priority: int = 1):
        """Add a widget that should be considered for content-aware sizing"""
        self.content_widgets.append({
            'widget': widget,
            'priority': expand_priority
        })
    
    def update_content_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update content layout based on available space"""
        spacing = self.layout_manager.get_spacing(category)
        
        # Update padding for all child widgets
        for child in self.winfo_children():
            if hasattr(child, 'configure'):
                try:
                    if isinstance(child, (ttk.Frame, ttk.LabelFrame)):
                        child.configure(padding=spacing)
                except tk.TclError:
                    pass
