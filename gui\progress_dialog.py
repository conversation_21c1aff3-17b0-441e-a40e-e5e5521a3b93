"""
Progress dialog for long-running analysis operations
Provides real-time progress updates and cancellation capability
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from typing import Callable, Optional, Any
from datetime import datetime, timedelta

from utils.logging_setup import LoggerMixin

class ProgressDialog(LoggerMixin):
    """Progress dialog with cancellation support"""
    
    def __init__(self, parent: tk.Widget, title: str = "Analysis Progress"):
        self.parent = parent
        self.title = title
        self.cancelled = False
        self.completed = False
        self.result = None
        self.error = None
        
        # Progress tracking
        self.start_time = None
        self.current_step = 0
        self.total_steps = 0
        self.step_descriptions = []
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Setup UI
        self.setup_ui()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def center_dialog(self):
        """Center the dialog on screen"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"500x300+{x}+{y}")
    
    def setup_ui(self):
        """Set up the progress dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title label
        self.title_label = ttk.Label(
            main_frame,
            text="Preparing analysis...",
            font=('Arial', 12, 'bold')
        )
        self.title_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(pady=(0, 10))
        
        # Progress percentage
        self.percentage_label = ttk.Label(main_frame, text="0%")
        self.percentage_label.pack()
        
        # Current step description
        self.step_label = ttk.Label(
            main_frame,
            text="Initializing...",
            wraplength=450,
            justify=tk.CENTER
        )
        self.step_label.pack(pady=(10, 0))
        
        # Time information frame
        time_frame = ttk.Frame(main_frame)
        time_frame.pack(pady=(20, 0), fill=tk.X)
        
        # Elapsed time
        ttk.Label(time_frame, text="Elapsed:").pack(side=tk.LEFT)
        self.elapsed_label = ttk.Label(time_frame, text="00:00")
        self.elapsed_label.pack(side=tk.LEFT, padx=(5, 20))
        
        # Estimated remaining time
        ttk.Label(time_frame, text="Remaining:").pack(side=tk.LEFT)
        self.remaining_label = ttk.Label(time_frame, text="--:--")
        self.remaining_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # Details text area
        details_frame = ttk.LabelFrame(main_frame, text="Details", padding=5)
        details_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # Scrollable text widget
        text_frame = ttk.Frame(details_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.details_text = tk.Text(
            text_frame,
            height=6,
            wrap=tk.WORD,
            yscrollcommand=scrollbar.set,
            font=('Courier', 9),
            state=tk.DISABLED
        )
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.details_text.yview)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Cancel button
        self.cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel_operation
        )
        self.cancel_button.pack(side=tk.RIGHT)
        
        # Hide in background button
        self.hide_button = ttk.Button(
            button_frame,
            text="Hide",
            command=self.hide_dialog
        )
        self.hide_button.pack(side=tk.RIGHT, padx=(0, 5))
        
        # Start time updates
        self.start_time = datetime.now()
        self.update_time_display()
    
    def set_steps(self, step_descriptions: list):
        """Set the steps for the progress dialog"""
        self.step_descriptions = step_descriptions
        self.total_steps = len(step_descriptions)
        self.current_step = 0
        
        if self.total_steps > 0:
            self.step_label.config(text=self.step_descriptions[0])
    
    def update_progress(self, step: int, description: str = "", details: str = ""):
        """Update progress to specific step"""
        if self.cancelled:
            return
        
        self.current_step = step
        
        # Update progress bar
        if self.total_steps > 0:
            progress = (step / self.total_steps) * 100
            self.progress_var.set(progress)
            self.percentage_label.config(text=f"{progress:.1f}%")
        
        # Update step description
        if description:
            self.step_label.config(text=description)
        elif step < len(self.step_descriptions):
            self.step_label.config(text=self.step_descriptions[step])
        
        # Add details if provided
        if details:
            self.add_detail(details)
        
        # Update display
        self.dialog.update_idletasks()
    
    def add_detail(self, detail: str):
        """Add a detail message to the details area"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        message = f"[{timestamp}] {detail}\n"
        
        self.details_text.config(state=tk.NORMAL)
        self.details_text.insert(tk.END, message)
        self.details_text.see(tk.END)
        self.details_text.config(state=tk.DISABLED)
    
    def update_time_display(self):
        """Update elapsed and remaining time display"""
        if self.cancelled or self.completed:
            return
        
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            elapsed_str = str(elapsed).split('.')[0]  # Remove microseconds
            self.elapsed_label.config(text=elapsed_str)
            
            # Estimate remaining time
            if self.current_step > 0 and self.total_steps > 0:
                avg_time_per_step = elapsed.total_seconds() / self.current_step
                remaining_steps = self.total_steps - self.current_step
                remaining_seconds = avg_time_per_step * remaining_steps
                
                if remaining_seconds > 0:
                    remaining_time = timedelta(seconds=int(remaining_seconds))
                    remaining_str = str(remaining_time).split('.')[0]
                    self.remaining_label.config(text=remaining_str)
        
        # Schedule next update
        self.dialog.after(1000, self.update_time_display)
    
    def cancel_operation(self):
        """Cancel the current operation"""
        if not self.completed:
            self.cancelled = True
            self.title_label.config(text="Cancelling...")
            self.step_label.config(text="Please wait while the operation is cancelled...")
            self.cancel_button.config(state=tk.DISABLED)
            self.add_detail("Cancellation requested by user")
    
    def hide_dialog(self):
        """Hide the dialog (continue in background)"""
        self.dialog.withdraw()
    
    def show_dialog(self):
        """Show the dialog again"""
        self.dialog.deiconify()
        self.dialog.lift()
    
    def complete_operation(self, result: Any = None, error: str = None):
        """Mark operation as complete"""
        self.completed = True
        self.result = result
        self.error = error
        
        if error:
            self.title_label.config(text="Analysis Failed")
            self.step_label.config(text=f"Error: {error}")
            self.progress_var.set(0)
            self.percentage_label.config(text="Error")
            self.add_detail(f"Analysis failed: {error}")
        else:
            self.title_label.config(text="Analysis Complete")
            self.step_label.config(text="Analysis completed successfully!")
            self.progress_var.set(100)
            self.percentage_label.config(text="100%")
            self.add_detail("Analysis completed successfully")
        
        # Update buttons
        self.cancel_button.config(text="Close", state=tk.NORMAL, command=self.close_dialog)
        self.hide_button.config(state=tk.DISABLED)
    
    def close_dialog(self):
        """Close the dialog"""
        self.dialog.destroy()
    
    def on_close(self):
        """Handle window close event"""
        if not self.completed:
            # Ask for confirmation if operation is still running
            import tkinter.messagebox as messagebox
            if messagebox.askyesno("Cancel Operation", "Analysis is still running. Cancel it?"):
                self.cancel_operation()
                self.dialog.after(1000, self.close_dialog)  # Close after a delay
        else:
            self.close_dialog()

class AnalysisProgressManager(LoggerMixin):
    """Manages progress for analysis operations"""
    
    def __init__(self, parent: tk.Widget):
        self.parent = parent
        self.current_dialog = None
        self.operation_thread = None
        self.cancelled = False
    
    def start_analysis(self, operation_func: Callable, steps: list, title: str = "Analysis Progress") -> Any:
        """Start an analysis operation with progress tracking"""
        # Create progress dialog
        self.current_dialog = ProgressDialog(self.parent, title)
        self.current_dialog.set_steps(steps)
        self.cancelled = False
        
        # Progress callback
        def progress_callback(step: int, description: str = "", details: str = ""):
            if self.current_dialog and not self.cancelled:
                self.current_dialog.update_progress(step, description, details)
        
        # Cancellation check
        def is_cancelled() -> bool:
            return self.current_dialog.cancelled if self.current_dialog else False
        
        # Run operation in thread
        def run_operation():
            try:
                result = operation_func(progress_callback, is_cancelled)
                
                if self.current_dialog:
                    if is_cancelled():
                        self.current_dialog.complete_operation(error="Operation cancelled by user")
                    else:
                        self.current_dialog.complete_operation(result)
                
                return result
                
            except Exception as e:
                self.logger.error(f"Analysis operation failed: {e}")
                if self.current_dialog:
                    self.current_dialog.complete_operation(error=str(e))
                return None
        
        # Start operation thread
        self.operation_thread = threading.Thread(target=run_operation, daemon=True)
        self.operation_thread.start()
        
        # Return dialog for external control
        return self.current_dialog
    
    def is_running(self) -> bool:
        """Check if an analysis is currently running"""
        return (self.operation_thread and 
                self.operation_thread.is_alive() and 
                self.current_dialog and 
                not self.current_dialog.completed)
    
    def cancel_current(self):
        """Cancel current operation"""
        if self.current_dialog:
            self.current_dialog.cancel_operation()
    
    def cleanup(self):
        """Cleanup resources"""
        if self.current_dialog:
            self.current_dialog.close_dialog()
        
        if self.operation_thread and self.operation_thread.is_alive():
            self.cancelled = True
