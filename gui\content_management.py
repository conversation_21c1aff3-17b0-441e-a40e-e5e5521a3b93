"""
Advanced Content Management System for AI Analysis Program
Handles dynamic content fitting, overflow management, priority-based display, and smart content optimization
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional
from enum import Enum
from utils.logging_setup import LoggerMixin


class ContentPriority(Enum):
    """Content priority levels for display management."""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    OPTIONAL = 5


class ContentType(Enum):
    """Types of content for specialized handling."""
    TEXT = "text"
    ANALYSIS_RESULT = "analysis_result"
    SUMMARY = "summary"
    DETAILS = "details"
    CONNECTIONS = "connections"
    MIND_MAP = "mind_map"
    RAW_DATA = "raw_data"
    ERROR = "error"
    STATUS = "status"


class ContentItem:
    """Represents a single content item with metadata."""
    
    def __init__(self, content_id: str, content: Any, content_type: ContentType, 
                 priority: ContentPriority = ContentPriority.NORMAL, 
                 metadata: Optional[Dict] = None):
        self.content_id = content_id
        self.content = content
        self.content_type = content_type
        self.priority = priority
        self.metadata = metadata or {}
        self.display_widget = None
        self.is_visible = False
        self.display_order = 0
        
    def get_display_size_estimate(self) -> int:
        """Estimate display size in pixels."""
        if isinstance(self.content, str):
            lines = self.content.count('\n') + 1
            return lines * 20  # Approximate line height
        return 100  # Default estimate
        
    def get_content_summary(self, max_length: int = 100) -> str:
        """Get truncated summary of content."""
        if isinstance(self.content, str):
            if len(self.content) <= max_length:
                return self.content
            return self.content[:max_length] + "..."
        return str(self.content)[:max_length]


class OverflowHandler:
    """Enhanced overflow handling with multiple strategies."""
    
    def __init__(self):
        self.overflow_strategies = {
            ContentType.TEXT: self.handle_text_overflow,
            ContentType.ANALYSIS_RESULT: self.handle_analysis_overflow,
            ContentType.SUMMARY: self.handle_summary_overflow,
            ContentType.DETAILS: self.handle_details_overflow
        }
        
    def handle_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle overflow for specific content type."""
        content_type = content_item.content_type
        handler = self.overflow_strategies.get(content_type, self.handle_default_overflow)
        return handler(content_item, available_space)
        
    def handle_text_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle text content overflow with smart truncation."""
        content = content_item.content
        if not isinstance(content, str):
            return {"action": "none"}
            
        estimated_lines = available_space // 20
        content_lines = content.split('\n')
        
        if len(content_lines) <= estimated_lines:
            return {"action": "none"}
            
        visible_lines = max(3, estimated_lines - 2)
        truncated_content = '\n'.join(content_lines[:visible_lines])
        
        return {
            "action": "truncate",
            "visible_content": truncated_content,
            "hidden_content": '\n'.join(content_lines[visible_lines:]),
            "show_expand": True
        }
        
    def handle_analysis_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle analysis result overflow by creating tabbed interface."""
        return {
            "action": "create_tab",
            "tab_title": "Analysis Details",
            "summary": content_item.get_content_summary(200)
        }
        
    def handle_summary_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle summary overflow with expandable section."""
        return {
            "action": "expand_section",
            "section_title": "Summary",
            "max_height": available_space * 0.7
        }
        
    def handle_details_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Handle details overflow with scrollable area."""
        return {
            "action": "create_scrollable",
            "max_height": available_space * 0.8,
            "enable_search": True
        }
        
    def handle_default_overflow(self, content_item: ContentItem, available_space: int) -> Dict[str, Any]:
        """Default overflow handling."""
        return {
            "action": "create_scrollable",
            "max_height": available_space * 0.6
        }


class ContentManager(LoggerMixin):
    """Advanced content management system with priority-based display and overflow handling."""
    
    def __init__(self):
        super().__init__()
        self.content_items = {}  # content_id -> ContentItem
        self.display_panels = {}  # panel_id -> widget
        self.overflow_handler = OverflowHandler()
        self.layout_optimizer = LayoutOptimizer()

        # Content organization
        self.content_by_priority = {priority: [] for priority in ContentPriority}
        self.content_by_type = {content_type: [] for content_type in ContentType}

        # Display state
        self.visible_content = set()
        self.layout_callbacks = []

        # Fix: initialize content_panels and overflow_handlers
        self.content_panels = {}
        self.overflow_handlers = {}
        
    def register_panel(self, panel_id: str, panel_widget, overflow_handler=None):
        """Register a panel for content management"""
        self.content_panels[panel_id] = {
            'widget': panel_widget,
            'content': [],
            'visible_content': [],
            'overflow_content': []
        }
        
        if overflow_handler:
            self.overflow_handlers[panel_id] = overflow_handler
            
    def add_content(self, panel_id: str, content: Any, priority: str = 'normal', 
                   content_type: str = 'text', metadata=None):
        """Add content to a panel with smart placement"""
        if panel_id not in self.content_panels:
            self.logger.error(f"Panel {panel_id} not registered")
            return False
            
        content_item = {
            'content': content,
            'priority': priority,
            'type': content_type,
            'metadata': metadata or {},
            'id': f"{panel_id}_{len(self.content_panels[panel_id]['content'])}"
        }
        
        self.content_panels[panel_id]['content'].append(content_item)
        self._optimize_panel_content(panel_id)
        return True
    
    def _optimize_panel_content(self, panel_id: str):
        """Optimize content display for a panel"""
        panel_info = self.content_panels[panel_id]
        panel_widget = panel_info['widget']
        
        # Get available space
        available_space = self._get_available_space(panel_widget)
        
        # Sort content by priority
        sorted_content = sorted(
            panel_info['content'],
            key=lambda x: self._get_priority_value(x['priority']),
            reverse=True
        )
        
        # Fit content within available space
        visible_content = []
        overflow_content = []
        used_space = 0
        
        for item in sorted_content:
            item_space = self._estimate_content_space(item, panel_widget)
            
            if used_space + item_space <= available_space or not visible_content:
                # Always show at least one item
                visible_content.append(item)
                used_space += item_space
            else:
                overflow_content.append(item)
        
        panel_info['visible_content'] = visible_content
        panel_info['overflow_content'] = overflow_content
        
        # Update display
        self._update_panel_display(panel_id)
    
    def _get_priority_value(self, priority: str) -> int:
        """Convert priority string to numeric value"""
        priority_values = {
            'critical': 100,
            'high': 80,
            'normal': 50,
            'low': 20,
            'background': 10
        }
        return priority_values.get(priority, 50)
    
    def _get_available_space(self, widget) -> int:
        """Estimate available space in a widget"""
        try:
            return max(100, widget.winfo_height() - 50)  # Leave some margin
        except tk.TclError:
            return 500  # Default fallback
    
    def _estimate_content_space(self, content_item: Dict, widget) -> int:
        """Estimate space required for content item"""
        content_type = content_item['type']
        content = content_item['content']
        
        if content_type == 'text':
            # Estimate based on text length and line height
            if isinstance(content, str):
                lines = len(content.split('\n'))
                return max(20, lines * 20)  # Assume 20px per line
        elif content_type == 'widget':
            try:
                return content.winfo_reqheight()
            except (tk.TclError, AttributeError):
                return 50
        
        return 50  # Default estimate
    
    def _update_panel_display(self, panel_id: str):
        """Update the visual display of a panel"""
        panel_info = self.content_panels[panel_id]
        
        # Handle overflow
        if panel_info['overflow_content'] and panel_id in self.overflow_handlers:
            self.overflow_handlers[panel_id].handle_overflow(
                panel_info['visible_content'],
                panel_info['overflow_content']
            )
    
    def get_overflow_summary(self, panel_id: str) -> str:
        """Get a summary of overflow content"""
        if panel_id not in self.content_panels:
            return ""
            
        overflow_content = self.content_panels[panel_id]['overflow_content']
        if not overflow_content:
            return ""
            
        count = len(overflow_content)
        return f"{count} additional item{'s' if count != 1 else ''} available"


class LayoutOptimizer(LoggerMixin):
    """Optimizes layout for better space utilization"""
    
    def __init__(self):
        self.optimization_strategies = {
            'compact': self._apply_compact_layout,
            'comfortable': self._apply_comfortable_layout,
            'spacious': self._apply_spacious_layout
        }
    
    def optimize_layout(self, widget, strategy: str = 'compact', content_info=None):
        """Apply layout optimization strategy"""
        if strategy in self.optimization_strategies:
            self.optimization_strategies[strategy](widget, content_info or {})
    
    def _apply_compact_layout(self, widget, content_info: Dict):
        """Apply compact layout optimizations"""
        try:
            # Reduce padding and spacing
            if hasattr(widget, 'configure'):
                if isinstance(widget, (ttk.Frame, ttk.LabelFrame)):
                    widget.configure(padding=2)
                    
            # Optimize child widgets
            for child in widget.winfo_children():
                self._optimize_widget_spacing(child, 'compact')
                
        except tk.TclError:
            pass
    
    def _apply_comfortable_layout(self, widget, content_info: Dict):
        """Apply comfortable layout optimizations"""
        try:
            if hasattr(widget, 'configure'):
                if isinstance(widget, (ttk.Frame, ttk.LabelFrame)):
                    widget.configure(padding=5)
                    
            for child in widget.winfo_children():
                self._optimize_widget_spacing(child, 'comfortable')
                
        except tk.TclError:
            pass
    
    def _apply_spacious_layout(self, widget, content_info: Dict):
        """Apply spacious layout optimizations"""
        try:
            if hasattr(widget, 'configure'):
                if isinstance(widget, (ttk.Frame, ttk.LabelFrame)):
                    widget.configure(padding=10)
                    
            for child in widget.winfo_children():
                self._optimize_widget_spacing(child, 'spacious')
                
        except tk.TclError:
            pass
    
    def _optimize_widget_spacing(self, widget, strategy: str):
        """Optimize spacing for individual widgets"""
        spacing_configs = {
            'compact': {'padx': 2, 'pady': 1},
            'comfortable': {'padx': 5, 'pady': 3},
            'spacious': {'padx': 10, 'pady': 5}
        }
        
        try:
            config = spacing_configs.get(strategy, spacing_configs['comfortable'])
            
            # Apply to pack geometry manager
            pack_info = widget.pack_info()
            if pack_info:
                widget.pack_configure(**config)
                
        except tk.TclError:
            pass




class MultiColumnText(tk.Text):
    """Text widget with multi-column support for wide displays"""
    
    def __init__(self, parent, columns: int = 1, **kwargs):
        super().__init__(parent, **kwargs)
        self.columns = columns
        self.column_width = 0
        self.original_content = ""
        self.bind('<Configure>', self.on_resize)
        
    def set_content(self, content: str):
        """Set content with multi-column formatting"""
        self.original_content = content
        self.reformat_content()
    
    def set_columns(self, columns: int):
        """Change number of columns"""
        self.columns = max(1, columns)
        self.reformat_content()
    
    def on_resize(self, event=None):
        """Handle widget resize"""
        if event and event.widget == self:
            self.after_idle(self.reformat_content)
    
    def reformat_content(self):
        """Reformat content for multi-column display"""
        if not self.original_content or self.columns <= 1:
            self.delete('1.0', tk.END)
            self.insert('1.0', self.original_content)
            return
        
        try:
            # Calculate column width based on widget width
            widget_width = self.winfo_width()
            if widget_width <= 1:
                return  # Widget not yet configured
                
            font = self.cget('font')
            char_width = self.tk.call('font', 'measure', font, '0')
            
            if char_width > 0:
                self.column_width = max(20, (widget_width // self.columns) // char_width - 2)
                self._format_columns()
                
        except (tk.TclError, ZeroDivisionError):
            # Fallback to single column
            self.delete('1.0', tk.END)
            self.insert('1.0', self.original_content)
    
    def _format_columns(self):
        """Format content into columns"""
        if not self.original_content:
            return
            
        # Split content into paragraphs
        paragraphs = self.original_content.split('\n\n')
        
        # Distribute paragraphs across columns
        columns_content = [[] for _ in range(self.columns)]
        current_column = 0
        
        for paragraph in paragraphs:
            columns_content[current_column].append(paragraph)
            current_column = (current_column + 1) % self.columns
        
        # Format the columns
        formatted_content = []
        max_lines = max(len(col) for col in columns_content) if columns_content else 0
        
        for line_idx in range(max_lines):
            line_parts = []
            for col_idx in range(self.columns):
                if line_idx < len(columns_content[col_idx]):
                    # Wrap text to column width
                    paragraph = columns_content[col_idx][line_idx]
                    wrapped = self._wrap_text(paragraph, self.column_width)
                    line_parts.append(wrapped.ljust(self.column_width))
                else:
                    line_parts.append(' ' * self.column_width)
            
            formatted_content.append(' | '.join(line_parts))
        
        # Update widget content
        self.delete('1.0', tk.END)
        self.insert('1.0', '\n'.join(formatted_content))
    
    def _wrap_text(self, text: str, width: int) -> str:
        """Wrap text to specified width"""
        words = text.split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            if current_length + len(word) + 1 <= width:
                current_line.append(word)
                current_length += len(word) + 1
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]
                current_length = len(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return '\n'.join(lines)
