"""
Phase 1 Core Layout Improvements Integration
Integrates all Phase 1 components into the main application with simplified working methods
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Union
from utils.logging_setup import LoggerMixin

# Import Phase 1 components
from gui.advanced_responsive_layout import ResponsiveLayoutManager
from gui.enhanced_text_widget import EnhancedTextWidget
from gui.advanced_content_management import AdvancedContentManager


class Phase1IntegrationManager(LoggerMixin):
    """Manages integration of all Phase 1 improvements."""
    
    def __init__(self, main_window: tk.Tk):
        super().__init__()
        self.main_window = main_window
        
        # Initialize Phase 1 components
        self.responsive_layout = ResponsiveLayoutManager(main_window)
        self.content_manager = AdvancedContentManager()
        
        # Integration state
        self.integrated_panels = {}
        self.enhanced_widgets = {}
        self.performance_enabled = False
        
        self.logger.info("Phase 1 Integration Manager initialized")
        
    def integrate_all_improvements(self) -> bool:
        """Integrate all Phase 1 improvements into the application."""
        try:
            self.logger.info("Starting Phase 1 integration...")
            
            # 1. Setup responsive layout system
            self.setup_responsive_layout()
            
            # 2. Integrate advanced content management
            self.integrate_content_management()
            
            # 3. Enhance existing widgets
            self.enhance_existing_widgets()
            
            # 4. Apply initial layout optimization
            self.optimize_initial_layout()
            
            self.logger.info("Phase 1 integration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Phase 1 integration failed: {e}")
            return False
            
    def setup_responsive_layout(self):
        """Setup responsive layout system for the main window."""
        try:
            # Apply to main window structure
            self.apply_responsive_to_main_structure()
            
            # Setup window resize handling
            self.setup_resize_handling()
            
            self.logger.info("Responsive layout system setup completed")
            
        except Exception as e:
            self.logger.error(f"Failed to setup responsive layout: {e}")
            
    def apply_responsive_to_main_structure(self):
        """Apply responsive layout to main window structure."""
        try:
            # Find main panels/frames in the application
            main_panels = self.find_main_panels()
            
            for panel_id, panel_widget in main_panels.items():
                # Register panel with responsive layout
                self.responsive_layout.register_panel(panel_id, panel_widget)
                self.integrated_panels[panel_id] = panel_widget
                
            self.logger.info(f"Responsive layout applied to {len(main_panels)} panels")
            
        except Exception as e:
            self.logger.error(f"Failed to apply responsive layout to main structure: {e}")
            
    def find_main_panels(self) -> Dict[str, tk.Widget]:
        """Find main panels in the application."""
        panels = {}
        
        def find_panels(widget, depth=0):
            if depth > 8:  # Prevent infinite recursion
                return
                
            try:
                # Look for frames that might be main panels
                if isinstance(widget, (tk.Frame, ttk.Frame, tk.LabelFrame, ttk.LabelFrame)):
                    widget_name = widget.__class__.__name__
                    panel_id = f"{widget_name}_{len(panels)}"
                    panels[panel_id] = widget
                    
                # Continue traversing children
                for child in widget.winfo_children():
                    find_panels(child, depth + 1)
            except Exception:
                pass  # Skip problematic widgets
                
        find_panels(self.main_window)
        return panels
        
    def setup_resize_handling(self):
        """Setup window resize handling for responsive behavior."""
        def on_window_resize(event):
            if event.widget == self.main_window:
                try:
                    # Trigger content reflow
                    self.trigger_content_reflow()
                except Exception as e:
                    self.logger.error(f"Error handling window resize: {e}")
                    
        self.main_window.bind('<Configure>', on_window_resize)
        
    def integrate_content_management(self):
        """Integrate advanced content management system."""
        try:
            # Register all panels with content manager
            for panel_id, panel_widget in self.integrated_panels.items():
                self.content_manager.register_display_panel(panel_id, panel_widget)
                
            # Setup content optimization
            self.setup_content_optimization()
            
            self.logger.info("Content management system integrated")
            
        except Exception as e:
            self.logger.error(f"Failed to integrate content management: {e}")
            
    def setup_content_optimization(self):
        """Setup automatic content optimization."""
        def optimize_all_panels():
            for panel_id in self.integrated_panels.keys():
                try:
                    self.content_manager.optimize_layout(panel_id, 'balanced')
                except Exception as e:
                    self.logger.error(f"Failed to optimize panel {panel_id}: {e}")
                    
        # Store reference for later use
        self.optimize_content = optimize_all_panels
        
    def enhance_existing_widgets(self):
        """Enhance existing widgets with Phase 1 improvements."""
        try:
            # Find text widgets that can be enhanced
            text_widgets = self.find_text_widgets()
            
            for widget_id, widget in text_widgets.items():
                self.enhance_text_widget(widget_id, widget)
                
            self.logger.info(f"Enhanced {len(text_widgets)} text widgets")
            
        except Exception as e:
            self.logger.error(f"Failed to enhance existing widgets: {e}")
            
    def find_text_widgets(self) -> Dict[str, tk.Widget]:
        """Find text widgets that can be enhanced."""
        text_widgets = {}
        
        def find_text(widget, depth=0):
            if depth > 8:  # Prevent infinite recursion
                return
                
            try:
                if isinstance(widget, (tk.Text, tk.Label)):
                    widget_id = f"{widget.__class__.__name__}_{len(text_widgets)}"
                    text_widgets[widget_id] = widget
                    
                for child in widget.winfo_children():
                    find_text(child, depth + 1)
            except Exception:
                pass
                
        find_text(self.main_window)
        return text_widgets
        
    def enhance_text_widget(self, widget_id: str, widget: tk.Widget):
        """Enhance a specific text widget."""
        try:
            # Store reference for management
            self.enhanced_widgets[widget_id] = widget
            
        except Exception as e:
            self.logger.error(f"Failed to enhance widget {widget_id}: {e}")
            
    def optimize_initial_layout(self):
        """Apply initial layout optimization."""
        try:
            # Update the main window
            self.main_window.update_idletasks()
            
            # Optimize content layout
            if hasattr(self, 'optimize_content'):
                self.optimize_content()
                
            self.logger.info("Initial layout optimization completed")
            
        except Exception as e:
            self.logger.error(f"Failed to optimize initial layout: {e}")
            
    def trigger_content_reflow(self):
        """Trigger content reflow for responsive adjustment."""
        try:
            if hasattr(self, 'optimize_content'):
                self.optimize_content()
        except Exception as e:
            self.logger.error(f"Error during content reflow: {e}")
            
    def add_content_with_management(self, panel_id: str, content: Any, 
                                  priority: str = 'normal', content_type: str = 'text',
                                  metadata: Optional[Dict] = None) -> bool:
        """Add content using advanced content management."""
        try:
            return self.content_manager.add_content(
                panel_id, content, priority, content_type, metadata
            )
        except Exception as e:
            self.logger.error(f"Failed to add content to {panel_id}: {e}")
            return False
            
    def create_enhanced_text_widget(self, parent: tk.Widget, **kwargs) -> Union[EnhancedTextWidget, tk.Text]:
        """Create an enhanced text widget with all improvements."""
        try:
            enhanced_widget = EnhancedTextWidget(parent, **kwargs)
            return enhanced_widget
            
        except Exception as e:
            self.logger.error(f"Failed to create enhanced text widget: {e}")
            # Fallback to regular text widget
            return tk.Text(parent, **kwargs)
            
    def get_integration_status(self) -> Dict[str, Any]:
        """Get status of Phase 1 integration."""
        return {
            'responsive_panels': len(self.integrated_panels),
            'enhanced_widgets': len(self.enhanced_widgets),
            'content_manager_active': self.content_manager is not None,
            'total_managed_content': len(self.content_manager.content_items) if self.content_manager else 0
        }
        
    def create_responsive_panel(self, parent: tk.Widget, panel_id: str, **kwargs) -> tk.Frame:
        """Create a new responsive panel with Phase 1 improvements."""
        try:
            # Create frame
            frame = tk.Frame(parent, **kwargs)
            
            # Register with responsive layout
            self.responsive_layout.register_panel(panel_id, frame)
            
            # Register with content manager
            self.content_manager.register_display_panel(panel_id, frame)
            
            # Store reference
            self.integrated_panels[panel_id] = frame
            
            self.logger.info(f"Created responsive panel: {panel_id}")
            return frame
            
        except Exception as e:
            self.logger.error(f"Failed to create responsive panel {panel_id}: {e}")
            return tk.Frame(parent, **kwargs)  # Fallback
            
    def get_content_manager(self) -> AdvancedContentManager:
        """Get the content manager instance."""
        return self.content_manager
        
    def get_responsive_layout(self) -> ResponsiveLayoutManager:
        """Get the responsive layout manager instance."""
        return self.responsive_layout
        
    def clear_panel_content(self, panel_id: str):
        """Clear all content from a specific panel."""
        try:
            self.content_manager.clear_panel(panel_id)
        except Exception as e:
            self.logger.error(f"Failed to clear panel {panel_id}: {e}")
            
    def refresh_all_layouts(self):
        """Refresh all panel layouts."""
        try:
            for panel_id in self.integrated_panels.keys():
                self.content_manager.optimize_layout(panel_id, 'balanced')
        except Exception as e:
            self.logger.error(f"Failed to refresh layouts: {e}")


def integrate_phase1_improvements(main_window: tk.Tk) -> Phase1IntegrationManager:
    """
    Main function to integrate all Phase 1 improvements into an existing application.
    
    Args:
        main_window: The main tkinter window
        
    Returns:
        Phase1IntegrationManager instance for ongoing management
    """
    integration_manager = Phase1IntegrationManager(main_window)
    
    if integration_manager.integrate_all_improvements():
        return integration_manager
    else:
        raise Exception("Failed to integrate Phase 1 improvements")


def apply_to_existing_app(app_instance):
    """Apply Phase 1 improvements to existing application instance."""
    try:
        # Get main window from app instance
        main_window = None
        
        # Try different common attribute names
        for attr_name in ['window', 'root', 'main_window', 'master', '_root']:
            if hasattr(app_instance, attr_name):
                potential_window = getattr(app_instance, attr_name)
                if isinstance(potential_window, tk.Tk):
                    main_window = potential_window
                    break
                    
        if main_window is None:
            raise ValueError("Could not find main window in application instance")
            
        # Apply improvements
        integration_manager = integrate_phase1_improvements(main_window)
        
        # Store reference in app instance
        app_instance.phase1_manager = integration_manager
        
        return integration_manager
        
    except Exception as e:
        print(f"Failed to apply Phase 1 improvements to existing app: {e}")
        return None


# Utility functions for easy integration

def create_enhanced_scrollable_frame(parent: tk.Widget, integration_manager: Phase1IntegrationManager) -> tk.Frame:
    """Create an enhanced scrollable frame with Phase 1 improvements."""
    try:
        # Create main frame
        main_frame = tk.Frame(parent)
        
        # Create canvas and scrollbar
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)
        
        # Configure scrolling
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return scrollable_frame
        
    except Exception as e:
        print(f"Failed to create enhanced scrollable frame: {e}")
        return tk.Frame(parent)


def demo_phase1_improvements():
    """Create a demo window showing Phase 1 improvements."""
    root = tk.Tk()
    root.title("Phase 1 Improvements Demo")
    root.geometry("900x700")
    
    try:
        # Apply Phase 1 improvements
        integration_manager = integrate_phase1_improvements(root)
        
        # Create main container
        main_container = tk.Frame(root, relief='groove', bd=2)
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create demo panels
        left_panel = integration_manager.create_responsive_panel(main_container, "left_panel", bg='lightblue')
        right_panel = integration_manager.create_responsive_panel(main_container, "right_panel", bg='lightgreen')
        
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 5))
        right_panel.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # Add content to panels
        integration_manager.add_content_with_management(
            "left_panel", 
            "HIGH PRIORITY: This message should appear prominently at the top.",
            priority="high",
            content_type="status"
        )
        
        integration_manager.add_content_with_management(
            "left_panel",
            "Normal priority content: This text demonstrates how content is managed based on priority and available space.",
            priority="normal",
            content_type="text"
        )
        
        integration_manager.add_content_with_management(
            "right_panel",
            "Analysis Result: The Phase 1 improvements include responsive layout, advanced content management, and enhanced text widgets.",
            priority="normal",
            content_type="analysis"
        )
        
        # Create enhanced text widget in right panel
        enhanced_text = integration_manager.create_enhanced_text_widget(right_panel, height=8, wrap='word')
        enhanced_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Add some sample text
        sample_text = """This is an enhanced text widget with Phase 1 improvements:

1. Intelligent content management
2. Responsive layout capabilities  
3. Advanced overflow handling
4. Priority-based display

Try resizing the window to see the responsive behavior in action!"""
        
        if hasattr(enhanced_text, 'insert'):
            enhanced_text.insert('1.0', sample_text)
            if hasattr(enhanced_text, 'configure'):
                enhanced_text.configure(state='disabled')
        
        # Status display
        status_frame = tk.Frame(root, relief='sunken', bd=1)
        status_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        status = integration_manager.get_integration_status()
        status_text = f"Integration Status: {status['responsive_panels']} panels, {status['enhanced_widgets']} widgets, {status['total_managed_content']} content items"
        
        status_label = tk.Label(status_frame, text=status_text, font=('Arial', 9))
        status_label.pack(pady=5)
        
        # Control buttons
        controls_frame = tk.Frame(root)
        controls_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        refresh_btn = tk.Button(
            controls_frame, 
            text="Refresh Layouts",
            command=integration_manager.refresh_all_layouts
        )
        refresh_btn.pack(side='left', padx=5)
        
        clear_btn = tk.Button(
            controls_frame,
            text="Clear Left Panel", 
            command=lambda: integration_manager.clear_panel_content("left_panel")
        )
        clear_btn.pack(side='left', padx=5)
        
        print("Phase 1 Demo Integration Status:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("\nDemo Controls:")
        print("- Resize window to see responsive behavior")
        print("- Use buttons to test content management")
        print("- Scroll text widgets for performance testing")
        
        root.mainloop()
        
    except Exception as e:
        print(f"Demo failed: {e}")
        # Simple fallback demo
        tk.Label(root, text=f"Demo Error: {e}", wraplength=400).pack(pady=50)
        root.mainloop()


if __name__ == "__main__":
    demo_phase1_improvements()
