#!/usr/bin/env python3
"""
AI Analysis Program - Main Entry Point
A comprehensive tool for topical AI analysis using Ollama API
"""


import tkinter as tk
from tkinter import messagebox
import sys
from pathlib import Path
from utils.config import Config
from utils.logging_setup import setup_logging
from gui.enhanced_main_window import Enhanced<PERSON><PERSON>Window
from gui.performance_upgrade import quick_performance_fix, PerformanceSettingsPanel

# Add project root to path (must be after imports for linting compliance)
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class AIAnalysisApp:
    """Main application class for AI Analysis Program"""
    
    def __init__(self):
        self.config = Config()
        self.logger = setup_logging()
        self.root = None
        self.main_window = None
        
    def initialize(self):
        """Initialize the application"""
        try:
            # Create main window with enhanced styling
            self.root = tk.Tk()
            self.root.title("AI Analysis Program - Enhanced Edition")

            # Get screen dimensions for responsive sizing
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # Calculate window size as percentage of screen (90% width, 85% height)
            window_width = int(screen_width * 0.9)
            window_height = int(screen_height * 0.85)

            # Center the window
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
            self.root.minsize(1200, 800)  # Increased minimum size

            # Set window icon and properties
            self.root.state('normal')  # Start in normal state, user can maximize if needed

            # Set application icon (if available)
            try:
                self.root.iconbitmap("assets/icon.ico")
            except Exception:
                pass  # Icon file not found, continue without it

            # Create main window instance
            self.main_window = EnhancedMainWindow(self.root, self.config)
            
            # Apply performance upgrades to eliminate scrolling blur/lag
            self.apply_performance_upgrades()

            # Configure window closing
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            self.logger.info("Application initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            messagebox.showerror("Initialization Error", f"Failed to start application:\n{str(e)}")
            return False
    
    def apply_performance_upgrades(self):
        """Apply performance upgrades to eliminate scrolling issues"""
        try:
            # Import performance upgrade locally to avoid unused import warnings
            from gui.performance_upgrade import upgrade_scrolling_performance
            
            # Use a more robust approach to find scrollable frames
            self.performance_upgrades = []
            
            def find_scrollable_frames(widget):
                """Recursively find all scrollable frames"""
                scrollable_frames = []
                
                # Check if this widget has a canvas (indicating scrollable capability)
                if hasattr(widget, 'canvas') and hasattr(widget, 'scrollable_frame'):
                    scrollable_frames.append(widget)
                
                # Recursively check children
                try:
                    for child in widget.winfo_children():
                        scrollable_frames.extend(find_scrollable_frames(child))
                except Exception:
                    pass
                    
                return scrollable_frames
            
            # Find all scrollable frames in the main window
            scrollable_frames = find_scrollable_frames(self.main_window)
            
            # Apply performance upgrades with high performance mode enabled
            for frame in scrollable_frames:
                try:
                    upgrade = upgrade_scrolling_performance(frame, performance_mode=True)
                    self.performance_upgrades.append(upgrade)
                except Exception as e:
                    self.logger.warning(f"Failed to upgrade frame {frame}: {e}")
            
            if self.performance_upgrades:
                self.logger.info(f"Applied performance upgrades to {len(self.performance_upgrades)} scrollable frames")
                
                # Create a simple menu for performance settings
                self.create_performance_hotkey()
            else:
                self.logger.info("No scrollable frames found, but performance system is ready")
                
        except Exception as e:
            self.logger.error(f"Failed to apply performance upgrades: {e}")
            
    def create_performance_hotkey(self):
        """Create hotkey for quick performance toggle"""
        try:
            # Bind Ctrl+Shift+P for performance settings
            if self.root:
                self.root.bind('<Control-Shift-P>', lambda e: self.open_performance_settings())
                
                # Show notification about hotkey
                self.root.after(2000, lambda: self.show_performance_notification())
            
        except Exception as e:
            self.logger.error(f"Failed to create performance hotkey: {e}")
            
    def show_performance_notification(self):
        """Show notification about performance improvements"""
        try:
            messagebox.showinfo(
                "Performance Upgrade Active",
                "✅ High-Performance Scrolling Enabled\n\n"
                "• Scrolling blur eliminated\n"
                "• Lag and catchup delays removed\n"
                "• Instant scroll response\n\n"
                "Press Ctrl+Shift+P for performance settings"
            )
        except Exception:
            pass
            
    def add_performance_menu(self):
        """Add performance settings to the application menu - simplified version"""
        # This method is kept for compatibility but simplified
        pass
            
    def open_performance_settings(self):
        """Open performance settings dialog"""
        try:
            from gui.performance_upgrade import PerformanceSettingsPanel
            if hasattr(self, 'performance_upgrades') and self.performance_upgrades:
                PerformanceSettingsPanel(self.root, self.performance_upgrades[0])
            else:
                messagebox.showinfo(
                    "Performance Settings", 
                    "High-Performance Mode is active.\n\n"
                    "All scrolling optimizations are enabled to eliminate blur and lag."
                )
        except Exception as e:
            self.logger.error(f"Failed to open performance settings: {e}")
            
    def toggle_performance_mode(self):
        """Toggle performance mode for all upgraded frames"""
        try:
            if hasattr(self, 'performance_upgrades'):
                for upgrade in self.performance_upgrades:
                    current_mode = upgrade.performance_mode
                    upgrade.set_performance_mode(not current_mode)
                self.logger.info("Toggled performance mode")
                
                # Show status
                mode_status = "enabled" if not upgrade.performance_mode else "disabled"
                messagebox.showinfo("Performance Mode", f"Performance mode {mode_status}")
        except Exception as e:
            self.logger.error(f"Failed to toggle performance mode: {e}")
    
    def run(self):
        """Run the main application loop"""
        if not self.initialize():
            return

        try:
            self.logger.info("Starting application main loop")
            if self.root is not None:
                self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            messagebox.showerror("Application Error", f"An error occurred:\n{str(e)}")
        finally:
            self.cleanup()
    
    def on_closing(self):
        """Handle application closing"""
        try:
            # Save any pending work
            if self.main_window:
                if not self.main_window.confirm_close():
                    return

            self.logger.info("Application closing")
            if self.root is not None:
                self.root.destroy()

        except Exception as e:
            self.logger.error(f"Error during application closing: {e}")
            if self.root is not None:
                self.root.destroy()
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.main_window:
                self.main_window.cleanup()
            self.logger.info("Application cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

def main():
    """Main entry point"""
    try:
        app = AIAnalysisApp()
        app.run()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
