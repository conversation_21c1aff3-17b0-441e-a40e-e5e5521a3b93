"""
Performance Test and Demonstration Script
Shows the difference between original scrolling and optimized scrolling.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from gui.high_performance_scrolling import HighPerformanceScrollableFrame
from gui.enhanced_scrolling import EnhancedScrollableFrame


class PerformanceTestWindow:
    """Window for testing and comparing scrolling performance."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Scrolling Performance Test - Before vs After")
        self.root.geometry("1200x700")
        
        self.create_test_interface()
        
    def create_test_interface(self):
        """Create the test interface with side-by-side comparison."""
        
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="Scrolling Performance Test: Original vs Optimized",
            font=('Arial', 16, 'bold'),
            fg='navy'
        )
        title_label.pack(pady=(0, 20))
        
        # Instructions
        instructions = tk.Label(
            main_frame,
            text="Test scrolling performance in both panels. The left panel uses original scrolling,\n"
                 "the right panel uses the new high-performance scrolling system.\n"
                 "Use mouse wheel to scroll and notice the difference in blur and lag.",
            font=('Arial', 10),
            justify='center'
        )
        instructions.pack(pady=(0, 20))
        
        # Comparison frame
        comparison_frame = ttk.Frame(main_frame)
        comparison_frame.pack(fill='both', expand=True)
        
        # Left panel - Original scrolling
        left_frame = ttk.LabelFrame(comparison_frame, text="Original Scrolling (with blur/lag)", padding=10)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
        
        # Right panel - Optimized scrolling
        right_frame = ttk.LabelFrame(comparison_frame, text="High-Performance Scrolling (no blur/lag)", padding=10)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # Create scrollable content for both panels
        self.create_scrollable_content(left_frame, use_optimized=False)
        self.create_scrollable_content(right_frame, use_optimized=True)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))
        
        ttk.Button(
            button_frame,
            text="Show Performance Stats",
            command=self.show_performance_stats
        ).pack(side='left', padx=(0, 10))
        
        ttk.Button(
            button_frame,
            text="Toggle Performance Mode",
            command=self.toggle_performance_mode
        ).pack(side='left', padx=(0, 10))
        
        ttk.Button(
            button_frame,
            text="Close Test",
            command=self.root.destroy
        ).pack(side='right')
        
    def create_scrollable_content(self, parent, use_optimized=True):
        """Create scrollable content for testing."""
        
        if use_optimized:
            # Use high-performance scrollable frame
            scrollable = HighPerformanceScrollableFrame(parent, performance_mode=True)
            self.optimized_frame = scrollable
        else:
            # Use original enhanced scrollable frame
            scrollable = EnhancedScrollableFrame(parent)
            self.original_frame = scrollable
            
        scrollable.pack(fill='both', expand=True)
        
        # Add lots of content to test scrolling
        content_frame = scrollable.scrollable_frame
        
        # Add various types of content
        for i in range(100):
            if i % 10 == 0:
                # Header
                header = tk.Label(
                    content_frame,
                    text=f"Section {i//10 + 1}: Performance Test Content",
                    font=('Arial', 12, 'bold'),
                    bg='lightblue',
                    pady=5
                )
                header.pack(fill='x', pady=(10, 5))
            
            # Content text
            content_text = f"""
            Line {i+1}: This is test content for scrolling performance evaluation.
            The high-performance scrolling system eliminates blur and lag during scroll operations.
            It uses adaptive frame rates and optimized event handling to provide smooth scrolling.
            You should notice a significant difference when scrolling with the mouse wheel.
            """
            
            text_label = tk.Label(
                content_frame,
                text=content_text.strip(),
                justify='left',
                wraplength=400,
                pady=2
            )
            text_label.pack(fill='x', padx=10, pady=2)
            
            # Add some interactive elements
            if i % 20 == 0:
                button_frame = tk.Frame(content_frame)
                button_frame.pack(fill='x', padx=10, pady=5)
                
                tk.Button(
                    button_frame,
                    text=f"Test Button {i//20 + 1}",
                    command=lambda x=i: self.test_button_click(x)
                ).pack(side='left', padx=(0, 5))
                
                tk.Entry(button_frame, width=30).pack(side='left', padx=(0, 5))
                
    def test_button_click(self, index):
        """Handle test button clicks."""
        messagebox.showinfo("Button Test", f"Test button {index//20 + 1} clicked!")
        
    def show_performance_stats(self):
        """Show performance statistics."""
        try:
            if hasattr(self, 'optimized_frame'):
                stats = self.optimized_frame.get_performance_stats()
                
                stats_text = f"""Performance Statistics:

Average FPS: {stats.get('average_fps', 0):.1f}
Performance Mode: {'Enabled' if stats.get('performance_mode', False) else 'Disabled'}
Currently Scrolling: {'Yes' if stats.get('is_scrolling', False) else 'No'}
Queue Length: {stats.get('queue_length', 0)}

High-Performance Benefits:
✓ Eliminates scrolling blur
✓ Removes lag and catchup delays  
✓ Provides instant scroll response
✓ Reduces CPU usage during scrolling
✓ Adaptive frame rate optimization"""
                
                messagebox.showinfo("Performance Statistics", stats_text)
            else:
                messagebox.showinfo("Performance Statistics", "Performance system not available")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get performance stats: {e}")
            
    def toggle_performance_mode(self):
        """Toggle performance mode for the optimized frame."""
        try:
            if hasattr(self, 'optimized_frame'):
                current_mode = self.optimized_frame.performance_mode
                self.optimized_frame.set_performance_mode(not current_mode)
                
                mode_text = "enabled" if not current_mode else "disabled"
                messagebox.showinfo("Performance Mode", f"Performance mode {mode_text} for right panel")
            else:
                messagebox.showinfo("Performance Mode", "Optimized frame not available")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to toggle performance mode: {e}")
            
    def run(self):
        """Run the performance test window."""
        self.root.mainloop()


def run_performance_test():
    """Run the performance test demonstration."""
    try:
        test_window = PerformanceTestWindow()
        test_window.run()
    except Exception as e:
        print(f"Failed to run performance test: {e}")


if __name__ == "__main__":
    run_performance_test()
