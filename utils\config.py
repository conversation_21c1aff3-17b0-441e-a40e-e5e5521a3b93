"""
Configuration management for AI Analysis Program
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

class Config:
    """Application configuration manager"""
    
    DEFAULT_CONFIG = {
        "ollama": {
            "base_url": "http://localhost:11434",
            "default_model": "llama2",
            "timeout": 30,
            "temperature": 0.7,
            "max_tokens": 2048
        },
        "analysis": {
            "max_recursive_depth": 5,
            "connection_threshold": 0.7,
            "batch_size": 10,
            "enable_caching": True
        },
        "export": {
            "default_format": "markdown",
            "output_directory": "exports",
            "include_metadata": True,
            "timestamp_format": "%Y%m%d_%H%M%S"
        },
        "gui": {
            "theme": "default",
            "font_size": 10,
            "auto_save": True,
            "save_interval": 300  # seconds
        },
        "logging": {
            "level": "INFO",
            "file_logging": True,
            "console_logging": True,
            "log_directory": "logs"
        }
    }
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.config_data = self.DEFAULT_CONFIG.copy()
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(self.config_data, user_config)
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
            print("Using default configuration")
    
    def save_config(self) -> None:
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def _merge_config(self, base: Dict[str, Any], update: Dict[str, Any]) -> None:
        """Recursively merge configuration dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'ollama.base_url')"""
        keys = key_path.split('.')
        value = self.config_data
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config_data
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the final value
        config[keys[-1]] = value
    
    def get_ollama_config(self) -> Dict[str, Any]:
        """Get Ollama-specific configuration"""
        return self.config_data.get("ollama", {})
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """Get analysis-specific configuration"""
        return self.config_data.get("analysis", {})
    
    def get_export_config(self) -> Dict[str, Any]:
        """Get export-specific configuration"""
        return self.config_data.get("export", {})
    
    def get_gui_config(self) -> Dict[str, Any]:
        """Get GUI-specific configuration"""
        return self.config_data.get("gui", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging-specific configuration"""
        return self.config_data.get("logging", {})
    
    def ensure_directories(self) -> None:
        """Ensure required directories exist"""
        directories = [
            self.get("export.output_directory", "exports"),
            self.get("logging.log_directory", "logs"),
            "data/cache",
            "data/exports"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def validate_config(self) -> bool:
        """Validate configuration settings"""
        try:
            # Check required sections
            required_sections = ["ollama", "analysis", "export", "gui", "logging"]
            for section in required_sections:
                if section not in self.config_data:
                    print(f"Warning: Missing configuration section: {section}")
                    return False
            
            # Validate specific settings
            if self.get("analysis.max_recursive_depth", 0) <= 0:
                print("Warning: Invalid max_recursive_depth")
                return False
            
            if not (0 <= self.get("analysis.connection_threshold", 0) <= 1):
                print("Warning: Invalid connection_threshold (must be 0-1)")
                return False
            
            return True
            
        except Exception as e:
            print(f"Configuration validation error: {e}")
            return False
