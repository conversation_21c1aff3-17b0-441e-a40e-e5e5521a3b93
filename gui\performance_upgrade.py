"""
Performance Upgrade for Enhanced Scrolling
Drop-in performance enhancements that eliminate scrolling blur and lag.
"""

import tkinter as tk
from gui.high_performance_scrolling import OptimizedScrolling, PerformanceSettings


class ScrollingPerformanceUpgrade:
    """Performance upgrade that can be applied to existing scrollable frames."""
    
    def __init__(self, scrollable_frame, performance_mode=True):
        self.scrollable_frame = scrollable_frame
        self.performance_mode = performance_mode
        self.original_mousewheel = None
        self.performance_settings = PerformanceSettings()
        
        # Apply performance upgrades
        self.apply_performance_upgrade()
        
    def apply_performance_upgrade(self):
        """Apply performance optimizations to existing scrollable frame."""
        canvas = getattr(self.scrollable_frame, 'canvas', None)
        if not canvas:
            print("Warning: No canvas found in scrollable frame")
            return
            
        # Store original mousewheel handler
        self.store_original_bindings(canvas)
        
        # Create optimized scrolling system
        self.optimized_scrolling = OptimizedScrolling(canvas, self.performance_mode)
        
        # Override existing mousewheel events with performance version
        self.replace_mousewheel_bindings(canvas)
        
        # Apply canvas optimizations
        self.optimize_canvas(canvas)
        
        # Disable problematic smooth scrolling if it exists
        self.disable_legacy_smooth_scrolling()
        
    def store_original_bindings(self, canvas):
        """Store original event bindings for fallback."""
        try:
            # Store mousewheel bindings
            self.original_bindings = {
                '<MouseWheel>': canvas.bind_class('Canvas', '<MouseWheel>'),
                '<Button-4>': canvas.bind_class('Canvas', '<Button-4>'),
                '<Button-5>': canvas.bind_class('Canvas', '<Button-5>')
            }
        except Exception:
            self.original_bindings = {}
            
    def replace_mousewheel_bindings(self, canvas):
        """Replace mousewheel bindings with high-performance versions."""
        # Remove existing bindings
        canvas.unbind('<MouseWheel>')
        canvas.unbind('<Button-4>')
        canvas.unbind('<Button-5>')
        
        # Add optimized bindings
        canvas.bind('<MouseWheel>', self.optimized_scrolling.optimized_mousewheel, '+')
        canvas.bind('<Button-4>', self.optimized_scrolling.optimized_mousewheel, '+')
        canvas.bind('<Button-5>', self.optimized_scrolling.optimized_mousewheel, '+')
        
        # Enable keyboard focus for navigation
        canvas.configure(takefocus=True)
        canvas.focus_set()
        
    def optimize_canvas(self, canvas):
        """Apply canvas-level performance optimizations."""
        canvas.configure(
            highlightthickness=0,
            borderwidth=0,
            relief='flat',
            # Performance optimizations
            bg=canvas.master.cget('bg') if hasattr(canvas.master, 'cget') else 'white'
        )
        
    def disable_legacy_smooth_scrolling(self):
        """Disable legacy smooth scrolling that causes performance issues."""
        # Disable smooth scrolling in the parent frame
        if hasattr(self.scrollable_frame, 'smooth_scroll_steps'):
            self.scrollable_frame.smooth_scroll_steps = 1
            
        if hasattr(self.scrollable_frame, 'smooth_scroll_enabled'):
            self.scrollable_frame.smooth_scroll_enabled = False
            
        # Cancel any existing scroll animations
        if hasattr(self.scrollable_frame, 'current_scroll_animation'):
            if self.scrollable_frame.current_scroll_animation:
                try:
                    self.scrollable_frame.after_cancel(self.scrollable_frame.current_scroll_animation)
                    self.scrollable_frame.current_scroll_animation = None
                except Exception:
                    pass
                    
    def set_performance_mode(self, enabled: bool):
        """Toggle performance mode."""
        self.performance_mode = enabled
        self.optimized_scrolling.set_performance_mode(enabled)
        
        if enabled:
            # Maximum performance settings
            self.performance_settings.apply_performance_profile('maximum_performance')
        else:
            # Balanced performance settings
            self.performance_settings.apply_performance_profile('balanced')
            
    def get_performance_stats(self):
        """Get current performance statistics."""
        return self.optimized_scrolling.get_performance_stats()
        
    def restore_original_bindings(self):
        """Restore original bindings if needed."""
        if hasattr(self, 'original_bindings') and self.original_bindings:
            canvas = self.scrollable_frame.canvas
            for event, binding in self.original_bindings.items():
                if binding:
                    canvas.bind(event, binding)


def upgrade_scrolling_performance(scrollable_frame, performance_mode=True):
    """
    Quick function to upgrade scrolling performance on any scrollable frame.
    
    Args:
        scrollable_frame: The scrollable frame to upgrade
        performance_mode: True for maximum performance (eliminates blur/lag)
    
    Returns:
        ScrollingPerformanceUpgrade instance for further configuration
    """
    return ScrollingPerformanceUpgrade(scrollable_frame, performance_mode)


class PerformanceSettingsPanel(tk.Toplevel):
    """Performance settings dialog for user configuration."""
    
    def __init__(self, parent, performance_upgrade):
        super().__init__(parent)
        self.performance_upgrade = performance_upgrade
        self.title("Scrolling Performance Settings")
        self.geometry("400x300")
        self.resizable(False, False)
        
        self.create_widgets()
        self.center_window()
        
    def create_widgets(self):
        """Create performance settings widgets."""
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame, 
            text="Scrolling Performance Settings",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Performance mode toggle
        self.performance_var = tk.BooleanVar(
            value=self.performance_upgrade.performance_mode
        )
        performance_frame = tk.Frame(main_frame)
        performance_frame.pack(fill='x', pady=5)
        
        tk.Label(performance_frame, text="High Performance Mode:").pack(side='left')
        performance_check = tk.Checkbutton(
            performance_frame,
            variable=self.performance_var,
            command=self.on_performance_toggle
        )
        performance_check.pack(side='right')
        
        # Performance info
        info_frame = tk.LabelFrame(main_frame, text="Performance Information", padx=10, pady=10)
        info_frame.pack(fill='both', expand=True, pady=20)
        
        # Current stats
        self.stats_label = tk.Label(info_frame, justify='left', anchor='w')
        self.stats_label.pack(fill='both', expand=True)
        
        # Update stats
        self.update_stats()
        
        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))
        
        tk.Button(
            button_frame, 
            text="Apply Settings",
            command=self.apply_settings
        ).pack(side='right', padx=(5, 0))
        
        tk.Button(
            button_frame, 
            text="Close",
            command=self.destroy
        ).pack(side='right')
        
        # Auto-update stats
        self.after(1000, self.update_stats_loop)
        
    def update_stats(self):
        """Update performance statistics display."""
        stats = self.performance_upgrade.get_performance_stats()
        
        stats_text = f"""Current Performance Statistics:

Average FPS: {stats.get('average_fps', 0):.1f}
Performance Mode: {'Enabled' if stats.get('performance_mode', False) else 'Disabled'}
Currently Scrolling: {'Yes' if stats.get('is_scrolling', False) else 'No'}
Scroll Queue Length: {stats.get('queue_length', 0)}

Performance Mode Benefits:
✓ Eliminates scrolling blur
✓ Removes lag and catchup delays
✓ Provides instant scroll response
✓ Reduces CPU usage during scrolling

Recommended: Keep Performance Mode enabled
for the best user experience."""
        
        self.stats_label.config(text=stats_text)
        
    def update_stats_loop(self):
        """Continuously update stats."""
        try:
            self.update_stats()
            self.after(1000, self.update_stats_loop)
        except Exception:
            pass  # Window was destroyed
            
    def on_performance_toggle(self):
        """Handle performance mode toggle."""
        self.performance_upgrade.set_performance_mode(self.performance_var.get())
        
    def apply_settings(self):
        """Apply current settings."""
        self.performance_upgrade.set_performance_mode(self.performance_var.get())
        self.destroy()
        
    def center_window(self):
        """Center the window on the parent."""
        if hasattr(self.master, 'winfo_rootx'):
            try:
                self.transient(self.master)  # type: ignore
                self.grab_set()
            except Exception:
                pass
            
            # Calculate center position
            self.geometry("+%d+%d" % (
                self.master.winfo_rootx() + 50,
                self.master.winfo_rooty() + 50
            ))


# Convenience function for quick performance upgrade
def quick_performance_fix(scrollable_frames):
    """
    Apply quick performance fix to multiple scrollable frames.
    
    Args:
        scrollable_frames: List of scrollable frames or single frame
    
    Returns:
        List of performance upgrade instances
    """
    if not isinstance(scrollable_frames, list):
        scrollable_frames = [scrollable_frames]
        
    upgrades = []
    for frame in scrollable_frames:
        upgrade = upgrade_scrolling_performance(frame, performance_mode=True)
        upgrades.append(upgrade)
        
    return upgrades
