"""
Project and Analysis History Management
Provides functionality to save, load, and manage analysis projects
"""

import json
import sqlite3
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import sanitize_filename, safe_json_save, safe_json_load

class ProjectManager(LoggerMixin):
    """Manages analysis projects and history"""
    
    def __init__(self, config: Config):
        self.config = config
        self.projects_dir = Path("data/projects")
        self.projects_dir.mkdir(parents=True, exist_ok=True)
        self.db_path = Path("data/analysis_history.db")
        self.init_database()
    
    def init_database(self):
        """Initialize the analysis history database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at REAL NOT NULL,
                    modified_at REAL NOT NULL,
                    file_path TEXT NOT NULL,
                    analysis_type TEXT,
                    topic_count INTEGER DEFAULT 0,
                    tags TEXT,
                    favorite BOOLEAN DEFAULT 0
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS analysis_history (
                    id TEXT PRIMARY KEY,
                    project_id TEXT,
                    analysis_type TEXT NOT NULL,
                    topic TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    duration_seconds REAL,
                    model_used TEXT,
                    success BOOLEAN DEFAULT 1,
                    result_size_bytes INTEGER,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_projects_created ON projects(created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_projects_modified ON projects(modified_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_history_created ON analysis_history(created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_history_project ON analysis_history(project_id)")
    
    def create_project(self, name: str, description: str = "", topic_data: Dict[str, Any] = None) -> str:
        """Create a new project"""
        project_id = str(uuid.uuid4())
        timestamp = time.time()
        
        # Create project data
        project_data = {
            "id": project_id,
            "name": name,
            "description": description,
            "created_at": timestamp,
            "modified_at": timestamp,
            "topic_data": topic_data or {},
            "analysis_results": [],
            "settings": {},
            "tags": [],
            "favorite": False
        }
        
        # Save project file
        safe_filename = sanitize_filename(f"{name}_{project_id[:8]}")
        file_path = self.projects_dir / f"{safe_filename}.json"
        
        if safe_json_save(project_data, file_path):
            # Add to database
            topic_count = len(topic_data.get("sub_topics", [])) if topic_data else 0
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO projects 
                    (id, name, description, created_at, modified_at, file_path, topic_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (project_id, name, description, timestamp, timestamp, str(file_path), topic_count))
            
            self.logger.info(f"Created project: {name} ({project_id})")
            return project_id
        else:
            raise Exception("Failed to save project file")
    
    def load_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Load a project by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT file_path FROM projects WHERE id = ?", (project_id,))
                row = cursor.fetchone()
                
                if row:
                    file_path = Path(row[0])
                    if file_path.exists():
                        return safe_json_load(file_path)
                    else:
                        self.logger.warning(f"Project file not found: {file_path}")
                        return None
                else:
                    self.logger.warning(f"Project not found in database: {project_id}")
                    return None
        except Exception as e:
            self.logger.error(f"Failed to load project {project_id}: {e}")
            return None
    
    def save_project(self, project_data: Dict[str, Any]) -> bool:
        """Save project data"""
        try:
            project_id = project_data.get("id")
            if not project_id:
                raise ValueError("Project ID is required")
            
            # Update modified timestamp
            project_data["modified_at"] = time.time()
            
            # Get file path from database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT file_path FROM projects WHERE id = ?", (project_id,))
                row = cursor.fetchone()
                
                if row:
                    file_path = Path(row[0])
                    
                    # Save project file
                    if safe_json_save(project_data, file_path):
                        # Update database
                        topic_count = len(project_data.get("topic_data", {}).get("sub_topics", []))
                        conn.execute("""
                            UPDATE projects 
                            SET name = ?, description = ?, modified_at = ?, topic_count = ?
                            WHERE id = ?
                        """, (
                            project_data.get("name", ""),
                            project_data.get("description", ""),
                            project_data["modified_at"],
                            topic_count,
                            project_id
                        ))
                        
                        self.logger.info(f"Saved project: {project_id}")
                        return True
                    else:
                        return False
                else:
                    self.logger.error(f"Project not found in database: {project_id}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Failed to save project: {e}")
            return False
    
    def delete_project(self, project_id: str) -> bool:
        """Delete a project"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get file path
                cursor = conn.execute("SELECT file_path FROM projects WHERE id = ?", (project_id,))
                row = cursor.fetchone()
                
                if row:
                    file_path = Path(row[0])
                    
                    # Delete file if exists
                    if file_path.exists():
                        file_path.unlink()
                    
                    # Delete from database
                    conn.execute("DELETE FROM analysis_history WHERE project_id = ?", (project_id,))
                    conn.execute("DELETE FROM projects WHERE id = ?", (project_id,))
                    
                    self.logger.info(f"Deleted project: {project_id}")
                    return True
                else:
                    self.logger.warning(f"Project not found: {project_id}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Failed to delete project {project_id}: {e}")
            return False
    
    def list_projects(self, limit: int = 50, offset: int = 0, search: str = "") -> List[Dict[str, Any]]:
        """List projects with optional search"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if search:
                    cursor = conn.execute("""
                        SELECT id, name, description, created_at, modified_at, analysis_type, topic_count, favorite
                        FROM projects 
                        WHERE name LIKE ? OR description LIKE ?
                        ORDER BY modified_at DESC 
                        LIMIT ? OFFSET ?
                    """, (f"%{search}%", f"%{search}%", limit, offset))
                else:
                    cursor = conn.execute("""
                        SELECT id, name, description, created_at, modified_at, analysis_type, topic_count, favorite
                        FROM projects 
                        ORDER BY modified_at DESC 
                        LIMIT ? OFFSET ?
                    """, (limit, offset))
                
                projects = []
                for row in cursor.fetchall():
                    projects.append({
                        "id": row[0],
                        "name": row[1],
                        "description": row[2],
                        "created_at": datetime.fromtimestamp(row[3]).isoformat(),
                        "modified_at": datetime.fromtimestamp(row[4]).isoformat(),
                        "analysis_type": row[5],
                        "topic_count": row[6],
                        "favorite": bool(row[7])
                    })
                
                return projects
                
        except Exception as e:
            self.logger.error(f"Failed to list projects: {e}")
            return []
    
    def add_analysis_to_history(self, project_id: str, analysis_type: str, topic: str, 
                               duration: float, model_used: str, success: bool, result_size: int) -> str:
        """Add analysis to history"""
        try:
            history_id = str(uuid.uuid4())
            timestamp = time.time()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO analysis_history 
                    (id, project_id, analysis_type, topic, created_at, duration_seconds, 
                     model_used, success, result_size_bytes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (history_id, project_id, analysis_type, topic, timestamp, 
                      duration, model_used, success, result_size))
            
            return history_id
            
        except Exception as e:
            self.logger.error(f"Failed to add analysis to history: {e}")
            return ""
    
    def get_analysis_history(self, project_id: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get analysis history"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if project_id:
                    cursor = conn.execute("""
                        SELECT h.id, h.analysis_type, h.topic, h.created_at, h.duration_seconds,
                               h.model_used, h.success, h.result_size_bytes, p.name as project_name
                        FROM analysis_history h
                        LEFT JOIN projects p ON h.project_id = p.id
                        WHERE h.project_id = ?
                        ORDER BY h.created_at DESC
                        LIMIT ?
                    """, (project_id, limit))
                else:
                    cursor = conn.execute("""
                        SELECT h.id, h.analysis_type, h.topic, h.created_at, h.duration_seconds,
                               h.model_used, h.success, h.result_size_bytes, p.name as project_name
                        FROM analysis_history h
                        LEFT JOIN projects p ON h.project_id = p.id
                        ORDER BY h.created_at DESC
                        LIMIT ?
                    """, (limit,))
                
                history = []
                for row in cursor.fetchall():
                    history.append({
                        "id": row[0],
                        "analysis_type": row[1],
                        "topic": row[2],
                        "created_at": datetime.fromtimestamp(row[3]).isoformat(),
                        "duration_seconds": row[4],
                        "model_used": row[5],
                        "success": bool(row[6]),
                        "result_size_bytes": row[7],
                        "project_name": row[8]
                    })
                
                return history
                
        except Exception as e:
            self.logger.error(f"Failed to get analysis history: {e}")
            return []
    
    def get_project_statistics(self) -> Dict[str, Any]:
        """Get project statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Project stats
                cursor = conn.execute("SELECT COUNT(*) FROM projects")
                total_projects = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM projects WHERE favorite = 1")
                favorite_projects = cursor.fetchone()[0]
                
                # Analysis stats
                cursor = conn.execute("SELECT COUNT(*) FROM analysis_history")
                total_analyses = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM analysis_history WHERE success = 1")
                successful_analyses = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT AVG(duration_seconds) FROM analysis_history WHERE success = 1")
                avg_duration = cursor.fetchone()[0] or 0
                
                # Most used analysis type
                cursor = conn.execute("""
                    SELECT analysis_type, COUNT(*) as count 
                    FROM analysis_history 
                    GROUP BY analysis_type 
                    ORDER BY count DESC 
                    LIMIT 1
                """)
                most_used_type = cursor.fetchone()
                
                return {
                    "total_projects": total_projects,
                    "favorite_projects": favorite_projects,
                    "total_analyses": total_analyses,
                    "successful_analyses": successful_analyses,
                    "success_rate": successful_analyses / total_analyses if total_analyses > 0 else 0,
                    "average_duration": avg_duration,
                    "most_used_analysis_type": most_used_type[0] if most_used_type else "None"
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get project statistics: {e}")
            return {}
    
    def toggle_favorite(self, project_id: str) -> bool:
        """Toggle project favorite status"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get current status
                cursor = conn.execute("SELECT favorite FROM projects WHERE id = ?", (project_id,))
                row = cursor.fetchone()
                
                if row:
                    new_status = not bool(row[0])
                    conn.execute("UPDATE projects SET favorite = ? WHERE id = ?", (new_status, project_id))
                    return new_status
                else:
                    return False
                    
        except Exception as e:
            self.logger.error(f"Failed to toggle favorite for project {project_id}: {e}")
            return False
    
    def cleanup_old_projects(self, days_old: int = 90) -> int:
        """Clean up old unused projects"""
        try:
            cutoff_time = time.time() - (days_old * 24 * 3600)
            
            with sqlite3.connect(self.db_path) as conn:
                # Get projects to delete
                cursor = conn.execute("""
                    SELECT id, file_path FROM projects 
                    WHERE modified_at < ? AND favorite = 0
                """, (cutoff_time,))
                
                projects_to_delete = cursor.fetchall()
                deleted_count = 0
                
                for project_id, file_path in projects_to_delete:
                    # Delete file
                    try:
                        Path(file_path).unlink(missing_ok=True)
                    except:
                        pass
                    
                    # Delete from database
                    conn.execute("DELETE FROM analysis_history WHERE project_id = ?", (project_id,))
                    conn.execute("DELETE FROM projects WHERE id = ?", (project_id,))
                    deleted_count += 1
                
                self.logger.info(f"Cleaned up {deleted_count} old projects")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old projects: {e}")
            return 0
