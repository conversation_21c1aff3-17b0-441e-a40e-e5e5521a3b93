"""
Button Sizing Utilities for AI Analysis Program
Provides consistent button sizing across the application
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Optional


class ButtonSizeManager:
    """Manages consistent button sizing across the application"""
    
    def __init__(self):
        self.default_padding = 2
        self.icon_width = 3
        self.short_text_width = 10
        self.medium_text_width = 15
        self.long_text_width = 20
    
    def calculate_button_width(self, text: str, include_padding: bool = True) -> int:
        """Calculate appropriate width for button text"""
        if not text:
            return self.icon_width
            
        # Count visible characters (excluding emoji which are wider)
        emoji_count = sum(1 for char in text if ord(char) > 127)
        text_length = len(text)
        
        # Emoji characters take up more space
        effective_length = text_length + (emoji_count * 0.5)
        
        if include_padding:
            return max(self.icon_width, int(effective_length + self.default_padding))
        else:
            return max(self.icon_width, int(effective_length))
    
    def create_adaptive_button(self, parent, text: str, command=None, **kwargs) -> ttk.Button:
        """Create a button with automatically calculated width"""
        width = self.calculate_button_width(text)
        
        # Remove width from kwargs if it exists
        kwargs.pop('width', None)
        
        button_kwargs = {
            'text': text,
            'width': width,
            **kwargs
        }
        
        if command is not None:
            button_kwargs['command'] = command
        
        return ttk.Button(parent, **button_kwargs)
    
    def update_button_text(self, button: ttk.Button, new_text: str):
        """Update button text and adjust width accordingly"""
        width = self.calculate_button_width(new_text)
        try:
            button.configure(text=new_text, width=width)
        except tk.TclError:
            # Fallback if button is destroyed or invalid
            pass
    
    def get_preset_widths(self) -> Dict[str, int]:
        """Get preset widths for common button types"""
        return {
            'icon_only': self.icon_width,
            'short': self.short_text_width,
            'medium': self.medium_text_width,
            'long': self.long_text_width
        }


# Global instance for use across the application
button_size_manager = ButtonSizeManager()


def create_responsive_button(parent, text: str, command=None, **kwargs) -> ttk.Button:
    """
    Convenience function to create a button with responsive sizing
    
    Args:
        parent: Parent widget
        text: Button text
        command: Button command callback
        **kwargs: Additional button options
    
    Returns:
        ttk.Button with appropriate width
    """
    return button_size_manager.create_adaptive_button(parent, text, command, **kwargs)


def update_button_responsive(button: ttk.Button, new_text: str):
    """
    Convenience function to update button text with responsive sizing
    
    Args:
        button: Button to update
        new_text: New text for the button
    """
    button_size_manager.update_button_text(button, new_text)


class ResponsiveButtonGroup:
    """Manages a group of buttons with consistent responsive behavior"""
    
    def __init__(self, parent, orientation: str = 'horizontal'):
        self.parent = parent
        self.orientation = orientation
        self.buttons = {}
        self.container = ttk.Frame(parent)
        
        if orientation == 'horizontal':
            self.container.pack(side=tk.LEFT, fill=tk.X)
        else:
            self.container.pack(side=tk.TOP, fill=tk.Y)
    
    def add_button(self, name: str, text: str, command=None, **kwargs) -> ttk.Button:
        """Add a button to the group"""
        button = create_responsive_button(self.container, text, command, **kwargs)
        
        if self.orientation == 'horizontal':
            button.pack(side=tk.LEFT, padx=(2 if self.buttons else 0, 0))
        else:
            button.pack(side=tk.TOP, pady=(2 if self.buttons else 0, 0))
        
        self.buttons[name] = button
        return button
    
    def update_button(self, name: str, text: str):
        """Update a button's text in the group"""
        if name in self.buttons:
            update_button_responsive(self.buttons[name], text)
    
    def get_button(self, name: str) -> Optional[ttk.Button]:
        """Get a button from the group"""
        return self.buttons.get(name)
    
    def remove_button(self, name: str):
        """Remove a button from the group"""
        if name in self.buttons:
            self.buttons[name].destroy()
            del self.buttons[name]
    
    def get_container(self) -> ttk.Frame:
        """Get the container frame"""
        return self.container


def fix_all_button_widths(root_widget):
    """
    Recursively fix all button widths in a widget hierarchy
    
    Args:
        root_widget: Root widget to start the search from
    """
    def fix_widget(widget):
        # Check if this is a button with text
        if isinstance(widget, ttk.Button):
            try:
                current_text = widget.cget('text')
                if current_text:
                    width = button_size_manager.calculate_button_width(current_text)
                    widget.configure(width=width)
            except tk.TclError:
                pass
        
        # Recursively process children
        try:
            for child in widget.winfo_children():
                fix_widget(child)
        except tk.TclError:
            pass
    
    fix_widget(root_widget)


# Preset button configurations for common scenarios
BUTTON_PRESETS = {
    'toolbar_icons': {
        'new': "🆕",
        'open': "📂", 
        'save': "💾",
        'start': "▶️",
        'stop': "⏹️",
        'settings': "⚙️",
        'help': "❓"
    },
    'toolbar_short': {
        'new': "🆕 New",
        'open': "📂 Open",
        'save': "💾 Save", 
        'start': "▶️ Start",
        'stop': "⏹️ Stop",
        'settings': "⚙️ Settings",
        'help': "❓ Help"
    },
    'toolbar_full': {
        'new': "🆕 New Analysis",
        'open': "📂 Open Project",
        'save': "💾 Save Project",
        'start': "▶️ Start Analysis",
        'stop': "⏹️ Stop Analysis",
        'settings': "⚙️ Settings",
        'help': "❓ Help"
    },
    'analysis_presets': {
        'quick': "🚀 Quick",
        'balanced': "🎯 Balanced", 
        'deep': "🔬 Deep"
    },
    'export_buttons': {
        'markdown': "📄 Markdown",
        'json': "📊 JSON",
        'clear': "🗑️ Clear",
        'connections': "🔗 Connections"
    }
}
