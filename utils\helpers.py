"""
Helper utilities for AI Analysis Program
"""

import re
import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import tkinter as tk
from tkinter import messagebox

def sanitize_filename(filename: str) -> str:
    """
    Sanitize a string to be safe for use as a filename
    
    Args:
        filename: Original filename string
        
    Returns:
        Sanitized filename string
    """
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove control characters
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    # Limit length
    if len(filename) > 200:
        filename = filename[:200]
    # Ensure it's not empty
    if not filename.strip():
        filename = "untitled"
    return filename.strip()

def generate_hash(text: str) -> str:
    """
    Generate a hash for the given text
    
    Args:
        text: Input text to hash
        
    Returns:
        SHA-256 hash string
    """
    return hashlib.sha256(text.encode('utf-8')).hexdigest()

def format_timestamp(dt: Optional[datetime] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format a datetime object as a string
    
    Args:
        dt: Datetime object (uses current time if None)
        format_str: Format string for datetime
        
    Returns:
        Formatted datetime string
    """
    if dt is None:
        dt = datetime.now()
    return dt.strftime(format_str)

def safe_json_load(file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
    """
    Safely load JSON from a file
    
    Args:
        file_path: Path to JSON file
        
    Returns:
        Loaded JSON data or None if failed
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return None

def safe_json_save(data: Dict[str, Any], file_path: Union[str, Path]) -> bool:
    """
    Safely save data to JSON file
    
    Args:
        data: Data to save
        file_path: Path to save file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception:
        return False

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Truncate text to specified length
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add when truncated
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def extract_keywords(text: str, min_length: int = 3) -> List[str]:
    """
    Extract keywords from text
    
    Args:
        text: Input text
        min_length: Minimum keyword length
        
    Returns:
        List of keywords
    """
    # Simple keyword extraction - can be enhanced with NLP libraries
    words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
    keywords = [word for word in words if len(word) >= min_length]
    # Remove common stop words
    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
    keywords = [word for word in keywords if word not in stop_words]
    return list(set(keywords))  # Remove duplicates

def calculate_similarity(text1: str, text2: str) -> float:
    """
    Calculate simple similarity between two texts
    
    Args:
        text1: First text
        text2: Second text
        
    Returns:
        Similarity score (0.0 to 1.0)
    """
    keywords1 = set(extract_keywords(text1))
    keywords2 = set(extract_keywords(text2))
    
    if not keywords1 and not keywords2:
        return 1.0
    if not keywords1 or not keywords2:
        return 0.0
    
    intersection = keywords1.intersection(keywords2)
    union = keywords1.union(keywords2)
    
    return len(intersection) / len(union) if union else 0.0

def show_error(title: str, message: str, parent: Optional[tk.Widget] = None) -> None:
    """
    Show error message dialog
    
    Args:
        title: Dialog title
        message: Error message
        parent: Parent widget
    """
    messagebox.showerror(title, message, parent=parent)

def show_info(title: str, message: str, parent: Optional[tk.Widget] = None) -> None:
    """
    Show info message dialog
    
    Args:
        title: Dialog title
        message: Info message
        parent: Parent widget
    """
    messagebox.showinfo(title, message, parent=parent)

def show_warning(title: str, message: str, parent: Optional[tk.Widget] = None) -> None:
    """
    Show warning message dialog
    
    Args:
        title: Dialog title
        message: Warning message
        parent: Parent widget
    """
    messagebox.showwarning(title, message, parent=parent)

def confirm_action(title: str, message: str, parent: Optional[tk.Widget] = None) -> bool:
    """
    Show confirmation dialog
    
    Args:
        title: Dialog title
        message: Confirmation message
        parent: Parent widget
        
    Returns:
        True if confirmed, False otherwise
    """
    return messagebox.askyesno(title, message, parent=parent)

def validate_topic_structure(topic_data: Dict[str, Any]) -> bool:
    """
    Validate topic data structure
    
    Args:
        topic_data: Topic data to validate
        
    Returns:
        True if valid, False otherwise
    """
    required_fields = ['title', 'description']
    
    for field in required_fields:
        if field not in topic_data or not topic_data[field]:
            return False
    
    # Validate sub-topics if present
    if 'sub_topics' in topic_data:
        if not isinstance(topic_data['sub_topics'], list):
            return False
        for sub_topic in topic_data['sub_topics']:
            if not validate_topic_structure(sub_topic):
                return False
    
    return True

def create_backup_filename(original_path: Union[str, Path]) -> Path:
    """
    Create a backup filename for the given path
    
    Args:
        original_path: Original file path
        
    Returns:
        Backup file path
    """
    path = Path(original_path)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{path.stem}_backup_{timestamp}{path.suffix}"
    return path.parent / backup_name
