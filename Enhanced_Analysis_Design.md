# Enhanced Analysis Logic Design

## Overview

This document outlines improvements to the analysis system to better utilize main topic context when analyzing sub-topics and improve topic relationship discovery.

## Current Analysis Logic Issues

### 1. Limited Context Integration
- **Problem**: Context is simply appended as additional text
- **Impact**: AI doesn't understand the hierarchical relationship
- **Example**: Analyzing "Machine Learning" as sub-topic of "AI" treats context as afterthought

### 2. Independent Sub-topic Analysis
- **Problem**: Each sub-topic analyzed in isolation
- **Impact**: Misses opportunities for coherent narrative
- **Example**: Sub-topics may contradict or overlap without awareness

### 3. Post-hoc Connection Finding
- **Problem**: Connections found after analysis completion
- **Impact**: Connections feel artificial and disconnected from analysis
- **Example**: Analysis doesn't inform connection discovery

### 4. Generic Prompting Strategy
- **Problem**: Same prompt templates regardless of topic hierarchy
- **Impact**: Doesn't leverage structural relationships
- **Example**: No differentiation between main topic and sub-topic analysis

## Enhanced Analysis Logic Design

### 1. Hierarchical Context Framework

#### Context Levels
```python
class ContextLevel:
    ROOT = "root"           # Main topic
    BRANCH = "branch"       # Primary sub-topic
    LEAF = "leaf"          # Secondary sub-topic
    CROSS_REF = "cross_ref" # Cross-referenced topic
```

#### Context Structure
```python
@dataclass
class AnalysisContext:
    main_topic: str
    topic_hierarchy: Dict[str, List[str]]
    analysis_goals: List[str]
    domain_knowledge: Dict[str, Any]
    previous_analyses: List[Dict[str, Any]]
    relationship_map: Dict[str, List[str]]
```

### 2. Context-Aware Prompting System

#### Prompt Templates by Context Level

**Root Topic Analysis**
```
You are analyzing the main topic: "{main_topic}"

Context:
- This is the central concept that will guide all subsequent analysis
- Sub-topics to be analyzed: {sub_topics}
- Analysis goals: {goals}

Please provide:
1. Comprehensive overview of the topic
2. Key dimensions for sub-topic analysis
3. Framework for understanding relationships
4. Success criteria for complete analysis

Focus on establishing the foundation for coherent sub-topic analysis.
```

**Branch Topic Analysis**
```
You are analyzing the sub-topic: "{sub_topic}"

Main Topic Context: "{main_topic}"
- Main topic analysis: {main_analysis_summary}
- Analysis framework: {analysis_framework}
- Related sub-topics: {related_subtopics}

Please analyze "{sub_topic}" specifically in relation to "{main_topic}":
1. How does this sub-topic contribute to understanding the main topic?
2. What unique perspective does it provide?
3. How does it relate to other sub-topics: {related_subtopics}?
4. What implications does this have for the overall analysis?

Maintain coherence with the main topic while providing specific insights.
```

**Cross-Reference Analysis**
```
You are analyzing connections between topics within the context of "{main_topic}":

Topics to connect: {topics}
Individual analyses: {topic_analyses}
Main topic framework: {framework}

Please identify:
1. Direct relationships between topics
2. Indirect connections through the main topic
3. Synergies and conflicts
4. Emergent patterns
5. Implications for the main topic understanding

Focus on meaningful connections that enhance overall comprehension.
```