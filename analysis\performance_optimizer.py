"""
Performance optimization utilities for AI Analysis Program
"""

import asyncio
import concurrent.futures
import hashlib
import sqlite3
import time
import threading
import psutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
import json
import pickle

from utils.logging_setup import LoggerMixin
from utils.config import Config

class PerformanceMonitor(LoggerMixin):
    """Monitor and track performance metrics"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
        
    def measure_time(self, operation_name: str):
        """Decorator to measure operation time"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    success = True
                except Exception as e:
                    result = None
                    success = False
                    raise
                finally:
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    self.metrics[operation_name] = {
                        'duration': duration,
                        'timestamp': start_time,
                        'success': success,
                        'memory_mb': psutil.Process().memory_info().rss / 1024 / 1024
                    }
                    
                    self.logger.debug(f"{operation_name} took {duration:.3f}s")
                
                return result
            return wrapper
        return decorator
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of performance metrics"""
        if not self.metrics:
            return {}
        
        total_operations = len(self.metrics)
        successful_operations = sum(1 for m in self.metrics.values() if m.get('success', True))
        total_time = sum(m['duration'] for m in self.metrics.values())
        avg_time = total_time / total_operations if total_operations > 0 else 0
        
        return {
            'total_operations': total_operations,
            'successful_operations': successful_operations,
            'success_rate': successful_operations / total_operations if total_operations > 0 else 0,
            'total_time': total_time,
            'average_time': avg_time,
            'uptime': time.time() - self.start_time
        }

class AnalysisCache(LoggerMixin):
    """Intelligent caching system for analysis results"""
    
    def __init__(self, config: Config):
        self.config = config
        self.cache_dir = Path("data/cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.db_path = self.cache_dir / "analysis_cache.db"
        self.ttl_hours = config.get("performance.cache_ttl_hours", 24)
        self.init_database()
    
    def init_database(self):
        """Initialize cache database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache (
                    key TEXT PRIMARY KEY,
                    result TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    expires_at REAL NOT NULL,
                    size_bytes INTEGER NOT NULL
                )
            """)
            conn.execute("CREATE INDEX IF NOT EXISTS idx_expires ON cache(expires_at)")
    
    def get_cache_key(self, prompt: str, model: str, params: Dict[str, Any]) -> str:
        """Generate cache key for analysis request"""
        content = {
            'prompt': prompt,
            'model': model,
            'params': sorted(params.items()) if params else []
        }
        content_str = json.dumps(content, sort_keys=True)
        return hashlib.sha256(content_str.encode()).hexdigest()
    
    def get_cached_result(self, cache_key: str) -> Optional[str]:
        """Retrieve cached analysis result"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT result FROM cache WHERE key = ? AND expires_at > ?",
                    (cache_key, time.time())
                )
                row = cursor.fetchone()
                if row:
                    self.logger.debug(f"Cache hit for key: {cache_key[:8]}...")
                    return row[0]
                else:
                    self.logger.debug(f"Cache miss for key: {cache_key[:8]}...")
                    return None
        except Exception as e:
            self.logger.error(f"Cache retrieval error: {e}")
            return None
    
    def store_result(self, cache_key: str, result: str):
        """Store analysis result in cache"""
        try:
            now = time.time()
            expires_at = now + (self.ttl_hours * 3600)
            size_bytes = len(result.encode('utf-8'))
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    "INSERT OR REPLACE INTO cache (key, result, created_at, expires_at, size_bytes) VALUES (?, ?, ?, ?, ?)",
                    (cache_key, result, now, expires_at, size_bytes)
                )
            
            self.logger.debug(f"Cached result for key: {cache_key[:8]}... ({size_bytes} bytes)")
            
        except Exception as e:
            self.logger.error(f"Cache storage error: {e}")
    
    def cleanup_expired(self):
        """Remove expired cache entries"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("DELETE FROM cache WHERE expires_at < ?", (time.time(),))
                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    self.logger.info(f"Cleaned up {deleted_count} expired cache entries")
        except Exception as e:
            self.logger.error(f"Cache cleanup error: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*), SUM(size_bytes) FROM cache WHERE expires_at > ?", (time.time(),))
                count, total_size = cursor.fetchone()
                
                cursor = conn.execute("SELECT COUNT(*) FROM cache WHERE expires_at <= ?", (time.time(),))
                expired_count = cursor.fetchone()[0]
                
                return {
                    'active_entries': count or 0,
                    'expired_entries': expired_count or 0,
                    'total_size_mb': (total_size or 0) / 1024 / 1024,
                    'cache_file_size_mb': self.db_path.stat().st_size / 1024 / 1024 if self.db_path.exists() else 0
                }
        except Exception as e:
            self.logger.error(f"Cache stats error: {e}")
            return {}

class AsyncAnalysisEngine(LoggerMixin):
    """Asynchronous analysis engine for parallel processing"""
    
    def __init__(self, config: Config, ollama_client):
        self.config = config
        self.ollama_client = ollama_client
        self.max_workers = config.get("performance.max_concurrent_analyses", 4)
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        self.cache = AnalysisCache(config)
        self.monitor = PerformanceMonitor()
    
    @PerformanceMonitor().measure_time("async_topic_analysis")
    async def analyze_topics_parallel(
        self, 
        topics: List[str], 
        context: str = "",
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """Analyze multiple topics in parallel"""
        
        # Create analysis tasks
        tasks = []
        for topic in topics:
            task = asyncio.create_task(self.analyze_single_topic_async(topic, context))
            tasks.append(task)
        
        # Process tasks as they complete
        results = []
        completed = 0
        
        for task in asyncio.as_completed(tasks):
            try:
                result = await task
                results.append(result)
                completed += 1
                
                if progress_callback:
                    progress_callback(completed, len(topics))
                    
            except Exception as e:
                self.logger.error(f"Task failed: {e}")
                results.append({"error": str(e)})
                completed += 1
                
                if progress_callback:
                    progress_callback(completed, len(topics))
        
        return results
    
    async def analyze_single_topic_async(self, topic: str, context: str = "") -> Dict[str, Any]:
        """Analyze a single topic asynchronously"""
        
        # Check cache first
        cache_key = self.cache.get_cache_key(
            prompt=f"{topic} {context}",
            model=self.ollama_client.default_model,
            params={"temperature": 0.7}
        )
        
        cached_result = self.cache.get_cached_result(cache_key)
        if cached_result:
            return {
                "topic": topic,
                "analysis": cached_result,
                "cached": True
            }
        
        # Run analysis in thread pool
        loop = asyncio.get_event_loop()
        
        try:
            analysis = await loop.run_in_executor(
                self.executor,
                self.ollama_client.analyze_topic,
                topic,
                context,
                "detailed"
            )
            
            if analysis:
                # Store in cache
                self.cache.store_result(cache_key, analysis)
                
                return {
                    "topic": topic,
                    "analysis": analysis,
                    "cached": False
                }
            else:
                return {
                    "topic": topic,
                    "analysis": "Analysis failed",
                    "error": True
                }
                
        except Exception as e:
            self.logger.error(f"Analysis failed for topic '{topic}': {e}")
            return {
                "topic": topic,
                "analysis": f"Error: {str(e)}",
                "error": True
            }
    
    def cleanup(self):
        """Cleanup resources"""
        if self.executor:
            self.executor.shutdown(wait=True)
        self.cache.cleanup_expired()

class UIOptimizer(LoggerMixin):
    """UI performance optimization utilities"""
    
    def __init__(self, root_widget):
        self.root = root_widget
        self.update_queue = []
        self.update_scheduled = False
        
    def debounced_update(self, callback: Callable, delay_ms: int = 100):
        """Debounce UI updates to prevent excessive redraws"""
        
        def schedule_update():
            self.update_queue.append(callback)
            
            if not self.update_scheduled:
                self.update_scheduled = True
                self.root.after(delay_ms, self.process_updates)
        
        return schedule_update
    
    def process_updates(self):
        """Process queued UI updates"""
        try:
            while self.update_queue:
                callback = self.update_queue.pop(0)
                callback()
        except Exception as e:
            self.logger.error(f"UI update error: {e}")
        finally:
            self.update_scheduled = False
    
    def virtual_scroll_setup(self, listbox, items: List[Any], item_height: int = 20):
        """Set up virtual scrolling for large lists"""
        visible_items = min(len(items), 20)  # Show max 20 items at once
        
        def update_visible_items():
            # This would implement virtual scrolling logic
            # For now, just limit the number of items shown
            start_index = 0  # Would be calculated based on scroll position
            end_index = min(start_index + visible_items, len(items))
            
            listbox.delete(0, 'end')
            for i in range(start_index, end_index):
                listbox.insert('end', str(items[i]))
        
        return update_visible_items

class MemoryManager(LoggerMixin):
    """Memory usage optimization and monitoring"""
    
    def __init__(self, config: Config):
        self.config = config
        self.max_memory_mb = config.get("performance.max_memory_mb", 512)
        self.cleanup_threshold = 0.8  # Cleanup when 80% of max memory is used
        
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
    
    def should_cleanup(self) -> bool:
        """Check if memory cleanup is needed"""
        memory_usage = self.get_memory_usage()
        return memory_usage['rss_mb'] > (self.max_memory_mb * self.cleanup_threshold)
    
    def cleanup_large_objects(self, objects_dict: Dict[str, Any]):
        """Clean up large objects from memory"""
        if not self.should_cleanup():
            return
        
        # Remove large cached objects
        items_to_remove = []
        for key, obj in objects_dict.items():
            try:
                # Estimate object size (rough approximation)
                obj_size = len(str(obj)) if obj else 0
                if obj_size > 1024 * 1024:  # > 1MB
                    items_to_remove.append(key)
            except:
                continue
        
        for key in items_to_remove:
            del objects_dict[key]
            self.logger.debug(f"Cleaned up large object: {key}")
        
        # Force garbage collection
        import gc
        gc.collect()
        
        self.logger.info(f"Memory cleanup completed. Removed {len(items_to_remove)} large objects.")

# Global performance monitor instance
performance_monitor = PerformanceMonitor()
