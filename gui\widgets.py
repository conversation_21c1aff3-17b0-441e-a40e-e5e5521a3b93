"""
Custom widgets for AI Analysis Program
Provides enhanced UI components with modern functionality
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional, Any

class CollapsibleFrame(ttk.Frame):
    """A collapsible frame widget that can be expanded/collapsed"""

    def __init__(self, parent, title: str = "", collapsed: bool = False, theme=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.theme = theme
        self.collapsed = collapsed
        self.title = title

        # Create header frame
        self.header_frame = ttk.Frame(self)
        self.header_frame.pack(fill=tk.X, padx=2, pady=2)

        # Toggle button
        self.toggle_var = tk.StringVar()
        self.update_toggle_text()

        self.toggle_button = ttk.Button(
            self.header_frame,
            textvariable=self.toggle_var,
            command=self.toggle,
            width=3
        )
        self.toggle_button.pack(side=tk.LEFT)

        # Title label
        self.title_label = ttk.Label(
            self.header_frame,
            text=self.title,
            font=self.theme.get_font("medium", "bold") if self.theme else ("Arial", 11, "bold")
        )
        self.title_label.pack(side=tk.LEFT, padx=(8, 0))

        # Content frame
        self.content_frame = ttk.Frame(self)
        if not self.collapsed:
            self.content_frame.pack(fill=tk.BOTH, expand=True, padx=4, pady=4)

    def update_toggle_text(self):
        """Update the toggle button text"""
        self.toggle_var.set("▼" if not self.collapsed else "▶")

    def toggle(self):
        """Toggle the collapsed state"""
        self.collapsed = not self.collapsed
        self.update_toggle_text()

        if self.collapsed:
            self.content_frame.pack_forget()
        else:
            self.content_frame.pack(fill=tk.BOTH, expand=True, padx=4, pady=4)

    def expand(self):
        """Expand the frame"""
        if self.collapsed:
            self.toggle()

    def collapse(self):
        """Collapse the frame"""
        if not self.collapsed:
            self.toggle()

class ToolTip:
    """Tooltip widget for providing contextual help"""

    def __init__(self, widget, text: str, delay: int = 500):
        self.widget = widget
        self.text = text
        self.delay = delay
        self.tooltip_window = None
        self.after_id = None

        # Bind events
        self.widget.bind("<Enter>", self.on_enter)
        self.widget.bind("<Leave>", self.on_leave)
        self.widget.bind("<Motion>", self.on_motion)

    def on_enter(self, event=None):
        """Handle mouse enter"""
        self.schedule_tooltip()

    def on_leave(self, event=None):
        """Handle mouse leave"""
        self.cancel_tooltip()
        self.hide_tooltip()

    def on_motion(self, event=None):
        """Handle mouse motion"""
        self.cancel_tooltip()
        self.schedule_tooltip()

    def schedule_tooltip(self):
        """Schedule tooltip to appear"""
        self.cancel_tooltip()
        self.after_id = self.widget.after(self.delay, self.show_tooltip)

    def cancel_tooltip(self):
        """Cancel scheduled tooltip"""
        if self.after_id:
            self.widget.after_cancel(self.after_id)
            self.after_id = None

    def show_tooltip(self):
        """Show the tooltip"""
        if self.tooltip_window:
            return

        x = self.widget.winfo_rootx() + 25
        y = self.widget.winfo_rooty() + 25

        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.wm_geometry(f"+{x}+{y}")

        label = tk.Label(
            self.tooltip_window,
            text=self.text,
            background="#FFFFDD",
            foreground="#000000",
            relief=tk.SOLID,
            borderwidth=1,
            font=("Arial", 9),
            wraplength=300
        )
        label.pack()

    def hide_tooltip(self):
        """Hide the tooltip"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

class StatusIndicator(ttk.Frame):
    """Status indicator widget with icon and text"""

    def __init__(self, parent, theme=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.theme = theme

        # Icon label
        self.icon_label = ttk.Label(self, text="●")
        self.icon_label.pack(side=tk.LEFT)

        # Status text
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(self, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=(4, 0))

        # Set initial status
        self.set_status("Ready", "gray")

    def set_status(self, text: str, status_type: str = "info"):
        """Set the status text and color"""
        self.status_var.set(text)

        # Map status types to colors and icons
        status_map = {
            "success": ("✓", "green"),
            "error": ("✗", "red"),
            "warning": ("⚠", "orange"),
            "info": ("ℹ", "blue"),
            "loading": ("⟳", "blue"),
            "ready": ("●", "gray")
        }

        icon, color = status_map.get(status_type, ("●", "gray"))
        self.icon_label.configure(text=icon, foreground=color)

class ScrollableFrame(ttk.Frame):
    """A scrollable frame that can be used for any panel."""
    def __init__(self, parent, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.canvas = tk.Canvas(self, borderwidth=0, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Mousewheel support
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def get_frame(self):
        return self.scrollable_frame