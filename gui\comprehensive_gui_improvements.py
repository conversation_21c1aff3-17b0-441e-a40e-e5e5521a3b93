"""
Comprehensive GUI Improvements Integration
Combines Phase 1 and Phase 2 improvements for complete GUI enhancement
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Union
from utils.logging_setup import LoggerMixin

# Import Phase 1 components
from gui.phase1_integration_fixed import Phase1IntegrationManager
from gui.phase2_advanced_features import Phase2AdvancedFeatures, TabbledResultsDisplay, MultiColumnTextWidget

# Import existing GUI components for integration
try:
    from gui.main_window import MainWindow
    MAIN_WINDOW_AVAILABLE = True
except ImportError:
    MAIN_WINDOW_AVAILABLE = False


class ComprehensiveGUIManager(LoggerMixin):
    """Manages comprehensive GUI improvements combining all phases."""
    
    def __init__(self, main_window: tk.Tk):
        super().__init__()
        self.main_window = main_window
        
        # Initialize phase managers
        self.phase1_manager = Phase1IntegrationManager(main_window)
        self.phase2_manager = Phase2AdvancedFeatures(main_window)
        
        # Integration state
        self.enhanced_panels = {}
        self.original_widgets = {}
        self.improvement_status = {
            'phase1_enabled': False,
            'phase2_enabled': False,
            'comprehensive_mode': False
        }
        
        self.logger.info("Comprehensive GUI Manager initialized")
        
    def apply_all_improvements(self) -> bool:
        """Apply all GUI improvements from Phase 1 and Phase 2."""
        try:
            self.logger.info("Starting comprehensive GUI improvements...")
            
            # Apply Phase 1 improvements
            if self.phase1_manager.integrate_all_improvements():
                self.improvement_status['phase1_enabled'] = True
                self.logger.info("Phase 1 improvements applied successfully")
            else:
                self.logger.warning("Phase 1 improvements failed")
                
            # Apply Phase 2 improvements
            if self.apply_phase2_improvements():
                self.improvement_status['phase2_enabled'] = True
                self.logger.info("Phase 2 improvements applied successfully")
            else:
                self.logger.warning("Phase 2 improvements failed")
                
            # Enable comprehensive mode if both phases succeeded
            if (self.improvement_status['phase1_enabled'] and 
                self.improvement_status['phase2_enabled']):
                self.improvement_status['comprehensive_mode'] = True
                self.apply_comprehensive_enhancements()
                
            self.logger.info("Comprehensive GUI improvements completed")
            return self.improvement_status['comprehensive_mode']
            
        except Exception as e:
            self.logger.error(f"Comprehensive GUI improvements failed: {e}")
            return False
            
    def apply_phase2_improvements(self) -> bool:
        """Apply Phase 2 advanced features."""
        try:
            # Find results panels that can be enhanced
            results_panels = self.find_results_panels()
            
            for panel_id, panel_widget in results_panels.items():
                self.enhance_results_panel(panel_id, panel_widget)
                
            return True
            
        except Exception as e:
            self.logger.error(f"Phase 2 improvements failed: {e}")
            return False
            
    def find_results_panels(self) -> Dict[str, tk.Widget]:
        """Find panels that can benefit from Phase 2 results enhancements."""
        results_panels = {}
        
        def find_results_widgets(widget, path=""):
            try:
                # Look for text widgets that might be results displays
                if isinstance(widget, tk.Text):
                    if 'result' in str(widget).lower() or 'analysis' in str(widget).lower():
                        panel_id = f"results_{len(results_panels)}"
                        results_panels[panel_id] = widget
                        
                # Look for frames that might contain results
                elif isinstance(widget, (tk.Frame, ttk.Frame)):
                    children_text_widgets = []
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Text):
                            children_text_widgets.append(child)
                            
                    if len(children_text_widgets) > 0:
                        panel_id = f"results_frame_{len(results_panels)}"
                        results_panels[panel_id] = widget
                        
                # Continue traversing
                for child in widget.winfo_children():
                    find_results_widgets(child, f"{path}/{child.__class__.__name__}")
                    
            except Exception:
                pass
                
        find_results_widgets(self.main_window)
        return results_panels
        
    def enhance_results_panel(self, panel_id: str, panel_widget: tk.Widget):
        """Enhance a specific results panel with Phase 2 features."""
        try:
            # Store original widget for potential rollback
            self.original_widgets[panel_id] = panel_widget
            
            # Get parent widget
            parent = panel_widget.master
            
            # Create enhanced results display
            enhanced_display = self.phase2_manager.create_advanced_results_panel(parent)
            
            # Copy existing content if it's a text widget
            if isinstance(panel_widget, tk.Text):
                try:
                    content = panel_widget.get('1.0', tk.END)
                    if content.strip():
                        enhanced_display.set_tab_content('summary', content)
                except Exception:
                    pass
                    
            # Replace the widget in layout
            pack_info = panel_widget.pack_info()
            grid_info = panel_widget.grid_info()
            
            # Remove original widget
            panel_widget.pack_forget()
            panel_widget.grid_forget()
            
            # Place enhanced widget
            if pack_info:
                enhanced_display.pack(**pack_info)
            elif grid_info:
                enhanced_display.grid(**grid_info)
            else:
                enhanced_display.pack(fill='both', expand=True)
                
            # Store enhanced widget
            self.enhanced_panels[panel_id] = enhanced_display
            
            self.logger.info(f"Enhanced results panel: {panel_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to enhance panel {panel_id}: {e}")
            
    def apply_comprehensive_enhancements(self):
        """Apply additional enhancements when both phases are active."""
        try:
            # Connect Phase 1 content management with Phase 2 displays
            self.integrate_content_management()
            
            # Apply unified theme
            self.apply_unified_theme()
            
            # Setup comprehensive navigation
            self.setup_comprehensive_navigation()
            
            self.logger.info("Comprehensive enhancements applied")
            
        except Exception as e:
            self.logger.error(f"Comprehensive enhancements failed: {e}")
            
    def integrate_content_management(self):
        """Integrate Phase 1 content management with Phase 2 displays."""
        content_manager = self.phase1_manager.get_content_manager()
        
        for panel_id, enhanced_display in self.enhanced_panels.items():
            if isinstance(enhanced_display, TabbledResultsDisplay):
                # Register enhanced display with content manager
                content_manager.register_display_panel(f"enhanced_{panel_id}", enhanced_display)
                
    def apply_unified_theme(self):
        """Apply unified theming across all improvements."""
        theme_config = {
            'bg_color': '#f8f9fa',
            'text_color': '#212529',
            'accent_color': '#007bff',
            'border_color': '#dee2e6',
            'font_family': 'Segoe UI',
            'font_size': 9
        }
        
        # Apply to Phase 1 components (skip if method not available)
        if hasattr(self.phase1_manager, 'apply_theme_integration'):
            self.phase1_manager.apply_theme_integration(theme_config)
        
        # Apply to enhanced panels
        for enhanced_display in self.enhanced_panels.values():
            if hasattr(enhanced_display, 'configure'):
                try:
                    enhanced_display.configure(bg=theme_config['bg_color'])
                except Exception:
                    pass
                    
    def setup_comprehensive_navigation(self):
        """Setup navigation that works across all enhanced components."""
        # Create global navigation shortcuts
        self.main_window.bind('<Control-1>', lambda e: self.focus_panel('topic_input'))
        self.main_window.bind('<Control-2>', lambda e: self.focus_panel('analysis'))
        self.main_window.bind('<Control-3>', lambda e: self.focus_panel('results'))
        self.main_window.bind('<Control-r>', lambda e: self.refresh_all_layouts())
        
    def focus_panel(self, panel_type: str):
        """Focus on a specific type of panel."""
        try:
            if panel_type == 'results' and self.enhanced_panels:
                # Focus on first enhanced results panel
                first_panel = list(self.enhanced_panels.values())[0]
                first_panel.focus_set()
                
        except Exception as e:
            self.logger.error(f"Failed to focus panel {panel_type}: {e}")
            
    def refresh_all_layouts(self):
        """Refresh all layouts and optimizations."""
        try:
            # Refresh Phase 1 layouts
            self.phase1_manager.refresh_all_layouts()
            
            # Update Phase 2 components
            for enhanced_display in self.enhanced_panels.values():
                if hasattr(enhanced_display, 'update'):
                    enhanced_display.update()
                    
            self.logger.info("All layouts refreshed")
            
        except Exception as e:
            self.logger.error(f"Failed to refresh layouts: {e}")
            
    def add_analysis_result(self, content: str, result_type: str = 'analysis', 
                          priority: str = 'normal'):
        """Add analysis result using the best available method."""
        try:
            # Try to add to enhanced results display first
            if self.enhanced_panels:
                for panel_id, enhanced_display in self.enhanced_panels.items():
                    if isinstance(enhanced_display, TabbledResultsDisplay):
                        # Determine best tab for this content type
                        tab_mapping = {
                            'summary': 'summary',
                            'analysis': 'details',
                            'connections': 'connections',
                            'mindmap': 'mindmap',
                            'raw': 'raw_data'
                        }
                        
                        target_tab = tab_mapping.get(result_type, 'details')
                        enhanced_display.set_tab_content(target_tab, content)
                        
                        self.logger.info(f"Added result to enhanced display: {result_type}")
                        return True
                        
            # Fallback to Phase 1 content management
            content_manager = self.phase1_manager.get_content_manager()
            
            # Try to find a suitable panel
            if hasattr(content_manager, 'integrated_panels'):
                for panel_id in content_manager.integrated_panels.keys():
                    if 'result' in panel_id.lower() or 'analysis' in panel_id.lower():
                        return content_manager.add_content(panel_id, content, priority, result_type)
                        
                # Last resort: add to any available panel
                available_panels = list(content_manager.integrated_panels.keys())
                if available_panels:
                    return content_manager.add_content(available_panels[0], content, priority, result_type)
                
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to add analysis result: {e}")
            return False
            
    def create_enhanced_text_input(self, parent: tk.Widget, **kwargs) -> Union[MultiColumnTextWidget, tk.Text]:
        """Create enhanced text input with all improvements."""
        try:
            # Use Phase 2 multi-column widget if available
            enhanced_widget = MultiColumnTextWidget(parent, **kwargs)
            
            # Apply Phase 1 optimizations
            widget_id = f"enhanced_text_{len(self.enhanced_panels)}"
            self.phase1_manager.enhanced_widgets[widget_id] = enhanced_widget
            
            return enhanced_widget
            
        except Exception as e:
            self.logger.error(f"Failed to create enhanced text input: {e}")
            return tk.Text(parent, **kwargs)
            
    def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all improvements."""
        phase1_status = self.phase1_manager.get_integration_status()
        
        return {
            'comprehensive_mode': self.improvement_status['comprehensive_mode'],
            'phase1_enabled': self.improvement_status['phase1_enabled'],
            'phase2_enabled': self.improvement_status['phase2_enabled'],
            'phase1_details': phase1_status,
            'enhanced_panels': len(self.enhanced_panels),
            'original_widgets_preserved': len(self.original_widgets),
            'total_improvements': (
                phase1_status.get('responsive_panels', 0) + 
                phase1_status.get('enhanced_widgets', 0) + 
                len(self.enhanced_panels)
            )
        }
        
    def rollback_to_original(self, panel_id: str = ""):
        """Rollback specific panel or all panels to original state."""
        try:
            if panel_id:
                # Rollback specific panel
                if panel_id in self.original_widgets and panel_id in self.enhanced_panels:
                    enhanced_widget = self.enhanced_panels[panel_id]
                    original_widget = self.original_widgets[panel_id]
                    
                    # Get layout info
                    pack_info = enhanced_widget.pack_info()
                    grid_info = enhanced_widget.grid_info()
                    
                    # Remove enhanced widget
                    enhanced_widget.destroy()
                    
                    # Restore original widget
                    if pack_info:
                        original_widget.pack(**pack_info)
                    elif grid_info:
                        original_widget.grid(**grid_info)
                        
                    # Clean up references
                    del self.enhanced_panels[panel_id]
                    del self.original_widgets[panel_id]
                    
                    self.logger.info(f"Rolled back panel: {panel_id}")
            else:
                # Rollback all panels
                for panel_id in list(self.enhanced_panels.keys()):
                    self.rollback_to_original(panel_id)
                    
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")


def apply_comprehensive_improvements(main_window: tk.Tk) -> ComprehensiveGUIManager:
    """
    Apply all GUI improvements to an existing application.
    
    Args:
        main_window: The main tkinter window
        
    Returns:
        ComprehensiveGUIManager instance for ongoing management
    """
    gui_manager = ComprehensiveGUIManager(main_window)
    
    if gui_manager.apply_all_improvements():
        return gui_manager
    else:
        raise Exception("Failed to apply comprehensive GUI improvements")


def enhance_existing_application(app_instance):
    """Enhance existing application with comprehensive improvements."""
    try:
        # Get main window from app instance
        main_window = None
        
        for attr_name in ['window', 'root', 'main_window', 'master', '_root']:
            if hasattr(app_instance, attr_name):
                potential_window = getattr(app_instance, attr_name)
                if isinstance(potential_window, tk.Tk):
                    main_window = potential_window
                    break
                    
        if main_window is None:
            raise ValueError("Could not find main window in application instance")
            
        # Apply comprehensive improvements
        gui_manager = apply_comprehensive_improvements(main_window)
        
        # Store reference in app instance
        app_instance.comprehensive_gui_manager = gui_manager
        
        # Add helper methods to app instance
        app_instance.add_analysis_result = gui_manager.add_analysis_result
        app_instance.refresh_gui_layouts = gui_manager.refresh_all_layouts
        app_instance.get_gui_status = gui_manager.get_comprehensive_status
        
        return gui_manager
        
    except Exception as e:
        print(f"Failed to enhance existing application: {e}")
        return None


def demo_comprehensive_improvements():
    """Demonstrate comprehensive GUI improvements."""
    root = tk.Tk()
    root.title("Comprehensive GUI Improvements Demo")
    root.geometry("1400x900")
    
    try:
        # Apply comprehensive improvements
        gui_manager = apply_comprehensive_improvements(root)
        
        # Create demo layout
        main_frame = tk.Frame(root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Left panel - input area
        left_panel = gui_manager.phase1_manager.create_responsive_panel(
            main_frame, "input_panel", bg='lightblue', width=300
        )
        left_panel.pack(side='left', fill='both', expand=False, padx=(0, 10))
        
        # Add input widgets
        tk.Label(left_panel, text="Enhanced Input Area", font=('Arial', 12, 'bold')).pack(pady=10)
        
        input_text = gui_manager.create_enhanced_text_input(left_panel, height=10)
        input_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Add sample text
        sample_input = """Enter your analysis topic here.

This enhanced input area features:
• Multi-column layout for wide displays
• Responsive design that adapts to screen size
• Advanced content management
• Integrated navigation and search

Try resizing the window to see the responsive behavior in action!"""
        
        # Add sample text
        if isinstance(input_text, MultiColumnTextWidget):
            input_text.set_content(sample_input)
        else:
            # Fallback for regular text widgets
            input_text.insert('1.0', sample_input)
        
        # Right panel - results area (this will be automatically enhanced)
        right_panel = tk.Frame(main_frame, bg='lightgreen')
        right_panel.pack(side='right', fill='both', expand=True)
        
        # Create a text widget that will be detected and enhanced
        results_text = tk.Text(right_panel, wrap='word')
        results_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Add sample results
        sample_results = """Analysis Results

This area demonstrates the comprehensive GUI improvements including:

Phase 1 Improvements:
✓ Responsive layout system
✓ Advanced content management  
✓ High-performance scrolling
✓ Content overflow handling

Phase 2 Improvements:
✓ Multi-column text layout
✓ Tabbed results display
✓ Smart navigation system
✓ Enhanced search functionality

The system automatically detects results panels and enhances them with advanced features like tabbed interfaces, multi-column layouts, and intelligent content organization.

Try adding analysis results using the demo controls below to see the enhanced display in action!"""
        
        results_text.insert('1.0', sample_results)
        
        # Control panel
        controls_frame = tk.Frame(root, bg='lightgray', height=60)
        controls_frame.pack(fill='x', padx=10, pady=(0, 10))
        controls_frame.pack_propagate(False)
        
        # Demo buttons
        btn_frame = tk.Frame(controls_frame)
        btn_frame.pack(expand=True)
        
        def add_sample_analysis():
            gui_manager.add_analysis_result(
                "This is a sample analysis result demonstrating the enhanced display capabilities.",
                'analysis', 'high'
            )
            
        def add_sample_summary():
            gui_manager.add_analysis_result(
                "Sample summary showing how different content types are handled.",
                'summary', 'normal'
            )
            
        def show_status():
            status = gui_manager.get_comprehensive_status()
            print("\nComprehensive GUI Status:")
            for key, value in status.items():
                print(f"  {key}: {value}")
                
        tk.Button(btn_frame, text="Add Analysis Result", command=add_sample_analysis).pack(side='left', padx=5)
        tk.Button(btn_frame, text="Add Summary", command=add_sample_summary).pack(side='left', padx=5)
        tk.Button(btn_frame, text="Refresh Layouts", command=gui_manager.refresh_all_layouts).pack(side='left', padx=5)
        tk.Button(btn_frame, text="Show Status", command=show_status).pack(side='left', padx=5)
        
        # Instructions
        instructions = tk.Label(
            controls_frame,
            text="Demo: Ctrl+1/2/3 to focus panels • Ctrl+R to refresh • Resize window to see responsive behavior",
            font=('Arial', 9)
        )
        instructions.pack()
        
        # Show initial status
        status = gui_manager.get_comprehensive_status()
        print("Comprehensive GUI Improvements Demo")
        print("=" * 50)
        print("Status:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        print("\nFeatures Available:")
        print("• Comprehensive responsive layout system")
        print("• Multi-column text display with adaptive columns") 
        print("• Advanced content management with priority handling")
        print("• Tabbed results interface with search functionality")
        print("• Smart navigation with minimap and breadcrumbs")
        print("• High-performance scrolling and rendering")
        print("• Unified theming and keyboard shortcuts")
        
        root.mainloop()
        
    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Simple fallback demo
        tk.Label(root, text=f"Demo Error: {e}", wraplength=600).pack(pady=50)
        root.mainloop()


if __name__ == "__main__":
    demo_comprehensive_improvements()
