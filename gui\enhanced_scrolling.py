"""
Enhanced scrolling system for AI Analysis Program
Provides smooth scrolling, keyboard navigation, and content management
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Dict, Any
from utils.logging_setup import LoggerMixin


class EnhancedScrollableFrame(ttk.Frame, LoggerMixin):
    """Scrollable frame with enhanced features"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.setup_scrolling()
        self.scroll_speed = 3
        self.smooth_scroll_steps = 10
        self.content_callbacks = []
        
    def setup_scrolling(self):
        """Set up the scrolling infrastructure"""
        # Create canvas and scrollbars
        self.canvas = tk.Canvas(self, highlightthickness=0, bd=0)
        self.v_scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.h_scrollbar = ttk.Scrollbar(self, orient="horizontal", command=self.canvas.xview)
        
        # Create scrollable frame
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        # Configure canvas scrolling
        self.canvas.configure(
            yscrollcommand=self.v_scrollbar.set,
            xscrollcommand=self.h_scrollbar.set
        )
        
        # Pack scrollbars and canvas
        self.v_scrollbar.pack(side="right", fill="y")
        self.h_scrollbar.pack(side="bottom", fill="x")
        self.canvas.pack(side="left", fill="both", expand=True)
        
        # Configure scrollable frame
        self.canvas_frame = self.canvas.create_window(
            (0, 0), window=self.scrollable_frame, anchor="nw"
        )
        
        # Bind events
        self.bind_scroll_events()
        
    def bind_scroll_events(self):
        """Bind scrolling and navigation events"""
        # Mouse wheel scrolling
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)
        self.canvas.bind("<Button-4>", self.on_mousewheel)  # Linux
        self.canvas.bind("<Button-5>", self.on_mousewheel)  # Linux
        
        # Keyboard navigation
        self.canvas.bind("<Key>", self.on_key_press)
        self.canvas.focus_set()
        
        # Frame configuration
        self.scrollable_frame.bind("<Configure>", self.on_frame_configure)
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        
        # Content tracking
        self.bind("<FocusIn>", self.on_focus_in)
        
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling with smooth animation"""
        if event.delta:
            delta = -1 * (event.delta / 120)
        else:
            # Linux
            delta = -1 if event.num == 4 else 1
            
        self.smooth_scroll_vertical(delta * self.scroll_speed)
        
    def smooth_scroll_vertical(self, delta):
        """Implement smooth vertical scrolling"""
        try:
            current_top = self.canvas.canvasy(0)
            total_height = self.scrollable_frame.winfo_reqheight()
            canvas_height = self.canvas.winfo_height()
            
            if total_height <= canvas_height:
                return  # No scrolling needed
                
            # Calculate smooth scroll steps
            step_size = delta / self.smooth_scroll_steps
            
            def scroll_step(step):
                if step <= 0:
                    return
                    
                # Calculate new position
                new_pos = current_top + step_size
                new_pos = max(0, min(new_pos, total_height - canvas_height))
                
                # Update scroll position
                fraction = new_pos / (total_height - canvas_height) if total_height > canvas_height else 0
                self.canvas.yview_moveto(fraction)
                
                # Schedule next step
                self.after(10, lambda: scroll_step(step - 1))
                
            scroll_step(self.smooth_scroll_steps)
            
        except (tk.TclError, ZeroDivisionError):
            # Fallback to regular scrolling
            self.canvas.yview_scroll(int(delta), "units")
    
    def on_key_press(self, event):
        """Handle keyboard navigation"""
        key_actions = {
            'Up': lambda: self.smooth_scroll_vertical(-self.scroll_speed),
            'Down': lambda: self.smooth_scroll_vertical(self.scroll_speed),
            'Page_Up': lambda: self.scroll_page(-1),
            'Page_Down': lambda: self.scroll_page(1),
            'Home': lambda: self.scroll_to_top(),
            'End': lambda: self.scroll_to_bottom()
        }
        
        action = key_actions.get(event.keysym)
        if action:
            action()
            return "break"
    
    def scroll_page(self, direction):
        """Scroll by page increments"""
        canvas_height = self.canvas.winfo_height()
        scroll_amount = canvas_height * 0.8 * direction  # 80% of visible area
        self.smooth_scroll_vertical(scroll_amount)
    
    def scroll_to_top(self):
        """Scroll to the top of the content"""
        self.canvas.yview_moveto(0)
    
    def scroll_to_bottom(self):
        """Scroll to the bottom of the content"""
        self.canvas.yview_moveto(1)
    
    def scroll_to_widget(self, widget):
        """Scroll to make a specific widget visible"""
        try:
            # Get widget position relative to scrollable frame
            widget_y = widget.winfo_y()
            widget_height = widget.winfo_height()
            
            # Get current view
            canvas_height = self.canvas.winfo_height()
            current_top = self.canvas.canvasy(0)
            current_bottom = current_top + canvas_height
            
            # Check if widget is already visible
            if widget_y >= current_top and (widget_y + widget_height) <= current_bottom:
                return
                
            # Calculate target position (center the widget)
            target_top = max(0, widget_y - (canvas_height - widget_height) // 2)
            total_height = self.scrollable_frame.winfo_reqheight()
            
            if total_height > canvas_height:
                fraction = target_top / (total_height - canvas_height)
                self.canvas.yview_moveto(fraction)
                
        except tk.TclError:
            pass
    
    def on_frame_configure(self, event=None):
        """Handle scrollable frame configuration changes"""
        # Update scroll region
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
        # Notify content callbacks
        for callback in self.content_callbacks:
            try:
                callback("content_changed", self.get_content_info())
            except Exception as e:
                self.logger.error(f"Error in content callback: {e}")
    
    def on_canvas_configure(self, event=None):
        """Handle canvas configuration changes"""
        # Update scrollable frame width to match canvas
        canvas_width = self.canvas.winfo_width()
        self.canvas.itemconfig(self.canvas_frame, width=canvas_width)
    
    def on_focus_in(self, event=None):
        """Handle focus events for keyboard navigation"""
        self.canvas.focus_set()
    
    def get_frame(self):
        """Get the scrollable frame for adding content"""
        return self.scrollable_frame
    
    def get_content_info(self) -> Dict[str, Any]:
        """Get information about current content"""
        try:
            return {
                'content_height': self.scrollable_frame.winfo_reqheight(),
                'content_width': self.scrollable_frame.winfo_reqwidth(),
                'canvas_height': self.canvas.winfo_height(),
                'canvas_width': self.canvas.winfo_width(),
                'scroll_top': self.canvas.canvasy(0),
                'scroll_left': self.canvas.canvasx(0)
            }
        except tk.TclError:
            return {}
    
    def add_content_callback(self, callback: Callable):
        """Add a callback for content changes"""
        self.content_callbacks.append(callback)
    
    def set_scroll_speed(self, speed: int):
        """Set the scrolling speed"""
        self.scroll_speed = max(1, min(10, speed))


class ContentNavigator(LoggerMixin):
    """Provides navigation features for scrollable content"""
    
    def __init__(self, scrollable_frame: EnhancedScrollableFrame):
        self.scrollable_frame = scrollable_frame
        self.bookmarks = {}
        self.history = []
        self.current_position = 0
        
    def add_bookmark(self, name: str, widget: tk.Widget):
        """Add a bookmark to a specific widget"""
        self.bookmarks[name] = widget
        
    def goto_bookmark(self, name: str):
        """Navigate to a bookmark"""
        if name in self.bookmarks:
            widget = self.bookmarks[name]
            self.scrollable_frame.scroll_to_widget(widget)
            self.add_to_history(name)
    
    def add_to_history(self, location: str):
        """Add current location to navigation history"""
        if self.current_position < len(self.history) - 1:
            # Remove forward history if we're navigating from middle
            self.history = self.history[:self.current_position + 1]
            
        self.history.append(location)
        self.current_position = len(self.history) - 1
        
        # Limit history size
        if len(self.history) > 50:
            self.history = self.history[-50:]
            self.current_position = len(self.history) - 1
    
    def go_back(self):
        """Navigate back in history"""
        if self.current_position > 0:
            self.current_position -= 1
            location = self.history[self.current_position]
            self.goto_bookmark(location)
    
    def go_forward(self):
        """Navigate forward in history"""
        if self.current_position < len(self.history) - 1:
            self.current_position += 1
            location = self.history[self.current_position]
            self.goto_bookmark(location)


class SearchableScrollableFrame(EnhancedScrollableFrame):
    """Scrollable frame with search functionality"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.search_highlights = []
        self.current_search = ""
        self.search_index = 0
        
    def search_content(self, query: str, case_sensitive: bool = False) -> int:
        """Search for text in all text widgets"""
        self.clear_search_highlights()
        
        if not query:
            return 0
            
        self.current_search = query
        matches = 0
        
        # Find all text widgets in the scrollable frame
        text_widgets = self._find_text_widgets(self.scrollable_frame)
        
        for widget in text_widgets:
            matches += self._search_in_widget(widget, query, case_sensitive)
            
        self.search_index = 0
        return matches
    
    def _find_text_widgets(self, parent) -> list:
        """Recursively find all text widgets"""
        text_widgets = []
        
        for child in parent.winfo_children():
            if isinstance(child, (tk.Text, ttk.Entry)):
                text_widgets.append(child)
            else:
                text_widgets.extend(self._find_text_widgets(child))
                
        return text_widgets
    
    def _search_in_widget(self, widget: tk.Widget, query: str, case_sensitive: bool) -> int:
        """Search for text in a specific widget"""
        matches = 0
        
        try:
            if isinstance(widget, tk.Text):
                # Search in Text widget
                content = widget.get("1.0", tk.END)
                search_content = content if case_sensitive else content.lower()
                search_query = query if case_sensitive else query.lower()
                
                start = 0
                while True:
                    pos = search_content.find(search_query, start)
                    if pos == -1:
                        break
                        
                    # Convert position to line.col format
                    line_start = content.rfind('\n', 0, pos) + 1
                    line_num = content[:pos].count('\n') + 1
                    col_num = pos - line_start
                    
                    start_pos = f"{line_num}.{col_num}"
                    end_pos = f"{line_num}.{col_num + len(query)}"
                    
                    # Highlight the match
                    widget.tag_add(f"search_highlight_{matches}", start_pos, end_pos)
                    widget.tag_config(f"search_highlight_{matches}", 
                                    background="yellow", foreground="black")
                    
                    self.search_highlights.append((widget, f"search_highlight_{matches}"))
                    matches += 1
                    start = pos + 1
                    
        except tk.TclError:
            pass
            
        return matches
    
    def clear_search_highlights(self):
        """Clear all search highlights"""
        for widget, tag in self.search_highlights:
            try:
                widget.tag_delete(tag)
            except tk.TclError:
                pass
        self.search_highlights = []
    
    def next_search_result(self):
        """Navigate to next search result"""
        if not self.search_highlights:
            return
            
        if self.search_index >= len(self.search_highlights):
            self.search_index = 0
            
        widget, tag = self.search_highlights[self.search_index]
        
        # Scroll to the highlight
        if isinstance(widget, tk.Text):
            try:
                ranges = widget.tag_ranges(tag)
                if ranges:
                    widget.see(ranges[0])
                    self.scroll_to_widget(widget)
            except tk.TclError:
                pass
                
        self.search_index += 1
    
    def previous_search_result(self):
        """Navigate to previous search result"""
        if not self.search_highlights:
            return
            
        self.search_index -= 1
        if self.search_index < 0:
            self.search_index = len(self.search_highlights) - 1
            
        widget, tag = self.search_highlights[self.search_index]
        
        # Scroll to the highlight
        if isinstance(widget, tk.Text):
            try:
                ranges = widget.tag_ranges(tag)
                if ranges:
                    widget.see(ranges[0])
                    self.scroll_to_widget(widget)
            except tk.TclError:
                pass
