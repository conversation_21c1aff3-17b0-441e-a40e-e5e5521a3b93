"""
Topic list panel for AI Analysis Program
Manages a library of main topics similar to the sub-topic list
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Optional, List, Callable
import json
import os
from pathlib import Path
from datetime import datetime

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import validate_topic_structure, show_error, show_info, sanitize_filename
from gui.widgets import CollapsibleFrame, ToolTip

class TopicListPanel(LoggerMixin):
    """Panel for managing a library of main topics"""

    def __init__(self, parent: tk.Widget, config: Config, theme=None, on_topic_selected: Optional[Callable] = None):
        self.parent = parent
        self.config = config
        self.theme = theme
        self.on_topic_selected = on_topic_selected  # Callback when a topic is selected

        # Topic library storage
        self.topics_dir = Path("data/topics")
        self.topics_dir.mkdir(parents=True, exist_ok=True)
        self.topic_library = []  # List of topic data

        self.setup_ui()
        self.load_topic_library()

    def setup_ui(self):
        """Set up the topic list user interface"""
        # Topic Library section
        library_frame = CollapsibleFrame(
            self.parent,
            title="📚 Topic Library",
            collapsed=False,
            theme=self.theme
        )
        library_frame.pack(fill=tk.BOTH, expand=True, pady=(0, self.theme.spacing.pad_md if self.theme else 10))

        # Search/Filter section
        search_frame = ttk.Frame(library_frame.content_frame)
        search_frame.pack(fill=tk.X, pady=(0, 5))

        search_label = ttk.Label(search_frame, text="🔍 Search:")
        search_label.pack(side=tk.LEFT)

        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        self.search_var.trace('w', self.on_search_changed)

        if self.theme:
            ToolTip(self.search_entry, "Search topics by title or description")

        # Topic list (TreeView)
        tree_frame = ttk.Frame(library_frame.content_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # TreeView with columns for Title, Description, Category, and Date
        self.topics_treeview = ttk.Treeview(
            tree_frame,
            columns=("Description", "Category", "Date", "SubTopics"),
            show="tree headings"
        )
        self.topics_treeview.heading("#0", text="Title")
        self.topics_treeview.heading("Description", text="Description")
        self.topics_treeview.heading("Category", text="Category")
        self.topics_treeview.heading("Date", text="Modified")
        self.topics_treeview.heading("SubTopics", text="Sub-topics")

        # Column widths
        self.topics_treeview.column("#0", width=200, minwidth=150)
        self.topics_treeview.column("Description", width=300, minwidth=200)
        self.topics_treeview.column("Category", width=100, minwidth=80)
        self.topics_treeview.column("Date", width=100, minwidth=80)
        self.topics_treeview.column("SubTopics", width=80, minwidth=60)

        self.topics_treeview.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar for TreeView
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.topics_treeview.yview)
        self.topics_treeview.configure(yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind double-click to select topic
        self.topics_treeview.bind("<Double-1>", self.on_topic_double_click)

        # Context menu for right-click
        self.setup_context_menu()

        # Toolbar for topic operations
        toolbar_frame = ttk.Frame(library_frame.content_frame)
        toolbar_frame.pack(fill=tk.X, pady=(5, 0))

        # Left side buttons
        left_buttons = ttk.Frame(toolbar_frame)
        left_buttons.pack(side=tk.LEFT)

        add_btn = ttk.Button(left_buttons, text="➕ Add Topic", command=self.add_topic_dialog)
        add_btn.pack(side=tk.LEFT)
        if self.theme:
            ToolTip(add_btn, "Add a new topic to the library")

        edit_btn = ttk.Button(left_buttons, text="✏️ Edit", command=self.edit_selected_topic)
        edit_btn.pack(side=tk.LEFT, padx=(5, 0))
        if self.theme:
            ToolTip(edit_btn, "Edit the selected topic")

        remove_btn = ttk.Button(left_buttons, text="🗑️ Remove", command=self.remove_selected_topic)
        remove_btn.pack(side=tk.LEFT, padx=(5, 0))
        if self.theme:
            ToolTip(remove_btn, "Remove the selected topic from library")

        duplicate_btn = ttk.Button(left_buttons, text="📋 Duplicate", command=self.duplicate_selected_topic)
        duplicate_btn.pack(side=tk.LEFT, padx=(5, 0))
        if self.theme:
            ToolTip(duplicate_btn, "Create a copy of the selected topic")

        # Right side buttons
        right_buttons = ttk.Frame(toolbar_frame)
        right_buttons.pack(side=tk.RIGHT)

        use_btn = ttk.Button(right_buttons, text="📝 Use Topic", command=self.use_selected_topic)
        use_btn.pack(side=tk.RIGHT)
        if self.theme:
            ToolTip(use_btn, "Load the selected topic into the input panel")

        # File operations section - collapsible
        file_frame = CollapsibleFrame(
            self.parent,
            title="📁 Library Management",
            collapsed=True,  # Start collapsed
            theme=self.theme
        )
        file_frame.pack(fill=tk.X, pady=(0, self.theme.spacing.pad_md if self.theme else 10))

        file_buttons_frame = ttk.Frame(file_frame.content_frame)
        file_buttons_frame.pack(fill=tk.X)

        # Import/Export buttons
        import_btn = ttk.Button(
            file_buttons_frame,
            text="📥 Import Topics",
            command=self.import_topics
        )
        import_btn.pack(side=tk.LEFT)
        if self.theme:
            ToolTip(import_btn, "Import topics from JSON file")

        export_btn = ttk.Button(
            file_buttons_frame,
            text="📤 Export Topics",
            command=self.export_topics
        )
        export_btn.pack(side=tk.LEFT, padx=(5, 0))
        if self.theme:
            ToolTip(export_btn, "Export selected topics to JSON file")

        refresh_btn = ttk.Button(
            file_buttons_frame,
            text="🔄 Refresh",
            command=self.refresh_topic_library
        )
        refresh_btn.pack(side=tk.RIGHT)
        if self.theme:
            ToolTip(refresh_btn, "Refresh the topic library")

    def setup_context_menu(self):
        """Set up right-click context menu"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="📝 Use Topic", command=self.use_selected_topic)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="✏️ Edit", command=self.edit_selected_topic)
        self.context_menu.add_command(label="📋 Duplicate", command=self.duplicate_selected_topic)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ Remove", command=self.remove_selected_topic)

        self.topics_treeview.bind("<Button-3>", self.show_context_menu)  # Right-click

    def show_context_menu(self, event):
        """Show context menu on right-click"""
        # Select the item under cursor
        item = self.topics_treeview.identify_row(event.y)
        if item:
            self.topics_treeview.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def on_search_changed(self, *args):
        """Handle search text changes"""
        search_text = self.search_var.get().lower()
        self.filter_topics(search_text)

    def filter_topics(self, search_text: str):
        """Filter topics based on search text"""
        # Clear current items
        for item in self.topics_treeview.get_children():
            self.topics_treeview.delete(item)

        # Re-populate with filtered items
        for topic_data in self.topic_library:
            title = topic_data.get('title', '').lower()
            description = topic_data.get('description', '').lower()
            category = topic_data.get('category', '').lower()

            if (not search_text or
                search_text in title or
                search_text in description or
                search_text in category):
                self.add_topic_to_tree(topic_data)

    def add_topic_to_tree(self, topic_data: Dict[str, Any]):
        """Add a topic to the TreeView"""
        title = topic_data.get('title', 'Untitled')
        description = topic_data.get('description', '')[:100] + ('...' if len(topic_data.get('description', '')) > 100 else '')
        category = topic_data.get('category', 'General')
        modified_date = topic_data.get('modified_date', '')

        # Count sub-topics
        sub_topics_count = 0
        if 'sub_topics_tree' in topic_data:
            sub_topics_count = self.count_subtopics(topic_data['sub_topics_tree'])
        elif 'sub_topics' in topic_data:
            sub_topics_count = len(topic_data['sub_topics'])

        # Format date
        if modified_date:
            try:
                if isinstance(modified_date, str):
                    date_obj = datetime.fromisoformat(modified_date.replace('Z', '+00:00'))
                else:
                    date_obj = datetime.fromtimestamp(modified_date)
                formatted_date = date_obj.strftime('%m/%d/%y')
            except:
                formatted_date = str(modified_date)[:10]
        else:
            formatted_date = ''

        item_id = self.topics_treeview.insert(
            '', 'end',
            text=title,
            values=(description, category, formatted_date, str(sub_topics_count)),
            tags=(topic_data.get('id', ''),)
        )

        return item_id

    def count_subtopics(self, subtopics_tree: List[Dict]) -> int:
        """Recursively count all sub-topics in a tree structure"""
        count = len(subtopics_tree)
        for subtopic in subtopics_tree:
            if 'sub_topics' in subtopic:
                count += self.count_subtopics(subtopic['sub_topics'])
        return count

    def on_topic_double_click(self, event):
        """Handle double-click on topic"""
        self.use_selected_topic()

    def get_selected_topic_data(self) -> Optional[Dict[str, Any]]:
        """Get the data for the currently selected topic"""
        selected = self.topics_treeview.selection()
        if not selected:
            return None

        item = selected[0]
        title = self.topics_treeview.item(item, 'text')

        # Find the topic data by title
        for topic_data in self.topic_library:
            if topic_data.get('title') == title:
                return topic_data

        return None

    def add_topic_dialog(self):
        """Show dialog to add a new topic"""
        dialog = TopicDialog(self.parent, "Add Topic to Library")
        if dialog.result:
            topic_data = dialog.result.copy()
            topic_data['id'] = self.generate_topic_id()
            topic_data['created_date'] = datetime.now().isoformat()
            topic_data['modified_date'] = datetime.now().isoformat()

            # Save to file
            if self.save_topic_to_file(topic_data):
                self.topic_library.append(topic_data)
                self.add_topic_to_tree(topic_data)
                show_info("Topic Added", f"Topic '{topic_data['title']}' added to library.")

    def edit_selected_topic(self):
        """Edit the selected topic"""
        topic_data = self.get_selected_topic_data()
        if not topic_data:
            show_error("No Selection", "Please select a topic to edit.")
            return

        dialog = TopicDialog(self.parent, "Edit Topic", topic_data)
        if dialog.result:
            # Update topic data
            topic_data.update(dialog.result)
            topic_data['modified_date'] = datetime.now().isoformat()

            # Save to file
            if self.save_topic_to_file(topic_data):
                self.refresh_topic_library()
                show_info("Topic Updated", f"Topic '{topic_data['title']}' updated.")

    def remove_selected_topic(self):
        """Remove the selected topic from library"""
        topic_data = self.get_selected_topic_data()
        if not topic_data:
            show_error("No Selection", "Please select a topic to remove.")
            return

        if messagebox.askyesno("Confirm Removal",
                              f"Are you sure you want to remove '{topic_data['title']}' from the library?"):
            # Remove file
            topic_file = self.topics_dir / f"{sanitize_filename(topic_data['title'])}_{topic_data.get('id', '')[:8]}.json"
            if topic_file.exists():
                topic_file.unlink()

            # Remove from library
            self.topic_library.remove(topic_data)

            # Remove from tree
            selected = self.topics_treeview.selection()
            if selected:
                self.topics_treeview.delete(selected[0])

            show_info("Topic Removed", f"Topic '{topic_data['title']}' removed from library.")

    def duplicate_selected_topic(self):
        """Create a duplicate of the selected topic"""
        topic_data = self.get_selected_topic_data()
        if not topic_data:
            show_error("No Selection", "Please select a topic to duplicate.")
            return

        # Create duplicate with new ID and title
        duplicate_data = topic_data.copy()
        duplicate_data['id'] = self.generate_topic_id()
        duplicate_data['title'] = f"{topic_data['title']} (Copy)"
        duplicate_data['created_date'] = datetime.now().isoformat()
        duplicate_data['modified_date'] = datetime.now().isoformat()

        # Save to file
        if self.save_topic_to_file(duplicate_data):
            self.topic_library.append(duplicate_data)
            self.add_topic_to_tree(duplicate_data)
            show_info("Topic Duplicated", f"Topic duplicated as '{duplicate_data['title']}'.")

    def use_selected_topic(self):
        """Load the selected topic into the input panel"""
        topic_data = self.get_selected_topic_data()
        if not topic_data:
            show_error("No Selection", "Please select a topic to use.")
            return

        if self.on_topic_selected:
            self.on_topic_selected(topic_data)
        else:
            show_info("Topic Selected", f"Topic '{topic_data['title']}' selected.")

    def generate_topic_id(self) -> str:
        """Generate a unique ID for a topic"""
        import uuid
        return str(uuid.uuid4())

    def save_topic_to_file(self, topic_data: Dict[str, Any]) -> bool:
        """Save a topic to a JSON file"""
        try:
            filename = f"{sanitize_filename(topic_data['title'])}_{topic_data.get('id', '')[:8]}.json"
            file_path = self.topics_dir / filename

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(topic_data, f, indent=2, ensure_ascii=False)

            return True
        except Exception as e:
            self.logger.error(f"Failed to save topic: {e}")
            show_error("Save Error", f"Failed to save topic: {str(e)}")
            return False

    def load_topic_library(self):
        """Load all topics from the topics directory"""
        self.topic_library = []

        try:
            for file_path in self.topics_dir.glob("*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        topic_data = json.load(f)

                    if validate_topic_structure(topic_data):
                        self.topic_library.append(topic_data)
                    else:
                        self.logger.warning(f"Invalid topic structure in {file_path}")

                except Exception as e:
                    self.logger.error(f"Failed to load topic from {file_path}: {e}")

            # Sort by modified date (newest first)
            self.topic_library.sort(key=lambda x: x.get('modified_date', ''), reverse=True)

            # Populate TreeView
            self.populate_tree()

        except Exception as e:
            self.logger.error(f"Failed to load topic library: {e}")

    def populate_tree(self):
        """Populate the TreeView with topics"""
        # Clear existing items
        for item in self.topics_treeview.get_children():
            self.topics_treeview.delete(item)

        # Add all topics
        for topic_data in self.topic_library:
            self.add_topic_to_tree(topic_data)

    def refresh_topic_library(self):
        """Refresh the topic library from disk"""
        self.load_topic_library()
        show_info("Library Refreshed", f"Loaded {len(self.topic_library)} topics from library.")

    def import_topics(self):
        """Import topics from a JSON file"""
        filename = filedialog.askopenfilename(
            title="Import Topics",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Handle both single topic and list of topics
                topics_to_import = data if isinstance(data, list) else [data]
                imported_count = 0

                for topic_data in topics_to_import:
                    if validate_topic_structure(topic_data):
                        # Generate new ID and timestamps
                        topic_data['id'] = self.generate_topic_id()
                        topic_data['created_date'] = datetime.now().isoformat()
                        topic_data['modified_date'] = datetime.now().isoformat()

                        # Check for duplicate titles
                        original_title = topic_data['title']
                        counter = 1
                        while any(t['title'] == topic_data['title'] for t in self.topic_library):
                            topic_data['title'] = f"{original_title} ({counter})"
                            counter += 1

                        # Save to file
                        if self.save_topic_to_file(topic_data):
                            self.topic_library.append(topic_data)
                            imported_count += 1

                self.populate_tree()
                show_info("Import Successful", f"Imported {imported_count} topics.")

            except Exception as e:
                show_error("Import Error", f"Failed to import topics: {str(e)}")

    def export_topics(self):
        """Export selected topics to a JSON file"""
        selected = self.topics_treeview.selection()
        if not selected:
            show_error("No Selection", "Please select topics to export.")
            return

        # Get selected topic data
        topics_to_export = []
        for item in selected:
            title = self.topics_treeview.item(item, 'text')
            for topic_data in self.topic_library:
                if topic_data.get('title') == title:
                    topics_to_export.append(topic_data)
                    break

        if not topics_to_export:
            show_error("Export Error", "No valid topics selected for export.")
            return

        filename = filedialog.asksaveasfilename(
            title="Export Topics",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                export_data = topics_to_export if len(topics_to_export) > 1 else topics_to_export[0]
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                show_info("Export Successful", f"Exported {len(topics_to_export)} topics to {filename}")

            except Exception as e:
                show_error("Export Error", f"Failed to export topics: {str(e)}")

    def get_topic_count(self) -> int:
        """Get the number of topics in the library"""
        return len(self.topic_library)

    def get_all_topics(self) -> List[Dict[str, Any]]:
        """Get all topics in the library"""
        return self.topic_library.copy()

    def clear_search(self):
        """Clear the search filter"""
        self.search_var.set('')

    def cleanup(self):
        """Cleanup resources"""
        pass


class TopicDialog:
    """Dialog for adding/editing topics in the library"""

    def __init__(self, parent: tk.Widget, title: str, initial_data: Optional[Dict[str, Any]] = None):
        self.result = None

        # Ensure parent is a tk.Tk or tk.Toplevel for transient
        master = parent.winfo_toplevel() if hasattr(parent, 'winfo_toplevel') else parent
        self.dialog = tk.Toplevel(master)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(True, True)

        # Only set transient if master is a Toplevel or Tk
        if isinstance(master, (tk.Tk, tk.Toplevel)):
            self.dialog.transient(master)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")

        self.setup_ui(initial_data)

        # Wait for dialog to close
        self.dialog.wait_window()

    def setup_ui(self, initial_data: Optional[Dict[str, Any]]):
        """Set up dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title field
        ttk.Label(main_frame, text="Title:").pack(anchor=tk.W)
        self.title_var = tk.StringVar()
        title_entry = ttk.Entry(main_frame, textvariable=self.title_var)
        title_entry.pack(fill=tk.X, pady=(2, 10))
        title_entry.focus()

        # Category field
        ttk.Label(main_frame, text="Category:").pack(anchor=tk.W)
        self.category_var = tk.StringVar()
        category_entry = ttk.Entry(main_frame, textvariable=self.category_var)
        category_entry.pack(fill=tk.X, pady=(2, 10))

        # Description field
        ttk.Label(main_frame, text="Description:").pack(anchor=tk.W)
        desc_frame = ttk.Frame(main_frame)
        desc_frame.pack(fill=tk.BOTH, expand=True, pady=(2, 10))

        self.desc_text = tk.Text(desc_frame, height=8, wrap=tk.WORD)
        desc_scrollbar = ttk.Scrollbar(desc_frame, orient="vertical", command=self.desc_text.yview)
        self.desc_text.configure(yscrollcommand=desc_scrollbar.set)

        self.desc_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        desc_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.RIGHT)

        # Load initial data
        if initial_data:
            self.title_var.set(initial_data.get('title', ''))
            self.category_var.set(initial_data.get('category', 'General'))
            self.desc_text.insert(1.0, initial_data.get('description', ''))
        else:
            self.category_var.set('General')

        # Bind Enter key to OK (Ctrl+Enter since we have a multi-line text field)
        self.dialog.bind('<Control-Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())

    def ok_clicked(self):
        """Handle OK button click"""
        title = self.title_var.get().strip()
        if not title:
            messagebox.showerror("Invalid Input", "Please enter a title.", parent=self.dialog)
            return

        category = self.category_var.get().strip() or 'General'
        description = self.desc_text.get(1.0, tk.END).strip()

        self.result = {
            'title': title,
            'category': category,
            'description': description
        }

        self.dialog.destroy()

    def cancel_clicked(self):
        """Handle Cancel button click"""
        self.dialog.destroy()